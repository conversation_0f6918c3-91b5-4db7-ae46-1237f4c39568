<?php

namespace App\Jobs;

use App\Notifications\SubscriptionDemandReport;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\Query\Builder;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\Storage;

class SendSubscriptionDemandReport implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function handle()
    {
        $recipient_emails = config('mail.notifications.subscription_demand_report');

        if (empty($recipient_emails)) {
            return;
        }

        $filepath = $this->generateReport();

        if (empty($filepath)) {
            return;
        }

        foreach ($recipient_emails as $recipient) {
            Notification::route('mail', $recipient)
                ->notify(new SubscriptionDemandReport($filepath));
        }
    }

    private function generateReport(): ?string
    {
        // Create a temporary memory file
        $temp_file = fopen('php://temp', 'r+');

        fputcsv($temp_file, [
            'pack_deadline',
            'barcode',
            'product_id',
            'sku',
            'title',
            'unit_of_issue',
            'qty',
            'weight_lbs_per_unit',
            'total_weight_lbs_per_unit',
            'packing_group',
            'accounting_class_id',
        ]);

        try {
            $this->baseQuery()
                ->chunk(250, function ($chunk) use ($temp_file) {
                    foreach ($chunk as $row) {
                        fputcsv($temp_file, (array) $row);
                    }
                });
        } catch (\Exception $e) {
            fclose($temp_file);
            return null;
        }

        // Rewind the temp file to the beginning of the file and get the content
        rewind($temp_file);
        $file_contents = stream_get_contents($temp_file);

        // Close the temp file
        fclose($temp_file);

        $timestamp = now()->format('Y_m_d_h_i');
        $filepath = config('filesystems.file_upload_prefix') . "/reports/subscription_demand_report_{$timestamp}.csv";

        Storage::disk('s3')->put($filepath, $file_contents);

        return $filepath;
    }

    protected function baseQuery(): Builder
    {
        return DB::table('subscription_demand_by_pack_deadline')
            ->select([
                'subscription_demand_by_pack_deadline.pack_deadline_at',
                'products.barcode',
                'subscription_demand_by_pack_deadline.product_id',
                'products.sku',
                'products.title',
                'products.unit_of_issue',
                'subscription_demand_by_pack_deadline.total_product_qty AS qty',
                'products.weight',
                DB::raw('total_product_qty * products.weight AS total_weight_lbs_per_unit'),
                DB::raw('
                        CASE
                            WHEN products.inventory_type = 1 THEN "Frozen"
                            WHEN products.inventory_type = 2 THEN "Dry"
                            WHEN products.inventory_type = 3 THEN "Fresh"
                            WHEN products.inventory_type = 4 THEN "Seasonal"
                            WHEN products.inventory_type = 5 THEN "Merchandise"
                            WHEN products.inventory_type = 6 THEN "Digital"
                            ELSE "Unknown"
                        END AS product_packing_group
                    '),
                'products.accounting_class',
            ])
            ->join('products', 'products.id', '=', 'subscription_demand_by_pack_deadline.product_id')
            ->orderBy('subscription_demand_by_pack_deadline.pack_deadline_at')
            ->orderBy('subscription_demand_by_pack_deadline.product_id');
    }
}
