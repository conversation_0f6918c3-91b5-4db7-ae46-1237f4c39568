<?php

namespace App\Jobs;

use App\Integrations\Drip\DripIntegration;
use App\Traits\TenantContextMiddleware;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class RecordBatchOfEventsInDrip implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, TenantContextMiddleware;

    public function __construct(
        public array $events
    ) {}

    public function handle(): void
    {
        if (empty($this->events)) return;

        $drip = DripIntegration::getInstance();

        if (is_null($drip)) return;

        $drip->recordBatchOfEvents($this->events);
    }

    public function tags(): array
    {
        return ['subscriptions', 'drip'];
    }
}
