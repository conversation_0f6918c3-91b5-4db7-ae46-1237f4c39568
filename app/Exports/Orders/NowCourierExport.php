<?php

namespace App\Exports\Orders;

use App\Models\Order;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;

class NowCourierExport
{
    public function __construct(
        public Builder $orderQuery
    ) {}

    public function export()
    {
        $builder = $this->orderQuery;

        $date = Carbon::tomorrow()->format('Y-m-d');

        return response()->stream(function () use ($builder, $date) {
            $export = fopen('php://output', 'w');
            ob_end_clean();
            fputcsv($export, [
                'Customer ID',
                'Name',
                'Address',
                'City',
                'State',
                'Zip',
                'Plus 4',
                'Stop Type',
                'Sequence',
                'Reference 1',
                'Reference 2',
                'Stop Notes',
                'Short Remarks',
                'E-mail Address',
                'Phone',
                'Pieces',
                'Weight',
                'Postdate',
                'Stop Time',
            ]);

            $builder
                ->chunk(500, function ($resourceChunk) use ($export, $date) {
                    foreach ($resourceChunk as $resource) {
                        /** @var Order $resource */

                        $street = $resource->shipping_street_2 ? $resource->shipping_street . ', ' . $resource->shipping_street_2 : $resource->shipping_street;
                        fputcsv($export, [
                            '59057',
                            $resource->customer_first_name . ' ' . $resource->customer_last_name,
                            $street,
                            $resource->shipping_city,
                            $resource->shipping_state,
                            $resource->shipping_zip,
                            null,
                            2, // Stop Type
                            50, // Sequence
                            'Order ' . $resource->id,
                            'Customer ' . $resource->customer_id,
                            $resource->invoice_notes,
                            $resource->invoice_notes,
                            $resource->customer_email,
                            $resource->customer_phone,
                            $resource->containers + $resource->containers_2,
                            1,
                            $date,
                            $date,
                        ]);
                    }
                });

            fclose($export);
        }, 200, [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="now_courier_export.csv"',
        ]);
    }
}
