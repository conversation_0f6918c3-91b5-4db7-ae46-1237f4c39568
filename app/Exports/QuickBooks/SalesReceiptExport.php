<?php

namespace App\Exports\QuickBooks;

use App\Models\Coupon;
use App\Models\Order;
use App\Models\OrderFee;
use App\Models\OrderItem;
use App\Support\Enums\Channel;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class SalesReceiptExport implements FromQuery, WithHeadings, WithMapping
{
    use Exportable;

    public function __construct(
        public Builder $orderQuery
    ){}

    public function query(): Builder
    {
        return $this->orderQuery
            ->with(['customer', 'fees', 'items.product', 'discounts', 'payments', 'paymentMethod', 'pickup']);
    }

    public function headings(): array
    {
        return array_merge($this->orderHeadings(), $this->productHeadings());
    }

    private function orderHeadings(): array
    {
        return [
            'Sales Receipt No',
            'Customer',
            'Company',
            'Sales Receipt Date',
            'Shipping Date',
            'Tracking No',
            'Deposit To',
            'Payment Method',
            'Reference No',
            'Billing Address Line 1',
            'Billing Address Line 2',
            'Billing Address City',
            'Billing Address Postal Code',
            'Billing Address Country',
            'Billing Address State',
            'Shipping Address Line 1',
            'Shipping Address Line 2',
            'Shipping Address City',
            'Shipping Address Postal Code',
            'Shipping Address Country',
            'Shipping Address State',
            'Memo',
            'Message displayed on sales receipt',
            'Email',
            'Customer First Name',
            'Customer Last Name',
            'Print Status',
            'Email Status',
            'Shipping',
            'Sales Tax Code',
            'Sales Tax Amount',
            'Discount Amount',
            'Discount Percent',
            'Subscription Savings',
            'Apply Tax after Discount',
            'Location',
        ];
    }

    private function productHeadings(): array
    {
        return [
            'Product/Service',
            'Product/Service SKU',
            'Product/Service Description',
            'Product/Service Quantity',
            'Product/Service Packages',
            'Product/Service Rate',
            'Product/Service Amount',
            'Product/Service Taxable',
            'Product/Service Class',
            'Product/Service Class 2'
        ];
    }

    public function map($row): array
    {
        $order_columns = $this->orderColumns($row);

        return collect()
            ->push(...$this->orderItems($row, $order_columns))
            ->push(...$this->discounts($row, $order_columns))
            ->push(...$this->fees($row, $order_columns))
            ->push($this->appliedCredit($row, $order_columns))
            ->push($this->deliveryFee($row, $order_columns))
            ->filter()
            ->toArray();
    }

    private function orderColumns(Order $order): array
    {
        return array_combine($this->orderHeadings(), [
            substr('GC-' . $order->id, 0, 21), // Sales Receipt No
            $order->customerAccountingId(), // Customer
            $order->companyName(), // Company
            $order->payment_date?->format('m/d/Y'), // Sales Receipt Date
            $order->pickup_date?->format('m/d/Y'), // Shipping Date.
            $order->formattedTrackingId(), // Tracking No
            'Undeposited Funds', // Deposit to
            $order->paymentMethod?->title, // Payment method
            $order->paymentReference(), // Reference No
            $order->billing_street, // Billing Address Line 1
            $order->billing_street_2, // Billing Address Line 2
            $order->billing_city, // Billing City
            $order->billing_zip, // Billing Address Postal Code
            $order->billing_country ?? 'USA', // Billing Address Country
            $order->billing_state, // Billing Address State
            $order->shipping_street, // Shipping Address Line 1
            $order->shipping_street_2, // Shipping Address Line 2
            $order->shipping_city, // Shipping Address City
            $order->shipping_zip, // Shipping Address Postal Code
            $order->shipping_country ?? 'USA', // Shipping Address Country
            $order->shipping_state, // Shipping Address State
            $order->formattedPackingNotes(), // Memo
            $order->formattedInvoiceNotes(), // Message displayed on sales receipt
            $order->customer_email, // Email
            $order->customer_first_name,
            $order->customer_last_name,
            'FALSE', // Print status
            null, // Email status
            0.00, // Shipping
            $order->pickup?->state, // Sales Tax Code
            money($order->tax, ''), // Sales Tax Amount
            money($order->order_discount, ''), // Discount Amount
            null, // Discount Percent
            money($order->subscription_savings ?? 0, ''), // Subscription Savings
            false, // Apply Tax after Discount
            addslashes($order->pickup?->title) ?: null, // Location
        ]);
    }

    private function orderItems(Order $order, array $order_columns): Collection
    {
        return $order->items->map(function (OrderItem $item) use ($order, $order_columns) {
            $order_columns['Sales Tax Amount'] = $item->taxAmountForRate($order->taxRate());

            $product_columns = array_combine($this->productHeadings(), [
                $item->product->title, // Product/Service
                $item->product->getSku(), // Product/Service SKU
                addslashes($item->title), // Product/Service Description
                $item->isPricedByWeight() ? $item->weight : $item->fulfilledQuantity(), // Product/Service Quantity'
                $item->fulfilledQuantity(), // Product/Service Packages
                money($item->unit_price, ''), // Product/Service Rate
                money($item->subtotal, ''), // Product/Service Amount
                $item->product->taxable ? 'TRUE' : 'FALSE',  // Product/Service Taxable
                Channel::get($order->type_id), // Product/Service Class
                $item->product->accounting_class, // Product/Service Class 2
            ]);

            return array_merge($order_columns, $product_columns);
        });
    }

    private function discounts(Order $order, array $order_columns): Collection
    {
        return $order->discounts->map(function (Coupon $coupon) use ($order, $order_columns) {
            $order_columns['Sales Tax Amount'] = 0;

            $product_columns = array_combine($this->productHeadings(), [
                trim($coupon->code), // Product/Service
                trim($coupon->code), // Product/Service SKU
                addslashes($coupon->description . ' (' . $coupon->code . ')'), // Product/Service Description
                1, // Product/Service Quantity
                1, // // Product/Service Packages
                money(-$coupon->getRelationValue('pivot')->savings, ''), // Product/Service Rate
                money(-$coupon->getRelationValue('pivot')->savings, ''), // Product/Service Amount
                'FALSE',  // Product/Service Taxable
                Channel::get($order->type_id), // Product/Service Class
                null, // Product/Service Class 2
            ]);

            return array_merge($order_columns, $product_columns);
        });
    }

    private function fees(Order $order, array $order_columns): Collection
    {
        return $order->fees->map(function (OrderFee $fee) use ($order, $order_columns) {
            $product_columns = array_combine($this->productHeadings(), [
                $fee->title, // Product/Service
                Str::slug($fee->title), // Product/Service SKU
                addslashes($fee->title), // Product/Service Description
                $fee->qty, // Product/Service Quantity
                $fee->qty, // // Product/Service Packages
                money($fee->amount, ''), // Product/Service Rate
                money($fee->subtotal, ''), // Product/Service Amount
                $fee->taxable ? 'TRUE' : 'FALSE',  // Product/Service Taxable
                Channel::get($order->type_id), // Product/Service Class
                null, // Product/Service Class 2
            ]);

            return array_merge($order_columns, $product_columns);
        });
    }

    private function appliedCredit(Order $order, array $order_columns): array
    {
        $order_columns['Sales Tax Amount'] = 0;

        $product_columns = array_combine($this->productHeadings(), [
            'Credit Applied', // Product/Service
            'credit-applied', // Product/Service SKU
            'Credit Applied', // Product/Service Description
            1, // Product/Service Quantity
            1, // Product/Service Packages
            $order->credit_applied ? money(-$order->credit_applied, '') : '0', // Product/Service Rate
            $order->credit_applied ? money(-$order->credit_applied, '') : '0', // Product/Service Amount
            'FALSE',  // Product/Service Taxable
            Channel::get($order->type_id), // Product/Service Class
            null, // Product/Service Class 2
        ]);

        return array_merge($order_columns, $product_columns);
    }

    private function deliveryFee(Order $order, array $order_columns): array
    {
        $order_columns['Sales Tax Amount'] = 0;

        $product_columns = array_combine($this->productHeadings(), [
            'Delivery Fee', // Product/Service
            'delivery-fee', // Product/Service SKU
            'Delivery Fee', // Product/Service Description
            1, // Product/Service Quantity
            1, // Product/Service Packages
            money($order->delivery_fee, ''), // Product/Service Rate
            money($order->delivery_fee, ''), // Product/Service Amount
            'FALSE',  // Product/Service Taxable
            Channel::get($order->type_id), // Product/Service Class
            null, // Product/Service Class 2
        ]);

        return array_merge($order_columns, $product_columns);
    }
}
