<?php

namespace App\Mail;

use App\Models\Order;
use App\Models\Template;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Symfony\Component\Mime\Email;

class TestEmail extends Mailable
{
    use Queueable;

    public $content;
    public $text;
    public $styles;
    public $template;
    public $order;
    public $subject;

    public function __construct(Order $order, Template $template)
    {
        $this->template = $template;
        $this->order = $order;
    }

    /**
     * Build the message.
     */
    public function build(): static
    {
        $this->styles = $this->template->settings;
        $mergedContent = $this->template->mergeWithOrder($this->order, 'preview');
        $this->content = $mergedContent->getHtml();
        $this->text = $mergedContent->getText();
        $this->subject = $this->template->subject;

        $this->from($this->template->getFromEmail(), $this->template->getFromName());
        $this->replyTo($this->template->getReplyToEmail(), $this->template->getFromName());

        return $this->withSymfonyMessage(function (Email $m) {
            $headers = $m->getHeaders();
            $headers->addTextHeader('X-Mailgun-Tag', 'Test Email');
        })->view('emails.transactional-html')->text('emails.transactional-text');
    }
}
