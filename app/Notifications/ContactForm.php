<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;

class ContactForm extends TenantAwareNotification implements ShouldQueue
{
    use Queueable;

    public function __construct(
        public string $topic,
        public string $email,
        public string $full_name,
        public string $body
    ) {}

    public function via(): array
    {
        return ['mail'];
    }

    public function toMail(): MailMessage
    {
        return (new MailMessage)
            ->view('emails.notifications.support', [
                'content' => $this->body,
                'name' => $this->full_name,
                'email' => $this->email,
            ])
            ->action('Reply Now', 'mailto:' . $this->email . '?subject=' . rawurlencode('Re: ' . $this->topic))
            ->subject($this->topic)
            ->replyTo($this->email, $this->full_name)
            ->from(config('mail.from.address'), config('mail.from.name'));
    }

    public function tags(): array
    {
        return ['notification'];
    }
}
