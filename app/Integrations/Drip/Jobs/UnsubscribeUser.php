<?php

namespace App\Integrations\Drip\Jobs;

use App\Integrations\Drip\DripIntegration;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class UnsubscribeUser implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $userId;

    public $tags;

    public function __construct(int $userId, array $tags = [])
    {
        $this->userId = $userId;
        $this->tags = $tags;
    }

    public function handle()
    {
        $drip = DripIntegration::getInstance();

        if (is_null($drip)) return;

        $user = User::findOrFail($this->userId);

        $drip->updateSubscriber($user);
    }

    public function tags()
    {
        return ['integration', 'drip'];
    }
}
