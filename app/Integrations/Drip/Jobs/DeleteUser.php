<?php

namespace App\Integrations\Drip\Jobs;

use App\Integrations\Drip\DripIntegration;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class DeleteUser implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(public string $userEmail, public array $tags = [])
    {}

    public function handle(): void
    {
        $drip = DripIntegration::getInstance();

        if (is_null($drip)) return;

        $drip->recordEvent($this->userEmail, 'Deleted account');
    }

    public function tags()
    {
        return ['integration', 'drip'];
    }
}
