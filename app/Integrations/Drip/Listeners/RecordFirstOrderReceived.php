<?php

namespace App\Integrations\Drip\Listeners;

use App\Events\Order\OrderStatusWasUpdated;
use App\Integrations\Drip\Jobs\RecordFirstOrderReceived as RecordFirstOrderReceivedJob;
use App\Support\Enums\OrderStatus;

class RecordFirstOrderReceived
{
    public function handle(OrderStatusWasUpdated $event)
    {
        // If this is a first time order and the status is moved to completed
        // Then record the "First order received" event in Drip
        if ($event->newStatus != $event->oldStatus && $event->newStatus == OrderStatus::completed() && $event->order->first_time_order) {
            RecordFirstOrderReceivedJob::dispatch($event->order->id)
                ->onQueue('low');
        }
    }
}
