<?php

namespace App\Traits;

trait SettingsTrait
{
    public function setting(array|string $key, mixed $default = null): mixed
    {
        $field_name = $this->settingsField();

        if (is_array($key)) {
            $this->{$field_name} = $key;
            return $this->save();
        }

        if (!isset($this->{$field_name}->{$key}) || $this->{$field_name}->{$key} === '') {
            return $default;
        }

        return $this->{$field_name}->{$key};
    }

    protected function settingsField(): string
    {
        return 'settings';
    }

    public function getCustomFieldsAttribute(?string $value): mixed
    {
        return $this->getSettingsAttribute($value);
    }

    public function getSettingsAttribute(?string $value): mixed
    {
        if (is_null($value) || strlen($value) === 0) {
            return json_decode('{}');
        }

        return json_decode($value);
    }

    public function setCustomFieldsAttribute(?array $value): static
    {
        return $this->setSettingsAttribute($value);
    }

    public function setSettingsAttribute(?array $value): static
    {
        $field_name = $this->settingsField();

        if (is_null($value)) {
            $this->attributes[$field_name] = null;
            return $this;
        }

        if (isset($this->attributes[$field_name]) && is_array(json_decode($this->attributes[$field_name], true))) {
            $this->attributes[$field_name] = json_encode(array_merge(
                json_decode($this->attributes[$field_name], true),
                $value
            ));
            return $this;
        }

        $this->attributes[$field_name] = json_encode($value);

        return $this;
    }
}
