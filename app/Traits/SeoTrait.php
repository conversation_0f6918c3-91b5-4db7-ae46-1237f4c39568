<?php

namespace App\Traits;

trait SeoTrait
{
    public function robots(): string
    {
        return ($this->seo_visibility ?? false)
            ? 'all'
            : 'noindex, nofollow';
    }

    public function getPageHeading(): string
    {
        return ! empty($this->page_heading)
            ? strip_tags($this->page_heading)
            : strip_tags($this->title);

    }

    public function getMetaTitle(): string
    {
        return ! empty($this->page_title)
            ? strip_tags($this->page_title)
            : strip_tags($this->title);

    }

    public function getMetaDescription(bool $stripTags = true): ?string
    {
        if  ( ! empty($this->page_description)) {
            return $stripTags ? strip_tags($this->page_description) : $this->page_description;
        }

        // This is a fallback because seo_description was used on some models instead.
        if ( ! empty($this->seo_description)) {
            return $stripTags ? strip_tags($this->seo_description) : $this->seo_description;
        }

        return $stripTags ? strip_tags($this->description) : $this->description ?? null;
    }

    public function getCanonicalUrl(): string
    {
        return ! empty($this->canonical_url)
            ? $this->canonical_url
            : url()->current();

    }

    public function getHeadTags(): ?string
    {
        return ! empty($this->head_tags)
            ? $this->head_tags
            : null;
    }

    public function getBodyTags(): ?string
    {
        return ! empty($this->body_tags)
            ? $this->body_tags
            : null;
    }
}
