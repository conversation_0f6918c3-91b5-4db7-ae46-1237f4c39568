<?php

namespace App\Actions\Cart;

use App\Contracts\Cartable;
use App\Exceptions\BackOrderException;
use App\Models\Order;
use App\Models\OrderItem;

class CreateCartFromOrder
{
    protected $message = '';
    protected $itemsAdded = [];
    protected $errors = [];

    public function handle(Order $from, Cartable $to): self
    {
        $from->load([
            'pickup',
            'items.product.price' => function ($q) use ($to) {
                return $q->where('group_id', $to->cartPricingGroupId());
            }
        ]);

        [$deleted_items, $available_items] = $from->items->partition(fn(OrderItem $item) => $item->product->trashed());

        [$excluded_items, $available_items] = $available_items->partition(fn(OrderItem $item) =>
            $to->cartLocation()?->excludesProduct($item->product) ?? false
        );

        $out_of_stock_items = collect();

        $available_items->each(function (OrderItem $item) use ($to, $out_of_stock_items) {
            try {
                $to->addProduct($item->product, $item->qty);
                $this->itemsAdded[] = $item->title;
            } catch (BackOrderException $e) {
                $out_of_stock_items->push($item);
            }
        });

        if ($to instanceof Order) {
            $to->updateTotals();
        }

        $deleted_items->each(function (OrderItem $item) {
            $this->errors[] = "{$item->title} is no longer available.";
        });

        $excluded_items->each(function (OrderItem $item) {
            $this->errors[] = "{$item->title} is not available for your selected location.";
        });

        $out_of_stock_items->each(function (OrderItem $item) {
            $this->errors[] = "{$item->title} does not have enough stock for the requested quantity.";
        });

        return $this;
    }

    public function getMessage(): string
    {
        $this->message = 'No items were added to your cart.<br><br>';

        if (count($this->itemsAdded) > 0) {
            $this->message = '<strong>Added to your cart:</strong><br>' . implode('<br/>', $this->itemsAdded) . '<br><br>';
        }

        if ($this->hasErrors()) {
            $this->message .= '<strong class="text-action">Could not be added:</strong><br>' . $this->getErrorMessage();
        }

        return $this->message;
    }

    public function getErrorMessage(): ?string
    {
        if (count($this->errors) === 0) {
            return null;
        }

        return implode('<br>', $this->errors);
    }

    public function hasErrors(): bool
    {
        return count($this->errors) > 0;
    }
}
