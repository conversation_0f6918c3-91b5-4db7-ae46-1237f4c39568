<?php

namespace App\Actions\Billing;

use App\Billing\Gateway\GatewayException;
use App\Contracts\Billing;
use App\Exceptions\OrderChargeException;
use App\Models\OrderPayment;
use App\Models\Refund;
use App\Models\User;

class IssueRefund
{
    public function __construct(
        private Billing $billing
    ) {}

    /**
     * @throws OrderChargeException
     */
    public function handle(User $issuer, OrderPayment $payment, ?int $amount = null, ?string $description = null): Refund
    {
        try {
            $refund = $this->billing->refundPayment($payment, $amount, $description);
        } catch (GatewayException $exception) {
            throw new OrderChargeException($exception->getMessage());
        }

        $refund = Refund::create([
            'payment_id' => $payment->id,
            'admin_id' => $issuer->id,
            'refund_id' => $refund->id,
            'amount' => $refund->amount,
            'status' => $refund->success ? 'succeeded' : 'failed',
            'reason' => $refund->description
        ]);

        $payment->order->updateTotals();

        return $refund;
    }
}
