<?php

namespace App\Actions\Order;

use App\Models\Order;
use App\Models\OrderItem;
use App\Services\SettingsService;

class ConfirmRecurring extends Confirm
{
    protected function updateOrderAttributes(Order $order): Order
    {
        $order->type_id = $order->pickup->setting('sales_channel', 1) ?? 1;
        $order->delivery_rate = $order->pickup->delivery_rate;
        $order->delivery_fee_type = $order->pickup->setting('delivery_fee_type', 1);
        $order->accounting_id = $order->customer->accounting_id;
        $order->confirmed = true;
        $order->confirmed_date = today();
        $order->created_year = today()->year;
        $order->created_month = today()->month;
        $order->created_day = today()->day;
        $order->containers = app(SettingsService::class)->defaultParcelCount();

        // only generate dates if they are not already set and a date can be found
        if (is_null($order->pickup_date) || is_null($order->deadline_date)) {
            $order_window = $order->pickup->activeOrderWindow();

            $order->pickup_date = $order_window->deliveryDatetime();
            $order->original_pickup_date = $order_window->deliveryDatetime();
            $order->deadline_date = $order_window->deadlineDatetime();
            $order->schedule_id = $order_window?->scheduleId();
        }

        if ($order->customer->hasSecondaryEmail()) {
            $order->customer_email_alt = $order->customer->secondaryEmail();
        }

        return $order;
    }

    protected function applyTags(Order $order): void
    {
        app(ApplyTags::class)->handle($order);
    }

    protected function deductInventory(Order $order): void
    {
        $order->items->each(fn(OrderItem $item) => $item->fulfillInventoryWithoutThresholdCheck());
    }
}
