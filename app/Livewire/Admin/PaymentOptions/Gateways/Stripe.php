<?php

namespace App\Livewire\Admin\PaymentOptions\Gateways;

use App\Models\Card;
use App\Models\Setting;
use Livewire\Component;

class Stripe extends Component
{
    public bool $is_default = false;

    public bool $editing = false;

    protected $listeners = ['defaultPaymentGatewayWasUpdated'];

    public function render()
    {
        return view('livewire.payment-options.gateways.stripe');
    }

    public function edit()
    {
        $this->editing = !$this->editing;
    }

    public function setAsDefault(): bool
    {
        $is_connected = app('StripeBilling')->isConnected();

        if ( ! $is_connected) {
            error('Unable to establish a connection with the provided credentials.');
            return false;
        }

        Setting::updateOrCreate(['key' => 'payment_gateway'], ['value' => 'stripe']);
        Setting::flushCache();

        $this->is_default = true;

        $this->dispatch('defaultPaymentGatewayWasUpdated', default_gateway_id: Card::STRIPE);

        return true;
    }

    public function disconnect(): bool
    {
        try {
            app('StripeBilling')->disconnect();
        } catch (\Exception $exception) {
            error($exception->getMessage());
            return false;
        }

        Setting::whereIn('key', [
            'stripe_user_id',
            'stripe_publishable_key',
            'stripe_access_token',
            'stripe_refresh_token',
        ])->update(['value' => '']);

        Setting::where(['key' => 'payment_gateway', 'value' => 'stripe'])
            ->update(['value' => '']);

        Setting::flushCache();

        $this->dispatch('stripeDisconnected');

        return true;
    }

    public function defaultPaymentGatewayWasUpdated(int $default_gateway_id)
    {
        $this->is_default = $default_gateway_id === Card::STRIPE;
    }
}
