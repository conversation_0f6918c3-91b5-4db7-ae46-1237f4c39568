<?php

namespace App\Livewire\Admin\Pages;

use App\Livewire\Admin\SendsAdminNotifications;
use App\Models\Page;
use Illuminate\Validation\Rule;
use Livewire\Attributes\Locked;
use Livewire\Attributes\On;
use Livewire\Component;
use Ramsey\Uuid\Uuid;

class Show extends Component
{
    use SendsAdminNotifications;

    public string $tab = 'settings';
    public ?string $editing = null;

    #[Locked]
    public int $page_id;
    public string $slug;
    public string $title;
    public bool $public = false;
    public ?string $seo_meta_title = null;
    public ?string $seo_meta_description = null;
    public ?bool $seo_visible = null;
    public ?array $content = null;

    public function mount(int $page_id)
    {
        $this->page_id = $page_id;
        $this->initialize(Page::findOrFail($this->page_id));
    }

    protected function initialize(Page $page): void
    {
        $this->slug = $page->slug;
        $this->title = $page->title;
        $this->public = (bool) $page->visible;
        $this->seo_meta_title = $page->page_title;
        $this->seo_meta_description = $page->description;
        $this->seo_visible = (bool) $page->seo_visibility;
        $this->content = $page->settings->content ?? [];
    }

    #[On('widget-added'), On('widget-updated'), On('widget-deleted')]
    public function handleWidgetChange()
    {
        $this->initialize(Page::findOrFail($this->page_id));
    }

    public function render()
    {
        return view('livewire.pages.show');
    }

    public function updateWidgetOrder(array $sort)
    {
        /** @var Page $page */
        $page = Page::findOrFail($this->page_id);

        $order = collect($sort)->pluck('value')->toArray();

        $page->settings->content = collect($page->settings->content)
            ->sortBy(fn(array $widget) => array_search($widget['id'], $order))
            ->values()
            ->toArray();

        $page->save();

        $this->initialize($page);

        $this->sendAdminNotification([
            'type' => 'success',
            'title' => 'Widgets sorted!',
            'message' => 'The widget sort order has been updated!',
        ]);
    }

    public function save()
    {
        $validated = $this->validate([
            'slug' => [
                'required',
                'string',
                Rule::unique('pages', 'slug')
                    ->ignore($this->page_id)
            ],
            'title' => ['required', 'string'],
            'public' => ['nullable', 'boolean'],
            'seo_meta_title' => ['nullable', 'string'],
            'seo_meta_description' => ['nullable', 'string'],
            'seo_visible' => ['nullable', 'boolean'],
        ]);

        /** @var Page $page */
        $page = Page::findOrFail($this->page_id);

        $page->fill([
            'slug' => $validated['slug'],
            'title' => $validated['title'],
            'visible' => $validated['public'],
            'page_title' => $validated['seo_meta_title'],
            'description' => $validated['seo_meta_description'],
            'seo_visibility' => $validated['seo_visible']
        ]);

        $page->save();

        $this->sendAdminNotification([
            'level' => 'success',
            'title' => 'Page updated!',
            'message' => 'The page has been updated!',
        ]);
    }

    public function cloneWidget(string $id)
    {
        /** @var Page $page */
        $page = Page::findOrFail($this->page_id);

        $content = collect($page->settings->content ?? []);

        $widget_index = $content->search(fn($widget) => $widget['id'] === $id);

        $clonedWidget = $content[$widget_index];
        $clonedWidget['id'] = Uuid::uuid4();

        $page->settings->content = $content->push($clonedWidget)->toArray();

        $page->save();

        $this->initialize($page);

        $this->sendAdminNotification([
            'level' => 'success',
            'title' => 'Widget Cloned!',
            'message' => 'The widget has been successfully cloned.',
        ]);
    }
}
