<?php

namespace App\Livewire\Admin;

use App\Actions\Subscription\SyncSubscriptionDatetimes;
use App\Exports\SubscriptionExport;
use App\Models\Pickup;
use App\Models\RecurringOrder;
use App\Models\RecurringOrderItem;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Livewire\Attributes\Computed;
use Livewire\Attributes\On;
use Livewire\Component;
use Livewire\WithPagination;
use Maatwebsite\Excel\Facades\Excel;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

class SubscriptionsTable extends Component
{
    use WithPagination, SendsAdminNotifications;

    public bool $selectingStatus = false;

    public bool $selectingDeliveryMethod = false;

    public string $customer_term = '';

    public string $product_term = '';

    public string $subscription_ids = '';

    public array $delivery_methods = [];

    public array $statuses = ['active'];

    public string $delivery_start_date = '';

    public string $delivery_end_date = '';
    public int $page_size = 25;
    protected $queryString = [
        'customer_term' => ['except' => ''],
        'product_term' => ['except' => ''],
        'subscription_ids' => ['except' => ''],
        'delivery_methods' => ['except' => []],
        'statuses',
        'delivery_start_date' => ['except' => null],
        'delivery_end_date' => ['except' => null],
    ];

    #[Computed]
    public function filterCount()
    {
        $count = 0;

        ! empty($this->customer_term) && $count++;
        ! empty($this->product_term) && $count++;
        ! empty($this->subscription_ids) && $count++;
        ! empty($this->delivery_methods) && $count++;
        count($this->statuses) > 0 && $count++;
        (! empty($this->delivery_start_date) || ! empty($this->delivery_end_date)) && $count++;

        return $count;
    }

    public function render()
    {
        return view('livewire.subscriptions-table', [
            'available_delivery_methods' => Pickup::query()
                ->whereIn('id', fn($query) => $query->select('fulfillment_id')->from('recurring_orders'))
                ->orderBy('title')
                ->pluck('title', 'id'),

            'subscriptions' => $this->baseQuery()
                ->with(['customer', 'fulfillment'])
                ->latest()
                ->paginate($this->page_size),
        ]);
    }

    private function baseQuery()
    {
        return RecurringOrder::query()
            ->when( ! empty($this->customer_term), function ($query) {
                return $query->whereHas('customer', function ($query) {
                    $query->whereRaw('CONCAT(first_name, " ", last_name) LIKE \'%' . $this->customer_term . '%\'')
                        ->orWhere('email', 'LIKE', '%' . $this->customer_term . '%');
                });
            })
            ->when( ! empty($this->product_term), function ($query) {
                return $query->whereHas('items.product', function ($query) {
                    $query->where('title', 'LIKE', '%' . $this->product_term . '%')
                        ->orWhere('sku', 'LIKE', '%' . $this->product_term . '%')
                        ->orWhereHas('collections', function($query) {
                            $query->where('title', 'LIKE', '%' . $this->product_term . '%');
                        });
                });
            })
            ->when( ! empty($this->subscription_ids), fn($query) =>
                $query->whereIn(
                    'recurring_orders.id',
                    str($this->subscription_ids)
                        ->explode(',')
                        ->map(fn($id) => (int) trim($id))
                )
            )
            ->when( ! empty($this->delivery_methods), fn($query) => $query->whereIn('fulfillment_id', $this->delivery_methods))
            ->when(count($this->statuses) !== 1, fn($q) => $q->withTrashed())
            ->when(count($this->statuses) === 1 && $this->statuses[0] === 'canceled', fn($q) => $q->onlyTrashed())
            ->when(
                ! empty($this->delivery_start_date) || ! empty($this->delivery_end_date),
                function ($q) {
                    $starts_at = ! empty($this->delivery_start_date)
                        ? Carbon::parse($this->delivery_start_date)->startOfDay()
                        : null;
                    $ends_at = ! empty($this->delivery_end_date)
                        ? Carbon::parse($this->delivery_end_date)->endOfDay()
                        : null;

                    return $q->deliveryDateBetween($starts_at, $ends_at);
                }
            )
            ->when(count($this->statuses) === 1 && $this->statuses[0] === 'canceled', fn($q) => $q->onlyTrashed());
    }

    public function clearFilters(): void
    {
        $this->reset(['customer_term', 'product_term', 'subscription_ids', 'delivery_methods', 'delivery_start_date', 'delivery_end_date']);
        $this->statuses = [];
    }

    public function export(): BinaryFileResponse
    {
        return Excel::download(
            new SubscriptionExport(
                $this->baseQuery()
                    ->with(['customer', 'fulfillment.schedule'])
                    ->orderBy('ready_at')
                    ->get()
            ),
            'subscriptions_export_'.now()->format('Y_m_d_H_i_s').'.csv'
        );
    }

    #[On('bulk-action-selected')]
    public function handleBulkAction(string $action, array $params): void
    {
        $ids = $params['subscription_ids'] ?? [];

        if ($params['bulk_selection_type'] === 'page') {
            $ids = $this->baseQuery()
                ->latest()
                ->forPage($this->getPage(), $this->page_size)
                ->pluck('id')
                ->toArray();
        }

        if ($params['bulk_selection_type'] === 'all') {
            $ids = $this->baseQuery()->pluck('id')->toArray();
        }

        if ($action === 'product') {
            $this->handleProductBulkAction($ids, $params);
        }

        if ($action === 'date') {
            $this->handleDeliveryDateBulkAction($ids, $params);
        }

        $this->sendAdminNotification([
            'level' => 'success',
            'title' => 'Subscriptions updated!',
            'message' => 'The subscription update was successful!',
        ]);

    }

    private function handleProductBulkAction(array $subscription_ids, array $params)
    {
        if ($params['product_action'] === 'remove') {
            RecurringOrderItem::query()
                ->whereIn('order_id', $subscription_ids)
                ->where('product_id', $params['removed_product_id'])
                ->delete();

            return;
        }

        RecurringOrderItem::query()
            ->whereIn('order_id', $subscription_ids)
            ->where('product_id', $params['removed_product_id'])
            ->update(['product_id' => $params['replacement_product_id']]);
    }

    private function handleDeliveryDateBulkAction(array $subscription_ids, array $params): void
    {
        $this->baseQuery()
            ->whereIn('id', $subscription_ids)
            ->with(['fulfillment.schedule'])
            ->chunk(100, function ($subscriptions) use ($params) {
                /** @var Collection $subscriptions */
                foreach ($subscriptions as $subscription) {
                    /** @var RecurringOrder $subscription */
                    app(SyncSubscriptionDatetimes::class)
                        ->handle(
                            subscription: $subscription,
                            new_delivery_date: Carbon::parse($params['delivery_date'])
                        );
                }
            });
    }
}
