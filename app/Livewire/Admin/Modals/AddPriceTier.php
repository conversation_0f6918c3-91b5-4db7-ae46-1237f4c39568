<?php

namespace App\Livewire\Admin\Modals;

use App\Livewire\Admin\SendsAdminNotifications;
use App\Livewire\Theme\Modals\ModalAttributes;
use App\Models\Price;
use Closure;
use Livewire\Attributes\On;
use Livewire\Component;

class AddPriceTier extends Component
{
    use ModalAttributes, SendsAdminNotifications;

    public ?int $product_id = null;

    public int $quantity = 1;

    public string $unit_price = '10.00';

    public string $sale_unit_price = '10.00';

    public function render()
    {
        return view('livewire.modals.add-price-tier');
    }

    public function submit()
    {
        $validated = $this->validate($this->rules());

        $price = Price::create([
            'product_id' => $this->product_id,
            'quantity' => $validated['quantity'],
            'unit_price' => formatCurrencyForDB($validated['unit_price']),
            'sale_unit_price' => formatCurrencyForDB($validated['sale_unit_price']),
        ]);

        $this->sendAdminNotification([
            'level' => 'success',
            'title' => 'Success!',
            'message' => 'The price tier has been added successfully.',
        ]);

        $this->dispatch('price-tier-added', product_id: $this->product_id, quantity: $validated['quantity']);
        $this->close();
    }

    public function rules()
    {
        return [
            'quantity' => ['required', 'int', 'min:1', function (string $attribute, int $value, Closure $fail) {
                if (Price::query()->where(['product_id' => $this->product_id, 'quantity' => $value])->exists()) {
                    $fail("The {$attribute} already exists.");
                }
            }],
            'unit_price' => ['required', 'numeric', 'min:0'],
            'sale_unit_price' => ['required', 'numeric', 'min:0'],
        ];
    }

    #[On('close-modal-add-price-tier')]
    public function close(): void
    {
        $this->resetValidation();
        $this->reset();
        $this->closeModal();
    }

    #[On('open-modal-add-price-tier')]
    public function open(int $product_id): void
    {
        $this->product_id = $product_id;
        $this->openModal();
    }
}
