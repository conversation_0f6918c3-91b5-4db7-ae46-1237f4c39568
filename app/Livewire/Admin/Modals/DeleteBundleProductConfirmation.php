<?php

namespace App\Livewire\Admin\Modals;

use App\Livewire\Theme\Modals\ModalAttributes;
use App\Models\Product;
use Livewire\Attributes\On;
use Livewire\Component;

class DeleteBundleProductConfirmation extends Component
{
    use ModalAttributes;

    public ?int $bundle_id = null;
    public ?int $product_id = null;

    public function render()
    {
        return view('livewire.modals.delete-bundle-product-confirmation', [
            'bundle' => Product::query()->select(['title'])->find($this->bundle_id),
            'product' => Product::query()->select(['title'])->find($this->product_id),
        ]);
    }

    public function submit()
    {
        Product::find($this->bundle_id)?->bundle()->detach($this->product_id);

        $this->sendAdminNotification([
            'level' => 'success',
            'title' => 'Product updated!',
            'message' => 'The product has been removed from the bundle!',
        ]);

        $this->dispatch('bundleUpdated', $this->bundle_id);

        $this->close();
    }

    #[On('close-modal-delete-bundle-product-confirmation')]
    public function close(): void
    {
        $this->closeModal();
    }

    #[On('open-modal-delete-bundle-product-confirmation')]
    public function open(int $bundle_id, int $product_id): void
    {
        $this->bundle_id = $bundle_id;
        $this->product_id = $product_id;
        $this->openModal();
    }
}
