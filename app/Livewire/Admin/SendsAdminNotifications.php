<?php

namespace App\Livewire\Admin;

trait SendsAdminNotifications
{
    public function sendAdminNotification(array $notification): void
    {
        $this->dispatch('admin-notification-sent', notification: [
            'level' => $notification['level'] ?? 'success',
            'title' => $notification['title'] ?? 'Success!',
            'message' => $notification['message'] ?? '',
            'duration' => $notification['duration'] ?? 3000,
        ]);
    }
}
