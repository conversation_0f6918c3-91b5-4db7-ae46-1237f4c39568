<?php

namespace App\Livewire\Admin\Settings;

use App\Livewire\Admin\SendsAdminNotifications;
use App\Models\Setting;
use Illuminate\Support\Facades\Cache;
use Livewire\Component;

class Site extends Component
{
    use SendsAdminNotifications;

    public ?string $homepage_promo_image = null;
    public string $homepage_promo_cta_label = 'Learn more';
    public string $homepage_promo_cta_url = '#';

    public function mount()
    {
        $homepage_promo = json_decode(setting('homepage_promo'), true);

        $this->homepage_promo_image = $homepage_promo['image'] ?? null;
        $this->homepage_promo_cta_label = $homepage_promo['cta']['label'] ?? 'Learn more';
        $this->homepage_promo_cta_url = $homepage_promo['cta']['url'] ?? '#';
    }

    public function render()
    {
        return view('livewire.settings.site');
    }

    public function removePromoImage()
    {
        $this->setPromoImage(path: null);
    }

    public function setPromoImage(?string $path)
    {
        $this->homepage_promo_image = $path;
    }

    public function save()
    {
        $validated = $this->validate([
            'homepage_promo_image' => ['nullable', 'url'],
            'homepage_promo_cta_label' => ['nullable', 'string', 'max:255'],
            'homepage_promo_cta_url' => ['nullable', 'string'],
        ]);

        Setting::updateOrCreate(['key' => 'homepage_promo'], ['value' => json_encode([
            'image' => $validated['homepage_promo_image'],
            'cta' => [
                'label' => $validated['homepage_promo_cta_label'],
                'url' => $validated['homepage_promo_cta_url'],
            ],
        ])]);

        Cache::tags('setting')->flush();

        $this->sendAdminNotification([
            'level' => 'success',
            'title' => 'Site updated!',
            'message' => 'The site has been successfully updated.',
        ]);
    }
}
