<?php

namespace App\Livewire\Theme\Modals;

use App\Models\Address;
use App\Models\User;
use App\Services\SettingsService;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use Livewire\Attributes\On;
use Livewire\Component;

class EditAddress extends Component
{
    use ModalAttributes;

    public int $address_id;
    public string $street;
    public string $street_2;
    public string $city;
    public string $state;
    public string $postal_code;
    public ?string $instructions;
    public bool $is_default = false;

    protected array $rules = [
        'street' => ['required', 'string'],
        'street_2' => ['nullable', 'string'],
        'city' => ['required'],
        'state' => ['required'],
        'postal_code' => ['required'],
        'instructions' => ['nullable', 'string'],
        'is_default' => ['boolean'],
    ];

    public function render()
    {
        return view('theme::livewire.modals.edit-address');
    }

    public function submit()
    {
        $validated = $this->validate();

        $user = auth()->user();

        if (
            (! ($validated['is_default'] ?? false))
            && $user->addresses()
                ->where('address_id', $this->address_id)
                ->first()
                ?->getRelationValue('location')->is_default
        ) {
            error("You cannot update a user's default address without setting another address as default first.");

            return $this->redirect(route('customer.addresses'));
        }

        $attributes = Arr::except($validated, ['street_2', 'instructions', 'is_default']);

        $address = Address::firstOrCreate(array_merge($attributes, ['country' => app(SettingsService::class)->farmCountry()]));

        if ($validated['is_default'] ?? false) {
            $this->resetAddressDefaults($user);
        }

       if ($address->wasRecentlyCreated) {
            $user->addresses()->detach($this->address_id);

            $user->addresses()->attach($address->id, [
                'name' => Str::limit("{$validated['street']}, {$validated['city']}, {$validated['state']}, {$validated['postal_code']}"),
                'street_2' => $validated['street_2'],
                'instructions' => $validated['instructions'],
                'is_default' => ($validated['is_default'] ?? false)
            ]);

            $this->address_id = $address->id;
       } else {
           $user->addresses()->updateExistingPivot($this->address_id, [
               'name' => Str::limit("{$validated['street']}, {$validated['city']}, {$validated['state']}, {$validated['postal_code']}"),
               'street_2' => $validated['street_2'],
               'instructions' => $validated['instructions'],
               'is_default' => ($validated['is_default'] ?? false)
           ]);
       }

        flash('The address has been updated!');

        return $this->redirect(route('customer.addresses'));
    }

    private function resetAddressDefaults(User $user): void
    {
        $user->addresses()->update(['is_default' => false]);
    }

    #[On('open-modal-edit-address')]
    public function open(int $address_id): void
    {
        $this->address_id = $address_id;

        $address = auth()->user()
            ->addresses()
            ->where('address_id', $this->address_id)
            ->first();

        $location = $address?->getRelationValue('location');

        $this->street = $address?->street;
        $this->street_2 = $location->street_2;
        $this->city = $address?->city;
        $this->state = $address?->state;
        $this->postal_code = $address?->postal_code;
        $this->instructions = $location->instructions;
        $this->is_default = $location->is_default;

        $this->openModal();
    }

    #[On('close-modal-edit-address')]
    public function close(): void
    {
        $this->reset(['address_id', 'street', 'street_2', 'city', 'state', 'postal_code', 'instructions', 'is_default']);
        $this->closeModal();
    }
}
