<?php

namespace App\Livewire\Theme;

use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Product;
use App\Models\RecurringOrder;
use App\Services\SubscriptionSettingsService;
use Livewire\Attributes\Locked;
use Livewire\Component;

class SubscriptionShow extends Component
{
    #[Locked]
    public int $subscription_id;

    public ?OrderItem $selected_promo_item;

    public function render()
    {
        $subscription = RecurringOrder::query()
            ->withTrashed()
            ->with(['items.product', 'currentOrder'])
            ->find($this->subscription_id);

        if (is_null($subscription)) {
            return $this->redirect(route('customer.orders'));
        }

        $this->selected_promo_item = $subscription->currentOrder?->findPromotionalItem();

        $in_transit_order = null;

        if ( ! $subscription->trashed()) {
            $in_transit_order = $subscription->orders()
                ->where('deadline_date', '<=', today())
                ->where('pickup_date', '>=', today())
                ->where('canceled', false)
                ->whereNull('skipped_at')
                ->get(['id', 'deadline_date', 'pickup_date'])
                ->filter(fn(Order $order) => $order->deadlineHasPassed())
                ->first();
        }

        return view('theme::livewire.subscription-show', [
            'subscription' => $subscription,
            'in_transit_order' => $in_transit_order,
            'available_promo_items' => Product::findMany(
                app(SubscriptionSettingsService::class)->productIncentiveIds()
            ),
        ]);
    }
}
