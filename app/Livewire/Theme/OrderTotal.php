<?php

namespace App\Livewire\Theme;

use Livewire\Component;

class OrderTotal extends Component
{
    use FetchesOrder;

    public int $total;

    protected $listeners = [
        'orderUpdated' => 'refreshTotal',
    ];

    public function mount()
    {
       $this->refreshTotal();
    }

    public function refreshTotal()
    {
        $this->total = $this->fetchCustomerOrder()->total ?? 0;
    }

    public function render()
    {
        return <<<'blade'
            <span>${{ money($total) }}</span>
        blade;
    }
}
