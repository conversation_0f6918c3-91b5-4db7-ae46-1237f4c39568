<?php

namespace App\Livewire\Theme;

use App\Models\Order;
use App\Services\StoreService;

trait FetchesOrder
{
    public function fetchCustomerOrder(): ?Order
    {
        return app(StoreService::class)->orderWithoutDeliveryMethodCheck();
    }

    public function findOrder(int $orderId)
    {
        return auth()->user()->orders()
            ->find($orderId)
            ?->load(Order::relationsToLoad());
    }
}
