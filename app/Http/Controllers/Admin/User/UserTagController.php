<?php

namespace App\Http\Controllers\Admin\User;

use App\Http\Controllers\Controller;
use App\Models\Tag;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class UserTagController extends Controller
{
    public function index(int $userId)
    {
        $userTags = User::findOrFail($userId)->tags()->pluck('id')->toArray();
        $tags = Tag::select(['id', 'title', 'slug'])->get();

        foreach ($tags as $index => $tag) {
            if (in_array($tag->id, $userTags)) {
                $tags[$index]['active'] = true;
            } else {
                $tags[$index]['active'] = false;
            }
        }

        return $tags->toJson();
    }

    public function store(Request $request, int $userId): JsonResponse
    {
        if (! is_null(Tag::where(['title' => $request->get('title')])->first())) {
            return response()->json(false);
        }

        $tag = Tag::firstOrCreate([
            'title' => $request->get('title'),
            'type' => Tag::type('user'),
        ]);
        $user = User::findOrFail($userId);
        $user->tags()->attach($tag->id);

        return response()->json([
            'id' => $tag->id,
            'title' => $tag->title,
            'slug' => $tag->slug,
            'active' => true,
        ]);
    }

    public function update(int $userId, int $tagId): JsonResponse
    {
        $user = User::findOrFail($userId);
        $user->tags()->attach($tagId);

        return response()->json(['responseText' => 'Tag assigned to user']);
    }

    public function destroy(int $userId, int $tagId): JsonResponse
    {
        $user = User::findOrFail($userId);
        $user->tags()->detach($tagId);

        return response()->json(['responseText' => 'Tag dissociated from user.']);
    }
}
