<?php

namespace App\Http\Controllers\Admin\Order;

use App\Exceptions\OrderChargeException;
use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\OrderPayment;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class OrderRefundController extends Controller
{
    public function store(Request $request, Order $order, OrderPayment $payment): JsonResponse
    {
        $validated = $request->validate([
            'amount' => [
                'nullable',
                'numeric',
                'min:0',
                function ($attribute, $value, $fail) use ($payment) {
                    if (formatCurrencyForDB($value) > $payment->amount) {
                        $fail('The refund amount can not be greater than the payment amount.');
                    }
                },
            ],
            'description' => ['nullable', 'string', 'max:255'],
            'full_refund' => ['nullable', 'boolean'],
        ]);

        $amount = ! $request->boolean('full_refund') && $request->has('amount')
            ? formatCurrencyForDB($validated['amount'])
            : null;

        try {
            $refund = $payment->issueRefund(auth()->user(), $amount, $validated['description'] ?? null);

        } catch (OrderChargeException $exception) {
            return response()->json($exception->getMessage(), 400);
        } catch (\Exception $exception) {
            \Bugsnag::notifyException($exception);

            return response()->json($exception->getMessage(), 400);
        }

        $order->updateTotals();

        return response()->json([
            'refund' => $refund,
            'order' => $order,
            'responseText' => 'A refund of &#36;'.money($refund['amount']).' was added.',
        ]);
    }
}
