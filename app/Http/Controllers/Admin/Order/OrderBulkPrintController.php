<?php

namespace App\Http\Controllers\Admin\Order;

use App\Http\Controllers\Controller;
use App\Http\Responses\OrderPrintResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

class OrderBulkPrintController extends Controller
{
    public function update(Request $request, string $template)
    {
        $validated = $request->validate([
            'type' => ['nullable', 'in:page,all'],
            'orders' => [Rule::requiredIf($request->get('type') !== 'all'), 'array'],
            'orderBy' => ['nullable'],
            'sort' => ['nullable', 'in:asc,desc'],
        ], [
            'orders.required' => 'Please select some orders and try again.',
        ]);

        if ($request->get('type') === 'all') {
            $validated['orders'] = [];
            $validated = array_merge($validated, (session('orders-filtered') ?? []));
        }

        return new OrderPrintResponse($template, $validated);
    }
}
