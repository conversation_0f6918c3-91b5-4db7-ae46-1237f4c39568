<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Page;
use Illuminate\Http\JsonResponse;

class PageWidgetSortController extends Controller
{
    public function store(Page $page): JsonResponse
    {
        $validated = request()->validate([
            'widgets' => ['array'],
            'widgets.*.id' => ['required', 'integer'],
            'widgets.*.sort' => ['required', 'integer'],
        ]);

        foreach ($validated['widgets'] as $widget) {
            $page->widgets()->where('id', $widget['id'])->update(['sort' => $widget['sort']]);
        }

        return response()->json('Page widgets sorted.');
    }
}
