<?php

namespace App\Http\Controllers\Admin\Product;

use App\Http\Controllers\Controller;
use App\Models\Product;

class ProductSeoController extends Controller
{
    public function update(Product $product)
    {
        $validated = request()->validate([
            'seo_description' => ['nullable', 'string'],
            'seo_visibility' => ['boolean'],
            'canonical_url' => ['nullable', 'string'],
            'page_title' => ['nullable', 'string'],
            'head_tags' => ['nullable', 'string'],
            'body_tags' => ['nullable', 'string'],
        ]);

        $product->update($validated);

        return back();
    }
}
