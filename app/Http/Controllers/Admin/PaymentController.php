<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Payment;
use Illuminate\Http\Request;
use Illuminate\View\View;

class PaymentController extends Controller
{
    public function index(): View
    {
        return view('payments.index')
            ->with(['payments' => Payment::all()]);
    }

    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required',
        ]);

        $payment = Payment::create($request->all());

        return to_route('admin.payments.edit', $payment->id);
    }

    public function edit(string $key): View
    {
        return view('payments.edit.'.$key)
            ->with([
                'payment' => Payment::where('key', $key)->firstOrFail(),
            ]);
    }

    public function update(Request $request, int $id)
    {
        $request->validate([
            'title' => ['sometimes', 'required'],
        ]);

        Payment::findOrFail($id)->update($request->only([
            'enabled', 'title', 'instructions',
        ]));

        return back();
    }
}
