<?php

namespace App\Http\Controllers\API\Theme;

use App\Http\Controllers\Controller;
use App\Models\Product;
use Illuminate\Http\Request;

class ProductController extends Controller
{
    public function index(Request $request)
    {
        return Product::select(['id', 'title', 'description', 'unit_price', 'unit_of_issue', 'weight', 'cover_photo'])
            ->filter($request->all())
            ->limit(50)
            ->get();
    }
}
