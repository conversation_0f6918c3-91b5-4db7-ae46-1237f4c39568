<?php

namespace App\Http\Controllers\API;

use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use App\Models\Template;
use App\Models\TemplateSegment;
use Illuminate\Http\Request;

class TemplateDuplicatedSegmentController extends Controller
{
    public function store(Request $request, $templateId): JsonResponse
    {
        TemplateSegment::where('sort', '>=', $request->get('sort'))->increment('sort', 1);

        $segment = TemplateSegment::create($request->only([
            'template_id',
            'title',
            'view',
            'content',
            'sort',
            'settings'
        ]));

        $segment->renderPreviewHtml();
        $segment->save();

        Template::findOrFail($templateId)->renderSegments();

        return response()->json([
            'segment' => $segment
        ]);
    }
}
