<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Product;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class ProductController extends Controller
{
    public function index(Request $request): JsonResponse
    {
        $products = Product::filter($request->all())
            ->limit(min($request->input('limit'), 300))
            ->get();

        if ( ! request()->has('without_default')) {
            $products->prepend(new Product(['id' => '0', 'title' => 'None']));
        }

        return response()->json($products);
    }
}
