<?php

namespace App\Http\Controllers\API;

use App\Billing\Gateway\GatewayException;
use App\Billing\Stripe\StripeStandardAccount;
use App\Contracts\Billing;
use App\Models\User;
use Illuminate\Http\JsonResponse;

class UserSetupIntentsController
{
    /**
     * @throws GatewayException
     */
    public function __invoke(User $user, Billing $billing): JsonResponse
    {
        return $this->handleStripe($user, $billing);
    }

    public function handleStripe(User $user, Billing $billing): JsonResponse
    {
        /** @var StripeStandardAccount $billing */

        try {
            if ( ! $user->hasCustomerId()) {
                $user = $this->createCustomer($billing, $user);
            }

            $client_secret = $billing->createSetupIntent($user)->client_secret;

        } catch (GatewayException $exception) {
            return response()->json(['message' => $exception->getMessage()], 400);
        }

        return response()->json(compact('client_secret'), 201);
    }

    /**
     * @throws GatewayException
     */
    private function createCustomer(Billing $billing, User $user)
    {
        $customer = $billing->createCustomer($user);

        $user->customer_id = $customer->id;
        $user->save();

        return $user;
    }
}
