<?php

namespace App\Http\Controllers\API;

use Illuminate\Http\JsonResponse;
use App\Contracts\CartService;
use App\Events\Cart\CartUpdated;
use App\Events\User\UserUpdated;
use App\Exceptions\ExclusivityException;
use App\Http\Controllers\Controller;
use App\Livewire\Theme\FetchesCart;
use App\Models\Pickup;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Facades\URL;

class CustomerController extends Controller
{
    use FetchesCart;

    public function update(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'pickup_point' => ['required', 'exists:pickups,id'],
            'redirect' => ['nullable'],
            'remove_excluded_items' => ['nullable', 'boolean']
        ]);

        $pickup = Pickup::find($validated['pickup_point']);

        if (auth()->guest()) {
            Cookie::queue(Cookie::make('fulfillment_id', (string) $pickup->id, 259200));

            return response()->json([
                'request' => $request->all(),
                'pickup_point' => $pickup->id,
                'redirect' => url($validated['redirect'] ?? '/store')
            ]);
        }

        /** @var User $user */
        $user = auth()->user();

        if ($user->hasRecurringOrder()) {
            return response()->json('The location cannot be updated when on a subscription.', 409);
        }

        $cart = $this->fetchShopperCart(should_stub: false);

        if ( ! is_null($cart)) {
            try {
                $cart->updateCartLocation($pickup, $request->boolean('remove_excluded_items'));
            } catch (ExclusivityException $e) {
                return response()->json($e->defaultMessage(), 422);
            }

            event(new CartUpdated($cart));
        }

        $user->updateFulfillmentLocation($pickup);
        $user->refresh();

        event(new UserUpdated($user));

        return response()->json([
            'request' => $request->all(),
            'pickup_point' => $user->pickup_point,
            'redirect' => $request->filled('redirect') ? url($request->get('redirect')) : URL::previous()
        ]);
    }
}
