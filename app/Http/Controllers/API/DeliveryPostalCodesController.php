<?php

namespace App\Http\Controllers\API;

use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use App\Models\Pickup;
use App\Models\PickupZip;
use Illuminate\Http\Request;

class DeliveryPostalCodesController extends Controller
{
    public function index(Request $request, Pickup $delivery_zone)
    {
        return $delivery_zone->zips()
            ->when($request->has('query'), function ($q) use ($request) {
                return $q->where('zip', 'LIKE', '%' . $request->input('query') . '%');
            })
            ->orderBy('zip')
            ->paginate(200);
    }

    public function edit(Request $request, Pickup $delivery_zone, PickupZip $postal_code)
    {
        abort_if($postal_code->pickup_id !== $delivery_zone->id, 404);

        return $postal_code;
    }

    public function store(Request $request, Pickup $delivery_zone)
    {
        $request->validate(['postal_codes' => ['required', 'string', 'max:5000']]);

        $postal_code_chunks = $this->preparePostalCodeInputString($request->input('postal_codes'));

        $duplicate_postal_codes = collect();

        // Loop over each chunk and validate for any duplicates.
        foreach ($postal_code_chunks as $postal_code_chunk) {
            $existing_postal_codes = $delivery_zone->zips()
                ->whereIn('zip', $postal_code_chunk)
                ->pluck('zip');

            $duplicate_postal_codes = $duplicate_postal_codes->merge($existing_postal_codes)->unique();

            $new_postal_codes = $postal_code_chunk
                ->diff($existing_postal_codes)
                ->map(function ($postalCode) {
                    return new PickupZip(['zip' => $postalCode]);
                });

            $delivery_zone->zips()->saveMany($new_postal_codes);
        }

        $ignoredMessage = null;
        if ($duplicate_postal_codes->count() > 0) {
            $ignoredMessage = $duplicate_postal_codes->count() < 5 ?
                "These postal codes where not added because they are duplicates: {$duplicate_postal_codes->implode(', ')}" :
                'Several postal codes were not be added because they are already being used.';
        }

        return response()->json([
            'message' => 'Postal codes added',
            'ignored' => $ignoredMessage
        ]);
    }

    public function update(Request $request, Pickup $delivery_zone, PickupZip $postal_code): JsonResponse
    {
        abort_if($postal_code->pickup_id !== $delivery_zone->id, 404);

        $request['postal_code'] = str_replace([' ', "\r\n", "\r", "\n"], '', $request->input('postal_code'));

        $request->validate([
            'postal_code' => ['required', 'string', 'min:5', 'max:16']
        ]);

        $postal_code->update([
            'zip' => str_replace(' ', '', $request->input('postal_code'))
        ]);

        return response()->json('Postal code updated');
    }

    public function destroy(Request $request, Pickup $delivery_zone, $postal_code = null): JsonResponse
    {
        if ($postal_code) {
            $pickup_zip = PickupZip::findOrFail($postal_code);
            abort_if($pickup_zip->pickup_id !== $delivery_zone->id, 404);

            $pickup_zip->delete();
            return response()->json('Postal code deleted');
        }

        $request->validate([
            'postal_codes_to_delete' => ['required', 'max:10000']
        ]);

        $this->preparePostalCodeInputString($request->input('postal_codes_to_delete'))
            ->each(function ($postal_code_chunk) use ($delivery_zone) {
                $delivery_zone->zips()
                    ->whereIn('zip', $postal_code_chunk)
                    ->delete();
            });

        return response()->json('Postal codes deleted.');
    }

    private function preparePostalCodeInputString($postal_code_string)
    {
        // Trim whitespace, filter blanks, and break the input into chunks
        return collect(explode(',', $postal_code_string))
            ->map(function ($postalCode) {
                return str_replace(' ', '', preg_replace('/\s+/S', ' ', $postalCode));
            })
            ->filter()
            ->chunk(100);
    }
}
