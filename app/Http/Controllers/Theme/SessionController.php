<?php

namespace App\Http\Controllers\Theme;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cookie;

class SessionController extends Controller
{
    public function __construct()
    {
        if (app()->environment(['production', 'testing'])) {
            $this->middleware('throttle:login')->only('store');
        }
    }

    public function show(Request $request)
    {
        if (auth()->check()) {
            return redirect()->to(url($request->cookie('last_viewed_page', '/account')));
        }

        return view('theme::authentication.login');
    }

    public function store(Request $request)
    {
        $credentials = $request->validate([
            'email' => ['required', 'email:filter'],
            'password' => ['required'],
        ]);

        $user = User::where('email', $credentials['email'])->first();

        if (($user?->needsPassword() ?? false)) {
            flash('Please finish creating your account by adding a password.');

            return redirect()->route('choose-password.show')
                ->with(['email' => $credentials['email']]);
        }

        if ( ! Auth::attempt($credentials)) {
            // Update user's last login.
            flash('The credentials you entered were incorrect.');
            return back()->withInput();
        }

        $user = auth()->user();
        $user->last_login = now();
        $user->save();

        flash('You have been logged in!');

        // Check if a global redirect is set.
        if (setting('user_login_redirect')) {
            return redirect()->to(setting('user_login_redirect'));
        }

        // Redirect back to the last viewed page.
        Cookie::queue(Cookie::forget('last_viewed_page'));

        return redirect()->to(url($request->cookie('last_viewed_page', '/')));
    }

    public function destroy(): RedirectResponse
    {
        Auth::logout();

        return redirect('/login')->withCookies([Cookie::forget('last_viewed_page')]);
    }
}
