<?php

namespace App\Http\Controllers\Theme\Password;

use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use App\Http\Controllers\Controller;
use App\Mail\PasswordReminder;
use App\Traits\ResetPasswordTrait;
use App\Models\User;
use Illuminate\Support\Facades\Mail;

class PasswordForgotController extends Controller
{
    use ResetPasswordTrait;

    public function show()
    {
        if (auth()->check()) {
            return redirect('/account');
        }

        return view('theme::authentication.forgot-password');
    }

    public function store(): RedirectResponse
    {
        request()->validate([
            'email' => ['required', 'email']
        ]);

        $user = User::where('email', request('email'))->first();

        if ($user) {
            Mail::to($user->email)->queue(new PasswordReminder([
                'token' => $this->makePasswordResetToken($user),
                'name' => $user->first_name,
            ]));
        }

        flash('Your password reset link has been sent to your email.');
        return redirect('/password/forgot');
    }

    public function showConfirmation(): View
    {
        return view('theme::authentication.forgot-password-confirmation')
            ->with(['email' => request('email')]);
    }
}
