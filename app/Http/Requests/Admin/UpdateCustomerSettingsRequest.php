<?php

namespace App\Http\Requests\Admin;

use App\Http\Requests\Request;
use Illuminate\Validation\Rule;

class UpdateCustomerSettingsRequest extends Request
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'registration_wall' => ['required', Rule::in(['site', 'store', 'prices', 'cart', 'checkout'])],
            'customer_location_wall' => [
                'required',
                Rule::when($this->registration_wall === 'checkout', Rule::in(['store', 'cart'])),
            ],
            'user_registration_credit' => ['numeric', 'min:0'],
            'user_registration_redirect' => ['nullable', 'string'],
            'user_login_redirect' => ['nullable', 'string'],
            'lead_capture_url' => ['nullable', 'string'],
            'pickup_results_count' => ['integer', 'min:1'],
            'pickup_results_radius' => ['integer', 'min:1'],
            'enable_referrals' => ['required', 'boolean'],
            'referral_bonus' => ['numeric', 'min:0'],
            'referral_payout' => ['numeric', 'min:0'],
            'require_user_activation' => ['required', 'boolean'],
            'newsletter_auto_opt_in' => ['required', 'boolean'],
            'user_export_format' => ['required', 'in:default,drip'],
        ];
    }

    public function messages(): array
    {
        return [
            'customer_location_wall.in' => 'The geofencing setting must be enabled when authenticating at checkout.',
        ];
    }

    protected function prepareForValidation()
    {
        foreach ($this->all() as $key => $value) {
            if (method_exists($this, $key)) {
                $this->merge([$key => $this->{$key}($value)]);
            }
        }
    }

    // Input mutations
    protected function referral_bonus($value)
    {
        return formatCurrencyForDB($value);
    }

    protected function referral_payout($value)
    {
        return formatCurrencyForDB($value);
    }
}
