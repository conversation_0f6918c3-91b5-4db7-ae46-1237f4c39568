<?php

namespace App\Http\Resources;

use App\Models\Page;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class PageResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        /** @var Page $page */
        $page = $this->resource;

        return [
            'id' => $page->id,
            'title' => $page->title,
            'slug' => $page->slug,
            'page_title' => $page->page_title,
            'description' => $page->description,
            'body' => $page->body,
            'settings' => $page->settings,
            'visible' => $page->visible,
            'needs_published' => $page->needs_published,
            'seo_visibility' => $page->seo_visibility,
            'created_at' => $page->created_at,
            'updated_at' => $page->updated_at,
            'deleted_at' => $page->deleted_at,
            'widgets' => ContentWidgetResource::collection($page->widgets)
        ];
    }
}
