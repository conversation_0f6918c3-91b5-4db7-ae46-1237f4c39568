<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class SyncProductPhotos extends Command
{
    protected $signature = 'app:sync-product-photos';

    protected $description = 'This command will sync product photos from the products table to the media_product.';

    public function handle()
    {
        DB::table('media_product')->truncate();

        DB::statement('
                INSERT INTO media_product (media_id, product_id, sort) 
                SELECT media.id as media_id, products.id as product_id, 1 
                FROM products 
                JOIN media ON products.cover_photo = media.path
        ');
    }
}
