<?php

namespace App\Console\Commands;

use App\Models\ApiKey;
use Illuminate\Console\Command;
use Illuminate\Support\Str;

class GenerateApiKey extends Command
{
    protected $signature = 'api:generate-key';

    protected $description = 'Generate an API key for a tenant.';

    public function handle(): int
    {
        /** @var ApiKey $model */
        $model = ApiKey::factory()->create(['name' => 'Test Key']);
        $apiKey = $model->generateKey();

        $keyModel = ApiKey::wherePrefix(Str::substr($apiKey, 0, 5))->first();

        $keyModel->scope = [
            'orders:index' => true,
            'orders:show' => true,
            'orders:update' => true,
            'products:index' => true,
            'products:show' => true,
            'products:update' => true,
            'inventory:update' => true
        ];

        $keyModel->save();

        $this->line('Here is your API key:');
        $this->line($apiKey);

        return self::SUCCESS;
    }
}
