<?php

namespace App\Listeners\Order\OrderWasPaid;

use App\Events\Order\OrderWasPaid;
use App\Models\User;
use Illuminate\Contracts\Queue\ShouldQueue;

class IssueReferralCredit implements ShouldQueue
{
    /**
     * Handle the event.
     */
    public function handle(OrderWasPaid $event): void
    {
        if ($event->order->first_time_order &&
            $event->order->customer &&
            $event->order->customer->referral_user_id
        ) {
            if ($referrer = User::find($event->order->customer->referral_user_id)) {
                $referrer->creditReferralBonus()->save();
            }
        }
    }
}
