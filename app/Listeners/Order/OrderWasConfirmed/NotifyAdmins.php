<?php

namespace App\Listeners\Order\OrderWasConfirmed;

use App\Events\Order\OrderWasConfirmed;
use App\Notifications\OrderConfirmed;
use App\Models\User;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Support\Facades\Notification;

class NotifyAdmins implements ShouldQueue
{
    use DispatchesJobs;

    public function handle(OrderWasConfirmed $event): void
    {
        if (setting('notify_of_new_orders')) {
            // Send admins a notification the order was confirmed.
            Notification::route('mail', setting('email_general'))
                ->notify(new OrderConfirmed($event->order->id));
        }

        if (count($adminRecipients = User::notificationRecipients('email_notify_new_orders'))) {
            foreach ($adminRecipients as $email => $name) {
                Notification::route('mail', [$email => $name])
                    ->notify(new OrderConfirmed($event->order->id));
            }
        }
    }
}
