<?php

namespace App\Listeners\Subscription;

use App\Events\Subscription\CustomerSubscribed;
use App\Http\Resources\SubscriptionResource;
use App\Models\Integration;
use App\Models\Webhook;
use Illuminate\Contracts\Queue\ShouldQueue;

class SendSubscriptionWebhooks implements ShouldQueue
{
    public function handle(CustomerSubscribed $event): void
    {
        $payload = json_decode((new SubscriptionResource($event->subscription))->toJson(), true);

        Webhook::forTopic('subscriptions.created')
            ->forDeliveryMethod($event->subscription->fulfillment)
            ->forSalesChannel($event->subscription->fulfillment->salesChannelId())
            ->get()
            ->each(fn (Webhook $webhook) => $webhook->send($payload));
    }
}