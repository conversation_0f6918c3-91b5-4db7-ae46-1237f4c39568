<?php

namespace App\Repositories\Reports;

use App\Models\Filters\IncomeAnalysisFilter;
use App\Models\Order;
use Illuminate\Support\Collection;
use stdClass;

class IncomeAnalysisRevenueFromFees
{
    /**
     * @return Collection<int, (object{accounting_class: 'Fee income', total: int}&stdClass)|(object{accounting_class: 'Sales tax', total: int}&stdClass)>
     */
    public function handle(array $filters): Collection
    {
        $result = Order::query()
            ->selectRaw('SUM(orders.fees_subtotal) AS fee_total, SUM(orders.tax) AS sales_tax_total')
            ->filter($filters, IncomeAnalysisFilter::class)
            ->toBase()
            ->first();

        return collect([
            (object) ['accounting_class' => 'Fee income', 'total' => (int) $result->fee_total],
            (object) ['accounting_class' => 'Sales tax', 'total' => (int) $result->sales_tax_total],
        ]);
    }
}
