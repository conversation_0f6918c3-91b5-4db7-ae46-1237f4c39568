<?php

namespace App\Repositories;

use App\Models\Order;
use App\Models\Product;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;

class OrderIndexRepository
{
    /**
     * @param  Collection<string, mixed>  $filters
     * @return Builder<Order>
     */
    public function query(Collection $filters): Builder
    {
        $query = Order::filter($filters->all())
            ->select([
                'orders.id', 'orders.paid', 'orders.type_id', 'orders.confirmed', 'orders.weight', 'orders.flagged', 'orders.exported', 'orders.first_time_order',
                'orders.packed', 'orders.picked_up', 'orders.customer_first_name', 'orders.customer_last_name', 'orders.customer_phone', 'orders.customer_email',
                'orders.status_id', 'orders.pickup_id', 'orders.pickup_date', 'orders.total', 'orders.order_discount', 'orders.tax', 'orders.subscription_savings',
                'orders.credit_applied', 'orders.delivery_fee', 'orders.customer_id', 'pickups.title as pickup_title', 'orders.containers', 'orders.containers_2',
                'orders.shipping_street', 'orders.shipping_street_2', 'orders.shipping_city', 'orders.shipping_state', 'orders.shipping_zip', 'orders.invoice_notes',
                'orders.billing_street', 'orders.billing_street_2', 'orders.billing_city', 'orders.billing_state', 'orders.billing_zip', 'orders.payment_date',
                'orders.tracking_id', 'orders.packing_notes', 'orders.payment_terms', 'orders.due_date', 'orders.staff_id', 'orders.payment_id', 'orders.confirmed_date',
            ])
            ->leftJoin('pickups', 'orders.pickup_id', '=', 'pickups.id');

        $product_filters = ['sku', 'products', 'inventory_type', 'vendor_id'];

        if ($filters->only($product_filters)->isEmpty()) {
            return $query;
        }

        return $query
            ->whereHas('items', fn($item_query) =>
                $item_query->select('id')
                    ->whereIn(
                        'product_id',
                        Product::filter($filters->only($product_filters)->all())
                            ->withTrashed()
                            ->pluck('id')
                    ));
    }
}
