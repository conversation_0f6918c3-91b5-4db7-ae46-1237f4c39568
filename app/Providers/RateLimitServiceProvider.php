<?php

namespace App\Providers;

use Illuminate\Cache\RateLimiting\Limit;
use Illuminate\Foundation\Support\Providers\RouteServiceProvider as ServiceProvider;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;

class RateLimitServiceProvider extends ServiceProvider
{
    public function boot(): void
    {
        RateLimiter::for('emails', function (Request $request) {
            return auth()->user()
                ? Limit::perMinute(2)->by(auth()->user()->id.'-'.auth()->user()->email)
                : Limit::perMinute(2)->by($request->ip());
        });

        RateLimiter::for('api', function (Request $request) {
            return Limit::perMinute(60)->by(auth()->user()?->id ?: $request->ip());
        });

        RateLimiter::for('login', function (Request $request) {
            return [
                Limit::perMinute(500)->by($request->ip()),
                Limit::perMinute(5)->by($request->input('email')),
            ];
        });

        RateLimiter::for('register', function (Request $request) {
            return [
                Limit::perDay(8)->by('register_daily:' . $request->ip()),
                Limit::perMinute(4)->by('register_minutely:' . $request->ip()),
            ];
        });
    }
}
