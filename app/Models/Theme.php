<?php

namespace App\Models;

use App\Exceptions\ThemeNotFoundException;
use App\Traits\SettingsTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Theme
 *
 * @property string $id
 * @property string $title
 * @property string|null $view_path
 * @property mixed $settings
 * @property string $css
 * @property string $css_preview
 * @property string $custom_css
 * @property int $active
 * @property int $user_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property mixed $custom_fields
 * @method static \Illuminate\Database\Eloquent\Builder|Theme newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Theme newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Theme query()
 * @method static \Illuminate\Database\Eloquent\Builder|Theme whereActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Theme whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Theme whereCss($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Theme whereCssPreview($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Theme whereCustomCss($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Theme whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Theme whereSettings($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Theme whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Theme whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Theme whereUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Theme whereViewPath($value)
 * @method static \Database\Factories\ThemeFactory factory($count = null, $state = [])
 * @mixin \Eloquent
 */
class Theme extends Model
{
    use HasFactory;

    use SettingsTrait;

    public $incrementing = false;
    protected $guarded = []; // or null
protected $primaryKey = 'id';

    /**
     * Install the provided theme.
     *
     * @throws ThemeNotFoundException
     */
    public static function install(string $themeName, ?string $themeIdToCopy = null): Theme
    {
        // Check if Theme files exist.
        $configFilePath = base_path('public/themes/' . $themeName . '/config/config.php');
        if (!file_exists($configFilePath)) {
            throw new ThemeNotFoundException('No config file found for ' . $themeName);
        }

        $configFile = require $configFilePath;

        $theme = Theme::forceCreate([
            'id' => $configFile['id'],
            'title' => $configFile['title'],
            'view_path' => $configFile['view_path']
        ]);

        if ($themeIdToCopy) {
            $existingTheme = Theme::find($themeIdToCopy);

            if ($existingTheme) {
                $theme->settings = $existingTheme->settings;
                $theme->css = $existingTheme->css;
                $theme->css_preview = $existingTheme->css_preview;
                $theme->custom_css = $existingTheme->custom_css;

                $theme->save();
            }
        }

        return $theme;
    }

    public static function defaultThemeId(): string
    {
        return 'default';
    }

    public function generateMobileLogo(): string
    {
        $height = $this->setting('logo_height');
        $padding = $this->setting('logo_padding');

        if ($height > 75) {
            $height = 75;
        }
        if ($padding > ($height / 4)) {
            $padding = 15;
        }

        return '@media (max-width: 414px) {.logo {padding: ' . $padding . 'px;} .logo__img {max-height: ' . $height . 'px;}}';
    }

    public function customCss(): string
    {
        return request()->cookie('preview_css')
            ? $this->css_preview
            : $this->css;
    }

    public function getThemePath(): string
    {
        return base_path('public/themes/' . $this->view_path);
    }
}
