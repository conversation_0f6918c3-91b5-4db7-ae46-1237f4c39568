<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

/**
 * App\Models\ApiKey
 *
 * @property int $id
 * @property int|null $created_by_id
 * @property string|null $name
 * @property string|null $description
 * @property string $prefix
 * @property string $key
 * @property string|null $scope
 * @property string|null $last_used_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @method static \Illuminate\Database\Eloquent\Builder|ApiKey newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ApiKey newQuery()
 * @method static \Illuminate\Database\Query\Builder|ApiKey onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|ApiKey query()
 * @method static \Illuminate\Database\Eloquent\Builder|ApiKey whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ApiKey whereCreatedById($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ApiKey whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ApiKey whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ApiKey whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ApiKey whereKey($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ApiKey whereLastUsedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ApiKey whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ApiKey wherePrefix($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ApiKey whereScope($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ApiKey whereUpdatedAt($value)
 * @method static \Illuminate\Database\Query\Builder|ApiKey withTrashed()
 * @method static \Illuminate\Database\Query\Builder|ApiKey withoutTrashed()
 * @method static \Database\Factories\ApiKeyFactory factory($count = null, $state = [])
 * @mixin \Eloquent
 */
class ApiKey extends Model
{
    use HasFactory;

    use SoftDeletes;
    protected $table = 'api_keys';
    protected $guarded = [];

    public function generateKey($rawKey = null)
    {
        $apiKey = Str::random(24);
        $this->prefix = Str::substr($apiKey, 0, 5);
        $this->key = Hash::make($apiKey);

        if (ApiKey::where('prefix', $this->prefix)->first()) {
            $this->generateKey($rawKey);
        } else {
            $this->save();
            return $apiKey;
        }
    }

    public function hasScope($lookupKey)
    {
        if (empty($this->scope)) {
            return false;
        }
        $scope = is_array($this->scope) ? $this->scope : json_decode($this->scope, true);
        return (bool) collect($scope)->first(function ($value, $key) use ($lookupKey) {
            return $key === $lookupKey && $value === true;
        });
    }

    public function doesNotHaveScope($lookupKey)
    {
        return !$this->hasScope($lookupKey);
    }

    public function setScopeAttribute(array $value)
    {
        if (empty($value)) {
            $this->attributes['scope'] = null;
            return $this;
        }

        $jsonPayload = collect($this->scope)
            ->merge($value)
            ->toJson();

        $this->attributes['scope'] = $jsonPayload;
        return $this;
    }

    public function getScopeAttribute($value)
    {
        return is_null($value) || !strlen($value)
            ? json_decode('{}', true)
            : json_decode($value, true);
    }
}
