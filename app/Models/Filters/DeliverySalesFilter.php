<?php

namespace App\Models\Filters;

use App\Traits\DateRangeTrait;
use EloquentFilter\ModelFilter;
use Illuminate\Database\Query\Builder;

class DeliverySalesFilter extends ModelFilter
{
    use DateRangeTrait;

    public function setup(): void
    {
        $this->where('orders.confirmed', true)
            ->where('orders.canceled', false);
    }

    public function sort($direction): void
    {
        $this->when( ! empty($this->input('orderBy')), function ($query) use ($direction) {
            return $query->orderBy(
                column: $this->input('orderBy'),
                direction: in_array($direction, ['asc', 'desc']) ? $direction : 'asc'
            );
        })
            ->orderBy('pickup_title');
    }

    public function pickup(array $pickup_ids): void
    {
        $this->whereIn('orders.pickup_id', $pickup_ids);
    }

    public function schedule(array $pickup_ids): void
    {
        $this->whereIn('pickups.schedule_id', $pickup_ids);
    }

    public function orderStatus(array $statuses): void
    {
        $this->whereIn('orders.status_id', $statuses);
    }

    public function confirmedDate($date): void
    {
        $range = $this->getDateRange($date);

        if ($start = $range->get('start')) {
            $this->where('orders.confirmed_date', '>=', $start);
        }
        if ($end = $range->get('end')) {
            $this->where('orders.confirmed_date', '<=', $end);
        }
    }

    public function pickupDate($date): void
    {
        $range = $this->getDateRange($date);

        if ($start = $range->get('start')) {
            $this->where('orders.pickup_date', '>=', $start);
        }
        if ($end = $range->get('end')) {
            $this->where('orders.pickup_date', '<=', $end);
        }
    }

    public function orderType(array $types): void
    {
        $this->whereIn('orders.type_id', $types);
    }

    public function firstTimeOrder($first_time_order): void
    {
        $this->where('orders.first_time_order', (bool) $first_time_order);
    }

    public function fulfillmentError($fulfillment_error): void
    {
        $this->where('orders.fulfillment_error', (bool) $fulfillment_error);
    }

    public function orderTags(array $tags): void
    {
        $this->whereIn('orders.id', function (Builder $query) use ($tags) {
            $query->select('order_id')
                ->from('order_tag')
                ->whereIn('tag_id', $tags);
        });
    }
}
