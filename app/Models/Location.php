<?php

namespace App\Models;

use App\Presenters\LocationPresenter;
use App\Traits\MapMarkerContentTrait;
use <PERSON><PERSON>brock\EloquentSluggable\Sluggable;
use Exception;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Laracasts\Presenter\PresentableTrait;

/**
 * App\Models\Models\Location
 *
 * @property int $id
 * @property string $title
 * @property string $subtitle
 * @property string $slug
 * @property string $type
 * @property string $description
 * @property string|null $hours_of_operation
 * @property string|null $cover_photo
 * @property string|null $street
 * @property string|null $street_2
 * @property string|null $city
 * @property string|null $state
 * @property string|null $zip
 * @property string $country
 * @property float|null $lat
 * @property float|null $lng
 * @property string|null $marker_color
 * @property string|null $marker_icon
 * @property string|null $contact_name
 * @property string|null $contact_phone
 * @property string|null $contact_email
 * @property string|null $contact_website
 * @property int $visible
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read string $map_marker_content
 * @method static \Illuminate\Database\Eloquent\Builder|Location findSimilarSlugs(string $attribute, array $config, string $slug)
 * @method static \Illuminate\Database\Eloquent\Builder|Location newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Location newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Location query()
 * @method static \Illuminate\Database\Eloquent\Builder|Location whereCity($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Location whereContactEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Location whereContactName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Location whereContactPhone($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Location whereContactWebsite($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Location whereCountry($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Location whereCoverPhoto($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Location whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Location whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Location whereHoursOfOperation($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Location whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Location whereLat($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Location whereLng($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Location whereMarkerColor($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Location whereMarkerIcon($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Location whereSlug($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Location whereState($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Location whereStreet($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Location whereStreet2($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Location whereSubtitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Location whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Location whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Location whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Location whereVisible($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Location whereZip($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Location withUniqueSlugConstraints(\Illuminate\Database\Eloquent\Model $model, string $attribute, array $config, string $slug)
 * @method static \Database\Factories\LocationFactory factory($count = null, $state = [])
 * @mixin \Eloquent
 */
class Location extends Model
{
    use HasFactory, PresentableTrait, Sluggable, MapMarkerContentTrait;

    /**
      * Return the sluggable configuration array for this model.
      */
    public function sluggable(): array
    {
        return [
            'slug' => [
                'source' => 'title'
            ]
        ];
    }

    protected $guarded = [];

    protected $presenter = LocationPresenter::class;

    /**
     * Make a new location
     * @throws Exception
     */
    public static function add(Request $request): static
    {
        $location = new static;

        $location->fill($request->all());

        $geocoded_address = geocoder()->fromAddressParts($request->toAddressParts());

        $location->lat = $geocoded_address->lat;
        $location->lng = $geocoded_address->lng;

        $location->save();

        if ($geocoded_address->isInaccurate()) {
            error('We were not able to get a very accurate geo coordinates for this location.
            You might need to adjust the latitude and longitude manually under settings.');
        }

        return $location;
    }
}
