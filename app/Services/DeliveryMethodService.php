<?php

namespace App\Services;

use App\Models\Pickup;
use App\Services\Geocoding\GeocodedAddress;
use App\Support\Enums\PickupStatus;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use Illuminate\Support\Collection;

class DeliveryMethodService
{
    protected ?int $method_type = null;

    protected array $required_product_ids = [];

    /**
     * @return EloquentCollection<int, Pickup>
     */
    public function find(GeocodedAddress $address): EloquentCollection
    {
        return Pickup::query()
            ->select([
                'id', 'title', 'display_name', 'slug', 'fulfillment_type', 'status_id',
                'delivery_rate', 'street', 'city', 'state', 'zip', 'settings', 'schedule_id'
            ])
            ->whereIn(
                'id',
                $this->findIdsByPostalCode($address)
                    ->merge($this->findIdsByState($address))
                    ->merge($this->findIdsByCoordinates($address))
                    ->unique()
            )
            ->get();
    }

    /**
     * @return Collection<int, int>
     */
    private function findIdsByPostalCode(GeocodedAddress $address): Collection
    {
        if (empty($address->postalCode)) {
            return collect();
        }

        return $this->baseQuery()
            ->whereHas('zips', fn($query) => $query->where('zip', $address->postalCode))
            ->pluck('id');
    }

    private function baseQuery()
    {
        return Pickup::query()
            ->where('visible', true)
            ->where('status_id', PickupStatus::open())
            ->when( ! is_null($this->method_type), fn($query) =>
                $query->where('fulfillment_type', $this->method_type)
            )
            ->when( ! empty($this->required_product_ids), fn($query) =>
                $query->whereDoesntHave('products', fn($query) =>
                    $query->whereIn('products.id', $this->required_product_ids)
                )
            );
    }

    /**
     * @return Collection<int, int>
     */
    private function findIdsByState(GeocodedAddress $address): Collection
    {
        if (empty($address->state)) {
            return collect();
        }

        return $this->baseQuery()
            ->whereHas('states', fn($query) => $query->where('state', $address->state))
            ->pluck('id');
    }

    /**
     * @return Collection<int, int>
     */
    private function findIdsByCoordinates(GeocodedAddress $address): Collection
    {
        return $this->baseQuery()
            ->select(['id'])
            ->where('fulfillment_type', Pickup::FULFILLMENT_TYPE_PICKUP)
            ->coordinates($address->lat, $address->lng)
            ->having('distance', '<=', setting('pickup_results_radius', 400))
            ->orderBy('distance')
            ->limit(setting('pickup_results_count', 100))
            ->pluck('id');
    }

    public function pickupLocations(): DeliveryMethodService
    {
        $this->method_type = Pickup::FULFILLMENT_TYPE_PICKUP;
        return $this;
    }

    public function deliveryZones(): DeliveryMethodService
    {
        $this->method_type = Pickup::FULFILLMENT_TYPE_DELIVERY;
        return $this;
    }

    public function hasProducts(array $product_ids): DeliveryMethodService
    {
        $this->required_product_ids = $product_ids;
        return $this;
    }
}
