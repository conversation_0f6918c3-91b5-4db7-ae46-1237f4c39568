<?php

namespace App\Services;

use Picqer\Barcode\Renderers\SvgRenderer;
use Picqer\Barcode\Types\TypeCode128;

class BarcodeService
{
    public function generateAsSvg($value, $width = 180, $height = 30): string
    {
        $renderer = new SvgRenderer;
        $renderer->setSvgType($renderer::TYPE_SVG_INLINE);

        return $renderer->render(
            barcode: (new TypeCode128)->getBarcode($value),
            width: $width,
            height: $height
        );
    }
}
