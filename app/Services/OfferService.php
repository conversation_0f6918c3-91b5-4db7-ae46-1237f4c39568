<?php

namespace App\Services;

use App\Cart\Offer;
use App\Contracts\Cartable;
use Illuminate\Support\Collection;

class OfferService
{
    /**
     * @return Collection<int, Offer>
     */
    public function forCart(Cartable $cart): Collection
    {
        $offers = collect();

        if ($cart->hasSubscriptionEligibleItems()) {
            $offers->push(
                new Offer(Offer::SUBSCRIPTION, $cart)
            );
        }

        return $offers;
    }

    public function complete(Cartable $cart, string $offer, array $params = []): Cartable
    {
        return match($offer) {
            Offer::SUBSCRIPTION => $this->completeSubscriptionOffer($cart, $params),
            default => $cart
        };
    }

    protected function completeSubscriptionOffer(Cartable $cart, array $params): Cartable
    {
        return match($params['purchase_type']) {
            'recurring' => $cart->setCartAsSubscriptionPurchase($params['frequency'], $params['product_incentive_id']),
            default => $cart->setCartAsOneTimePurchase()
        };
    }
}