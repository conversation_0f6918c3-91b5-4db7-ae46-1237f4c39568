<?php

namespace App\Services;

use App\Models\Date;
use App\Models\GiftCertificate;
use App\Models\Order;
use App\Models\Pickup;
use App\Models\RecurringOrder;
use App\Models\Setting;
use App\Models\Template;
use App\Models\User;
use Illuminate\Support\Collection;

class EmailTemplateBuilder
{
    public array $mergeVariables = [];

    private string $html;

    private string $text;

    public function __construct(
        public Template $template
    ) {
        $this->mergeVariables = array_merge($this->mergeVariables, $this->compileFarmTags());
    }

    private function compileFarmTags(): array
    {
        $settings = Setting::farmSettings()->pluck('value', 'key');

        return [
            '{farm_name}' => $this->getSettingByName($settings, 'farm_name'),
            '{farm_phone}' => $this->getSettingByName($settings, 'farm_phone'),
            '{farm_street}' => $this->getSettingByName($settings, 'farm_street'),
            '{farm_city}' => $this->getSettingByName($settings, 'farm_city'),
            '{farm_state}' => $this->getSettingByName($settings, 'farm_state'),
            '{farm_zip}' => $this->getSettingByName($settings, 'farm_zip'),
            '{farm_postal_code}' => $this->getSettingByName($settings, 'farm_zip'),
            '{farm_address}' => $this->getSettingByName($settings, 'farm_street') . ', ' . $this->getSettingByName($settings, 'farm_city') . ', ' . $this->getSettingByName($settings, 'farm_state') . ' ' . $this->getSettingByName($settings, 'farm_zip'),
            '{site_url}' => config('app.url') ?? "",
        ];
    }

    /**
     * @param  Collection<string, mixed> $settings
     * @param  string  $attribute
     * @return mixed
     */
    private function getSettingByName(Collection $settings, string $attribute): mixed
    {
        return $settings[$attribute] ?? null;
    }

    public function getHTML(): string
    {
        return $this->html;
    }

    public function getText(): string
    {
        return $this->text;
    }

    public function mergeTags(string $field = 'body'): EmailTemplateBuilder
    {
        $this->html = str_ireplace(
            array_keys($this->mergeVariables),
            array_values($this->mergeVariables),
            $this->template[$field]
        );

        $this->text = str_ireplace(
            array_keys($this->mergeVariables),
            array_values($this->mergeVariables),
            $this->template['plain_text']
        );

        return $this;
    }

    public function giftCard(GiftCertificate $gift_card): EmailTemplateBuilder
    {
        $this->mergeVariables = array_merge($this->mergeVariables, $this->compileGiftCardTags($gift_card));
        $this->mergeVariables = array_merge($this->mergeVariables, $this->compilePickupTags($gift_card->orderItem->order->pickup));
        $this->mergeVariables = array_merge($this->mergeVariables, $this->compileUserTags($gift_card->orderItem->order->customer));
        $this->mergeVariables = array_merge($this->mergeVariables, $this->compileOrderTags($gift_card->orderItem->order));

        return $this;
    }

    private function compileGiftCardTags(GiftCertificate $gift_card): array
    {
        return [
            '{gift_card_code}' => $gift_card->code,
            '[new_gift_card_code_tag]' => $gift_card->code, // Supports legacy default template
            '{gift_card_amount}' => '$'. money($gift_card->amount),
            '{gift_card_sender}' => $gift_card->orderItem->order->routeNotificationForMail(),
            '{gift_card_redemption_url}' => config('app.url') ?? '',
        ];
    }

    private function compilePickupTags(?Pickup $pickup = null, ?Date $date = null): array
    {
        if (!$pickup) {
            return [
                '{customer_first_name}' => '%recipient.first_name%',
                '{customer_last_name}' => '%recipient.last_name%',
                '{customer_name}' => '%recipient.full_name%',
                '{customer_full_name}' => '%recipient.full_name%',
                '{customer_credit}' => '%recipient.credit%',
                '{pickup_title}' => 'N/A',
                '{pickup_slug}' => 'N/A',
                '{pickup_subtitle}' => 'N/A',
                '{pickup_street}' => 'N/A',
                '{pickup_street_2}' => 'N/A',
                '{pickup_city}' => 'N/A',
                '{pickup_state}' => 'N/A',
                '{pickup_zip}' => 'N/A',
                '{pickup_address}' => 'N/A',
                '{pickup_times}' => 'N/A',
                '{pickup_deadline}' => 'N/A',
                '{pickup_next_date}' => 'N/A',
            ];
        }

        if (is_null($date)) {
            $deadlineDate = is_null($pickup->nextDate) ? 'N/A' : $pickup->nextDate->order_end_date->format('D, M jS');
            $pickupDate = is_null($pickup->nextDate) ? 'N/A' : $pickup->nextDate->pickup_date->format('D, M jS');
        } else {
            $deadlineDate = $date->order_end_date->format('D, M jS');
            $pickupDate = $date->pickup_date->format('D, M jS');
        }

        return [
            '{customer_first_name}' => '%recipient.first_name%',
            '{customer_last_name}' => '%recipient.last_name%',
            '{customer_name}' => '%recipient.full_name%',
            '{customer_full_name}' => '%recipient.full_name%',
            '{customer_credit}' => '%recipient.credit%',
            '{pickup_title}' => $pickup->present()->title(),
            '{pickup_slug}' => $pickup->slug,
            '{pickup_subtitle}' => $pickup->subtitle,
            '{pickup_street}' => $pickup->street,
            '{pickup_street_2}' => $pickup->street_2,
            '{pickup_city}' => $pickup->city,
            '{pickup_state}' => $pickup->state,
            '{pickup_zip}' => $pickup->zip,
            '{pickup_address}' => $pickup->present()->fullAddress(),
            '{pickup_times}' => $pickup->pickup_times,
            '{pickup_deadline}' => $deadlineDate,
            '{pickup_next_date}' => $pickupDate
        ];
    }

    private function compileUserTags(User $user): array
    {
        return [
            '{customer_first_name}' => $user->first_name ?? '',
            '{customer_last_name}' => $user->last_name ?? '',
            '{customer_name}' => $user->full_name ?? '',
            '{customer_email}' => $user->email ?? '',
            '{customer_phone}' => $user->phone ?? '',
            '{customer_street}' => $user->street ?? '',
            '{customer_street_2}' => $user->street_2 ?? '',
            '{customer_city}' => $user->city ?? '',
            '{customer_state}' => $user->state ?? '',
            '{customer_zip}' => $user->zip ?? '',
            '{customer_billing_street}' => $user->billing_street ?? '',
            '{customer_billing_street_2}' => $user->billing_street_2 ?? '',
            '{customer_billing_city}' => $user->billing_city ?? '',
            '{customer_billing_state}' => $user->billing_state ?? '',
            '{customer_billing_zip}' => $user->billing_zip ?? '',
            '{customer_pickup_title}' => $user->pickup['title'] ?? '',
            '{customer_credit}' => money($user->credit),
        ];
    }

    private function compileOrderTags(Order $order): array
    {
        $nextDeadlineDate = '';
        $nextDeliveryDate = '';

        $recurringOrderBlueprint = RecurringOrder::find($order->blueprint_id);

        if ($recurringOrderBlueprint && $recurringOrderBlueprint->currentOrder) {
            $nextDeadlineDate = $recurringOrderBlueprint->currentOrder->deadline_date ?? '';
            $nextDeliveryDate = $recurringOrderBlueprint->currentOrder->pickup_date ?? '';
        }

        return [
            '{payment_message}' => $order->paymentMethod ? $order->paymentMethod->instructions : '',
            '{payment_instructions}' => $order->paymentMethod ? $order->paymentMethod->instructions : '',
            '{payment_method}' => $order->paymentMethod ? $order->paymentMethod->title : '',
            '{payment_status}' => $order->paid ? 'Paid' : 'Not Paid',
            '{customer_name}' => $order->customer_first_name . ' ' . $order->customer_last_name,
            '{order_name}' => $order->customer_first_name . ' ' . $order->customer_last_name,
            '{order_first_name}' => $order->customer_first_name,
            '{order_last_name}' => $order->customer_last_name,
            '{shipping_address}' => $order->shipping_street . '<br>' . $order->shipping_street_2 . '<br>' . $order->shipping_city . ', ' . $order->shipping_state . ' ' . $order->shipping_zip,
            '{order_shipping_address}' => $order->shipping_street . '<br>' . $order->shipping_city . ', ' . $order->shipping_state . ' ' . $order->shipping_zip,
            '{order_total}' => money($order->total),
            '{order_subtotal}' => money($order->subtotal),
            '{order_total_due}' => money($order->total_due),
            '{order_payments_subtotal}' => money($order->payments_subtotal),
            '{order_tax}' => money($order->tax),
            '{order_fees}' => money($order->fees_subtotal),
            '{order_weight}' => $order->weight,
            '{order_discount}' => money($order->order_discount),
            '{order_coupons}' => money($order->coupon_subtotal),
            '{order_credit}' => money($order->credit_applied),
            '{order_discount_subtotal}' => money($order->order_discount + $order->credit_applied + $order->coupon_subtotal),
            '{order_subscription_savings}' => money($order->subscription_savings),
            '{order_delivery_rate}' => money($order->delivery_rate),
            '{order_delivery_fee}' => money($order->delivery_fee),
            '{order_number}' => $order->id,
            '{order_date}' => $order->present()->confirmedDate(),
            '{order_items}' => view('emails.partials.order-items')->with(compact('order'))->render(),
            '{order_items_text}' => view('emails.partials.order-items-plain-text')->with(compact('order'))->render(),
            '{order_payments}' => view('emails.partials.order-payments')->with(compact('order'))->render(),
            '{order_payments_text}' => view('emails.partials.order-payments-text')->with(compact('order'))->render(),
            '{order_summary}' => view('emails.partials.order-summary')->with(compact('order'))->render(),
            '{order_summary_text}' => view('emails.partials.order-summary-text')->with(compact('order'))->render(),
            '{order_invoice_notes}' => $order->invoice_notes,
            '{order_customer_notes}' => $order->customer_notes,
            '{order_packing_notes}' => $order->packing_notes,
            '{pickup_date}' => $order->pickup_date ? $order->present()->pickupDate('M j, Y') : 'N/A',
            '{order_pickup_date}' => $order->pickup_date ? $order->present()->pickupDate('M j, Y') : 'N/A',
            '{order_deadline}' => $order->deadline_date ? $order->deadline_date->toFormattedDateString() : 'N/A',
            '{order_deadline_date}' => $order->deadline_date ? $order->deadline_date->toFormattedDateString() : 'N/A',
            '{delivery_method}' => $order->pickup->isDeliveryZone() ? 'delivery' : 'pickup',
            '{delivery_type}' => $order->pickup->isDeliveryZone() ? 'delivery' : 'pickup',
            '{order_deadline_end_time}' => $order->deadline_date ? $order->deadlineEndTime() : 'N/A',

            '{reorder_frequency}' => $recurringOrderBlueprint ? __('messages.recurring.reorder_frequencies_formatted.' . $recurringOrderBlueprint->reorder_frequency) : 'N/A',
            '{next_reorder_deadline_date}' => $nextDeadlineDate ? $nextDeadlineDate->toFormattedDateString() : 'N/A',
            '{next_reorder_pickup_date}' => $nextDeliveryDate ? $nextDeliveryDate->toFormattedDateString() : 'N/A'
        ];
    }

    public function user(User $user): static
    {
        $this->mergeVariables = array_merge($this->mergeVariables, $this->compileUserTags($user));
        return $this;
    }

    public function pickup(Pickup $pickup): EmailTemplateBuilder
    {
        $this->mergeVariables = array_merge($this->mergeVariables, $this->compilePickupTags($pickup));
        return $this;
    }

    /**
     * @param  Order  $order
     * @param  Collection<string, mixed>  $data
     * @return EmailTemplateBuilder
     */
    public function custom(Order $order, Collection $data): EmailTemplateBuilder
    {
        $this->order($order);
        $this->mergeVariables = array_merge($this->mergeVariables, $this->compileCustomTags($data));

        return $this;
    }

    public function order(Order $order): static
    {
        $this->mergeVariables = array_merge($this->mergeVariables, $this->compilePickupTags($order->pickup));
        $this->mergeVariables = array_merge($this->mergeVariables, $this->compileUserTags($order->customer));
        $this->mergeVariables = array_merge($this->mergeVariables, $this->compileOrderTags($order));

        return $this;
    }

    /**
     * @param  Collection<string, mixed>  $data
     */
    public function compileCustomTags(Collection $data): array
    {
        return [
            '{email_subject}' => $data->get('subject'),
            '{custom_message}' => $this->mergeCustomMessage($data->get('message')),
        ];
    }

    public function mergeCustomMessage(string $message): string
    {
        return str_ireplace(
            array_keys($this->mergeVariables),
            array_values($this->mergeVariables),
            $message
        );
    }
}
