<?php

namespace App\Services\Geocoding;

use App\Contracts\Geocoder;
use App\Exceptions\NoGeocodeResultsException;
use Bugsnag\BugsnagLaravel\Facades\Bugsnag;
use Illuminate\Http\Client\RequestException;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Http;

class GoogleGeocoder implements Geocoder
{
    protected string $baseUrl = 'https://maps.googleapis.com/maps/api/geocode/json?';

    /**
     * @throws NoGeocodeResultsException
     */
    public function fromAddress(string $address): GeocodedAddress
    {
        try {
            $response = Http::get($this->baseUrl, [
                'address' => $this->formatAddress($address),
                'key' => $this->apiKey(),
            ])
                ->throw();
        } catch (RequestException $exception) {
            Bugsnag::notifyException($exception);
            throw new NoGeocodeResultsException;
        }

        return $this->parseResponse($response->json());
    }

    private function formatAddress(string|array $address): string
    {
        return trim(
            collect(Arr::wrap($address))
                ->map(fn($value) => trim($value))
                ->implode(', ')
        );
    }

    private function apiKey(): ?string
    {
        return config('services.google.geocoder_api_key');
    }

    /**
     * @throws NoGeocodeResultsException
     */
    private function parseResponse(array $response, bool $requirePostalCode = false) : GeocodedAddress
    {
        if (empty($response)) {
            throw new NoGeocodeResultsException('Unknown GoogleGeocode failure. No response.');
        }

        if (isset($response['status']) && $response['status'] === 'ZERO_RESULTS') {
            throw new NoGeocodeResultsException('No results found.');
        }

        $result = $response['results'][0];
        $postalCodeComponent = $this->findAddressComponent('postal_code', $result['address_components']);

        if ($requirePostalCode && is_null($postalCodeComponent)) {
            throw new NoGeocodeResultsException('No results found.');
        }

        $cityComponent = $this->findAddressComponent('locality', $result['address_components'])
            ?? $this->findAddressComponent('neighborhood', $result['address_components'])
            ?? $this->findAddressComponent('administrative_area_level_3', $result['address_components'])
            ?? $this->findAddressComponent('sublocality_level_1', $result['address_components'])
            ?? throw new NoGeocodeResultsException('Unable to locate the city. Try again.');

        $stateComponent = $this->findAddressComponent('administrative_area_level_1', $result['address_components']);

        $countryComponent = $this->findAddressComponent('country', $result['address_components']);

        return new GeocodedAddress(
            lat: $result['geometry']['location']['lat'],
            lng: $result['geometry']['location']['lng'],
            city: $cityComponent['long_name'] ?? null,
            state: $stateComponent['short_name'] ?? null,
            postalCode: $postalCodeComponent['long_name'] ?? null,
            country: $countryComponent['short_name'] ?? null,
            accuracy: $result['geometry']['location_type'] === 'ROOFTOP' ? 1 : 0.8
        );
    }

    private function findAddressComponent(string $component_type, array $address_components): ?array
    {
        return collect($address_components)
            ->first(fn(array $component) => in_array($component_type, $component['types'] ?? []));
    }

    /**
     * @throws NoGeocodeResultsException
     */
    public function fromZipcode(string $zip): GeocodedAddress
    {
        try {
            $response = Http::get($this->baseUrl, [
                'address' => $this->formatAddress($zip),
                'key' => $this->apiKey(),
            ])
                ->throw();
        } catch (RequestException $exception) {
            Bugsnag::notifyException($exception);
            throw new NoGeocodeResultsException;
        }

        return $this->parseResponse($response->json(), true);
    }

    /**
     * @throws NoGeocodeResultsException
     */
    public function fromAddressParts(array $addressParts = []): GeocodedAddress
    {
        try {
            $response = Http::get($this->baseUrl, [
                'address' => $this->formatAddress($addressParts),
                'key' => $this->apiKey(),
            ])
                ->throw();
        } catch (RequestException $exception) {
            Bugsnag::notifyException($exception);
            throw new NoGeocodeResultsException;
        }

        return $this->parseResponse($response->json());
    }
}
