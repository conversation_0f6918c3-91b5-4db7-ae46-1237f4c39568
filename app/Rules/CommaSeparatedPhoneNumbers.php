<?php

namespace App\Rules;

use Closure;
use Exception;
use Illuminate\Contracts\Validation\ValidationRule;
use libphonenumber\PhoneNumberUtil;

class CommaSeparatedPhoneNumbers implements ValidationRule
{
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $phoneNumbers = array_map('trim', explode(',', $value));

        foreach ($phoneNumbers as $phoneNumber) {
            $util = PhoneNumberUtil::getInstance();

            $is_valid = false;

            try {
                $is_valid = $util->isValidNumber($util->parse($phoneNumber, 'US'));
            } catch (Exception $exception) {}

            if ( ! $is_valid) {
                $fail('The entered phone number is invalid.');
            }
        }
    }
}
