<?php

namespace App\Cart\Validators;

use App\Contracts\Cartable;
use App\Models\Product;

class HasAvailableProductQuantities
{
    /**
     * @throws CartValidationException
     */
    public function handle(Cartable $cart, \Closure $next): Cartable
    {
        $items = $cart->cartItemsWithoutAvailableInventory();

        if ($items->isNotEmpty()) {
            throw new CartValidationException(
                rule: HasAvailableProductQuantities::class,
                message: 'Not all of your selected products are available.',
                data: ['items' => $items]
            );
        }

        return $next($cart);
    }
}