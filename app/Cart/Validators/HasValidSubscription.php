<?php

namespace App\Cart\Validators;

use App\Contracts\Cartable;

class HasValidSubscription
{
    /**
     * @throws CartValidationException
     */
    public function handle(Cartable $cart, \Closure $next): Cartable
    {
        if ($cart->isRecurring() && ! $cart->hasValidSubscription()) {
            throw new CartValidationException(
                rule: HasValidSubscription::class,
                message: 'The subscription frequency is required.'
            );
        }

        return $next($cart);
    }
}