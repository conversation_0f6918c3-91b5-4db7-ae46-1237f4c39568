<?php

namespace App\Observers;

use App\Actions\UpdateSitemap;
use App\Models\Menu;
use App\Models\MenuItem;
use App\Models\Page;
use Illuminate\Support\Facades\Cache;

class PageObserver
{
    public function saving(Page $page): void
    {
        MenuItem::where('resource_type', 'page')
            ->where('resource_id', $page->id)
            ->update(['path' => $page->slug]);

        Cache::tags(['menu', 'sitemap'])->flush();

        (new UpdateSitemap)->execute();
    }

    public function updating(Page $page): void
    {
        if ($page->isDirty('slug')) {
            $page->flushCache();
        }
    }

    public function saved(Page $page): void
    {
        Menu::flushCache();
        $page->flushCache();
    }

    public function deleting(Page $page): void
    {
        if ($page->isDirty('slug')) {
            $page->flushCache();
        }

        MenuItem::where('resource_type', 'page')
            ->where('resource_id', $page->id)
            ->update(['path' => $page->slug]);

        Cache::tags(['menu', 'sitemap'])->flush();

        (new UpdateSitemap)->execute();
    }

    public function deleted(Page $page): void
    {
        Menu::flushCache();
        $page->flushCache();
    }
}
