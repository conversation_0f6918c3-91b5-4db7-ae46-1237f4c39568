<?php

namespace App\Observers;

use App\Actions\UpdateSitemap;
use App\Models\Post;
use Illuminate\Support\Facades\Cache;

class PostObserver
{
    public function updating(Post $post): void
    {
        if ($post->isDirty('published_at')) {
            $date = $post->published_at;
            $post->published_year = $date->year;
            $post->published_month = $date->month;
            $post->published_day = $date->day;
        }
    }

    public function saving(Post $post): void
    {
        Cache::tags('post')->flush();
        Cache::tags('sitemap')->flush();

        (new UpdateSitemap())->execute();
    }

    public function deleting(Post $post): void
    {
        Cache::tags('post')->flush();
        Cache::tags('sitemap')->flush();

        (new UpdateSitemap())->execute();
    }
}
