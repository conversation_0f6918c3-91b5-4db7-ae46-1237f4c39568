<?php

namespace App\API\V1\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ProductResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'slug' => $this->slug,
            'type_id' => $this->type_id,
            'fulfillment_id' => $this->fulfillment_id,
            'sku' => $this->sku,
            'barcode' => $this->barcode,
            'vendor_id' => $this->vendor_id,
            'unit_price' => $this->unit_price,
            'wholesale_unit_price' => $this->wholesale_unit_price,
            'sale_unit_price' => $this->sale_unit_price,
            'unit_of_issue' => $this->unit_of_issue,
            'unit_description' => $this->unit_description,
            'weight_in_pounds' => (float) $this->weight,
            'weight_in_ounces' => round(($this->weight * 16), 2),
            'item_cost' => $this->item_cost,
            'inventory_type' => $this->inventory_type,
            'qty_on_hand' => $this->inventory,
            'qty_at_processor' => $this->processor_inventory,
            'qty_off_site' => $this->other_inventory,
            'stock_out_threshold' => $this->stock_out_inventory,
            'is_sale' => (bool) $this->sale,
            'is_visible' => (bool) $this->visible,
            'is_bundle' => (bool) $this->is_bundle,
            'is_taxable' => (bool) $this->taxable,
            'created_at' => $this->created_at ? $this->created_at->toIso8601String() : null,
            'updated_at' => $this->updated_at ? $this->updated_at->toIso8601String() : null,
        ];
    }
}
