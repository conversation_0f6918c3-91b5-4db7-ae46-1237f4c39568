<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('order_items', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('order_id')->unsigned()->index();
            $table->integer('product_id')->unsigned()->index();
            $table->integer('user_id')->unsigned()->index();

            $table->string('title');
            $table->enum('unit_of_issue', ['package','weight'])->default('package');
            $table->integer('unit_price')->unsigned();
            $table->integer('original_unit_price')->unsigned();
            $table->integer('store_price')->unsigned();

            $table->integer('qty')->unsigned();
            $table->integer('original_qty')->unsigned();

            $table->decimal('weight', 12, 3);
            $table->decimal('original_weight', 12, 3);

            $table->integer('subtotal')->unsigned();

            $table->boolean('taxable')->default(false);
            $table->integer('tax')->unsigned();

            $table->integer('discount')->unsigned();
            $table->enum('discount_type', ['fixed','percent']);

            $table->enum('stock_status', ['full','short','out','back']);
            $table->integer('sort_stock')->unsigned();

            $table->unsignedSmallInteger('created_year');
            $table->unsignedTinyInteger('created_month');
            $table->unsignedTinyInteger('created_day');

            $table->timestamps();

            $table->foreign('order_id')->references('id')->on('orders')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::drop('order_items');
    }
};
