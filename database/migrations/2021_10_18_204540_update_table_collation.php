<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $current_database = config('database.connections.mysql.database');

        if(!$current_database) {
            info('No tenant database found. Skipping migration.');
            return;
        }

        // update default for future tables created
        DB::statement("ALTER DATABASE `$current_database` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");

        // update default for all existing tables in the DB
        $results = DB::select("SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_SCHEMA='$current_database'");
        foreach ($results as $result) {
            DB::statement("ALTER TABLE {$result->TABLE_NAME} CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        $current_database = config('database.connections.mysql.database');

        if(!$current_database) {
            info('No tenant database found. Skipping migration.');
            return;
        }

        // update default for future tables created
        DB::statement("ALTER DATABASE `$current_database` CHARACTER SET utf8 COLLATE utf8_unicode_ci");

        // update default for all existing tables in the DB
        $results = DB::select("SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_SCHEMA='$current_database'");
        foreach ($results as $result) {
            DB::statement("ALTER TABLE {$result->TABLE_NAME} CONVERT TO CHARACTER SET utf8 COLLATE utf8_unicode_ci");
        }
    }
};
