<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'card_last_four',
                'card_brand',
                'card_exp_month',
                'card_exp_year',
                'card_name',
                'stax_customer_id',
                'payfac_customer_id'
            ]);
        });
    }
};
