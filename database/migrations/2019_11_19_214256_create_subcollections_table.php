<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('subcollections', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('collection_id')->unsigned();
            $table->integer('tag_id')->unsigned();
            $table->string('title');
            $table->text('description');
            $table->boolean('visible')->default(true);
            $table->string('cover_photo')->nullable();
            $table->text('settings')->nullable();
            $table->text('footer_description')->nullable();
            $table->boolean('seo_visibility')->default(true);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('subcollections');
    }
};
