<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('refunds', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('order_id')->unsigned();
            $table->integer('payment_id')->unsigned();
            $table->integer('admin_id')->unsigned();
            $table->string('refund_id')->nullable();
            $table->integer('amount')->unsigned()->default(0);
            $table->string('charge_id')->nullable();
            $table->string('reason', 600)->nullable();
            $table->string('status', 32)->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('refunds');
    }
};
