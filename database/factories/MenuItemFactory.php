<?php

namespace Database\Factories;

use App\Models\Menu;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class MenuItemFactory extends Factory
{
    public function definition(): array
    {
        return [
            'menu_id' => Menu::factory(),
            'submenu_id' => 0,
            'title' => $title = $this->faker->unique()->md5(),
            'path' =>  Str::slug($title),
        ];
    }
}
