<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Webhook>
 */
class WebhookFactory extends Factory
{
    public function definition(): array
    {
        return [
            'topic' => 'tests.store',
            'target_url' => 'https://test.com/webhook',
            'settings' => ['foo' => 'bar']
        ];
    }
}
