<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use App\Models\Product;
use App\Models\ProductPrice;
use App\Models\ProductPriceGroup;

class ProductPriceFactory extends Factory
{
    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'group_id' => ProductPriceGroup::factory(),
            'product_id' =>  Product::factory(),
            'unit_price' => $this->faker->randomNumber(4),
            'sale_unit_price' => $this->faker->randomNumber(4),
            'unit_of_issue' => $this->faker->randomElement(['package', 'weight']),
            'unit_description' => $this->faker->sentence(),
            'weight' => $this->faker->randomFloat(3),
        ];
    }
}
