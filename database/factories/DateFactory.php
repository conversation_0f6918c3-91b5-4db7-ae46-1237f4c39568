<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use App\Models\Date;

class DateFactory extends Factory
{
    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'order_start_date' => today()->subDays(7)->format('Y-m-d'),
            'order_end_date' => today()->addDays(3)->format('Y-m-d'),
            'pickup_date' => today()->addDays(5)->format('Y-m-d'),
            'active' => true,
            'reminder_sent' => false,
            'secondary_reminder_sent' => false
        ];
    }
}
