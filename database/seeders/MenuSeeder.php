<?php

namespace Database\Seeders;

use Illuminate\Support\Str;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class MenuSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->seedMenus();
        $this->seedMenuItems();
    }

    /**
     *  Build menus
     */
    private function seedMenus()
    {
        $date = date('Y-m-d H:i:s');
        DB::table('menus')->truncate();

        DB::table('menus')->insert([
            [
                'id' => 1,
                'title' => 'Main Menu',
                'name' => 'main',
                'submenu' => false,
                'parent_id' => 0,
                'created_at' => $date,
                'updated_at' => $date
            ],
            [
                'id' => 2,
                'title' => 'Store Menu',
                'name' => 'store',
                'submenu' => false,
                'parent_id' => 0,
                'created_at' => $date,
                'updated_at' => $date
            ],
            [
                'id' => 3,
                'title' => 'Footer Menu',
                'name' => 'footer',
                'submenu' => false,
                'parent_id' => 0,
                'created_at' => $date,
                'updated_at' => $date
            ]
        ]);
    }

    private function seedMenuItems()
    {
        $date = date('Y-m-d H:i:s');
        DB::table('menu_items')->truncate();
        DB::table('menu_items')->insert([
            [
                'id' => 1,
                'title' => 'Shop Now',
                'path' => '/store',
                'menu_id' => 1,
                'submenu_id' => 0,
                'sort' => 1,
                'type' => 'page',
                'created_at' => $date,
                'updated_at' => $date,
                'resource_type' => '',
                'resource_id' => 0
            ],
            [
                'id' => 4,
                'title' => 'About Us',
                'path' => Str::slug('About Us'),
                'menu_id' => 1,
                'submenu_id' => 0,
                'sort' => 4,
                'type' => 'page',
                'created_at' => $date,
                'updated_at' => $date,
                'resource_type' => 'page',
                'resource_id' => 2,
            ],
            [
                'id' => 5,
                'title' => 'Contact',
                'path' => '/contact',
                'menu_id' => 1,
                'submenu_id' => 0,
                'sort' => 5,
                'type' => 'page',
                'created_at' => $date,
                'updated_at' => $date,
                'resource_type' => '',
                'resource_id' => 0
            ],
            [
                'id' => 6,
                'title' => 'Shop Now',
                'path' => '/store',
                'menu_id' => 3,
                'submenu_id' => 0,
                'sort' => 1,
                'type' => 'page',
                'created_at' => $date,
                'updated_at' => $date,
                'resource_type' => '',
                'resource_id' => 0
            ],
            [
                'id' => 7,
                'title' => 'Pickup Locations',
                'path' => '/locations',
                'menu_id' => 3,
                'submenu_id' => 0,
                'sort' => 2,
                'type' => 'page',
                'created_at' => $date,
                'updated_at' => $date,
                'resource_type' => '',
                'resource_id' => 0
            ],
            [
                'id' => 8,
                'title' => 'Blog',
                'path' => '/blog',
                'menu_id' => 3,
                'submenu_id' => 0,
                'sort' => 3,
                'type' => 'page',
                'created_at' => $date,
                'updated_at' => $date,
                'resource_type' => '',
                'resource_id' => 0
            ],
            [
                'id' => 9,
                'title' => 'About Us',
                'path' => '/about-us',
                'menu_id' => 3,
                'submenu_id' => 0,
                'sort' => 4,
                'type' => 'page',
                'created_at' => $date,
                'updated_at' => $date,
                'resource_type' => 'page',
                'resource_id' => 2
            ],
            [
                'id' => 10,
                'title' => 'Contact',
                'path' => '/contact',
                'menu_id' => 3,
                'submenu_id' => 0,
                'sort' => 5,
                'type' => 'page',
                'created_at' => $date,
                'updated_at' => $date,
                'resource_type' => '',
                'resource_id' => 0
            ]
        ]);

        $collections = DB::table('collections')->get();
        $count = 1;
        foreach ($collections as $collection) {
            DB::table('menu_items')->insert(
                [
                    'title' => $collection->title,
                    'path' => '/store/' . $collection->slug,
                    'menu_id' => 2,
                    'submenu_id' => 0,
                    'sort' => $count,
                    'type' => 'collection',
                    'created_at' => $date,
                    'updated_at' => $date,
                    'resource_type' => 'collection',
                    'resource_id' => $collection->id
                ]
            );
            $count++;
        }
    }
}
