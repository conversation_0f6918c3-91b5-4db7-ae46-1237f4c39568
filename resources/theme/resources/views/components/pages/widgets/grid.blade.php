@props(['widget'])

@php
    $padding_top = match ($widget['settings']['padding']['top'] ?? '') {
        'sm' => 'tw-pt-6 lg:tw-pt-10',
        'md' => 'tw-pt-12 lg:tw-pt-16',
        'lg' => 'tw-pt-24 lg:tw-pt-32',
        'xl' => 'tw-pt-32 lg:tw-pt-48',
        default => '',
    };

    $padding_bottom = match ($widget['settings']['padding']['bottom'] ?? '') {
        'sm' => 'tw-pb-6 lg:tw-pb-10',
        'md' => 'tw-pb-12 lg:tw-pb-16',
        'lg' => 'tw-pb-24 lg:tw-pb-32',
        'xl' => 'tw-pb-32 lg:tw-pb-48',
        default => '',
    };

    $max_width = match ($widget['settings']['max_width'] ?? '') {
        'sm' => 'tw-max-w-lg',
        'md' => 'tw-max-w-4xl',
        'lg' => 'tw-max-w-6xl',
        'xl' => 'tw-max-w-7xl',
        default => '',
    };

    $grid_columns = match ((int) $widget['settings']['columns'] ?? '') {
        2 => 'tw-grid-cols-1 sm:tw-grid-cols-2',
        3 => 'tw-grid-cols-1 sm:tw-grid-cols-3',
        4 => 'tw-grid-cols-1 sm:tw-grid-cols-4',
        default => 'tw-grid-cols-1',
    };

    $query = match($widget['settings']['type']) {
        'blog' => \App\Models\Post::query()->with(['author']),
        'recipe' => \App\Models\Recipe::query(),
    };

    $items = ($widget['settings']['item_ids'] ?? false)
        ? $query
            ->whereIn('slug', explode(',', $widget['settings']['item_ids']))
            ->orderByRaw('FIELD(slug, "'. $widget['settings']['item_ids'] . '")')
            ->get()
        : collect();
@endphp

<div @if(!empty($widget['settings']['html_id'] ?? '')) id="{{ $widget['settings']['html_id'] }}" @endif class="tw-relative tw-w-full" style="background-color: {{ $widget['settings']['background']['color'] ?? '#ffffff' }}; @if(!empty($widget['settings']['background']['image']) ?? '') background-image: url('{{ $widget['settings']['background']['image'] }}'); background-repeat: no-repeat; background-size: cover; background-position: center center; @endif">
    <div class="tw-px-6 sm:tw-px-6 lg:tw-px-8 {{ $padding_top }} {{ $padding_bottom }}">

        <div class="tw-relative tw-mx-auto {{ $max_width }} tw-grid {{ $grid_columns }} tw-gap-x-8 tw-gap-y-20">
            @if($widget['settings']['type'] === 'blog')
                @foreach($items as $item)
                    <x-theme::blog-post-card :post="$item"/>
                @endforeach
            @else($widget['settings']['type'] === 'recipe')
                @foreach($items as $item)
                    <x-theme::recipe-card :recipe="$item"/>
                @endforeach
            @endif
        </div>
    </div>
</div>


