@if(auth()->check())
    <ul class="mobileNav">
        <li>
            <a href="{{ setting('mobile_store_url', '/navigation/store') }}"><i class="fa fa-lg fa-store"></i> @lang('Shop')</a>
        </li>
        <li>
            @if($has_subscription)
                <a href="{{ route('customers.recurring.edit') }}"><i class="fa fa-lg fa-shopping-basket"></i>
                    <livewire:theme.subscription-item-count />
                </a>
            @elseif( ! is_null($order))
                <a href="{{ route('customer.orders.show', compact('order')) }}"><i class="fa fa-lg fa-shopping-basket"></i>
                    <livewire:theme.order-item-count />
                </a>
            @else
                <a href="{{ route('cart.show') }}"><i class="fa fa-lg fa-shopping-basket"></i>
                    <livewire:theme.cart-item-count />
                </a>
            @endif
        </li>
        <li>
            <a href="/navigation/index"><i class="fa fa-lg fa-bars"></i> @lang('Menu')</a>
        </li>
    </ul>
@else
    <ul class="mobileNav">
        <li>
            <a href="{{ setting('mobile_store_url', '/navigation/store') }}"><i class="fa fa-lg fa-search"></i> @lang('Shop')</a>
        </li>
        <li>
            <a href="{{ route('login') }}"><i class="fa fa-lg fa-user"></i> {{ __('messages.sign_in') }}</a>
        </li>
        <li>
            <a href="/navigation/index"><i class="fa fa-lg fa-bars"></i> @lang('Menu')</a>
        </li>
    </ul>
@endif

