@if($errors->count())
    <div x-data="{ show: true }" class="siteMessage" x-bind:class="!show && 'siteMessage--hide'" id="siteMessage">
        <div class="siteMessage__message">
            <ul class="list-unstyled">
                @foreach($errors->all() as $error)
                    <li> - {!! $error !!}</li>
                @endforeach
            </ul>
        </div>
        <div class="siteMessage__closeButtonContainer">
            <a href="#" class="siteMessage__closeButton" x-on:click="show = false">
                <i class="fa fa-times-circle-o"></i> Dismiss
            </a>
        </div>
    </div>
@endif

@if(Session::has('flash_notification.message'))
    <div x-data="{ show: true }" class="siteMessage" x-bind:class="!show && 'siteMessage--hide'" id="siteMessage">
        <div class="siteMessage__message">
            {!! Session::get('flash_notification.message') !!}
        </div>
        <div class="siteMessage__closeButtonContainer">
            <a href="#" class="siteMessage__closeButton" x-on:click="show = false">
                <i class="fa fa-times-circle-o"></i> Dismiss
            </a>
        </div>
    </div>
@endif
