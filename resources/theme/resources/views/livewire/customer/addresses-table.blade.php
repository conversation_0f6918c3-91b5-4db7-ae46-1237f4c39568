<section class="customerProfile__contentContainer">
    <div class="tw-reset">
        <div class="">
            <div class="sm:tw-flex sm:tw-items-center">
                <div class="sm:tw-flex-auto">
                    <h1 class="tw-text-xl tw-font-semibold tw-leading-6 tw-text-gray-900">{{ __('messages.manage_address') }}</h1>
                    <p class="tw-mt-2 tw-text-sm tw-text-gray-700">A list of all of your shipping addresses.</p>
                </div>
                <div class="tw-mt-4 sm:tw-ml-16 sm:tw-mt-0 sm:tw-flex-none">
                    <button type="button" wire:click="$dispatch('open-modal-add-address')" class="tw-block tw-rounded-md tw-bg-theme-action-color tw-px-3 tw-py-2 tw-text-center tw-text-sm tw-font-semibold tw-text-white tw-shadow-sm hover:tw-bg-theme-action-color/70 focus-visible:tw-outline focus-visible:tw-outline-2 focus-visible:tw-outline-offset-2 focus-visible:tw-outline-theme-action-color">
                        Add address
                    </button>
                </div>
            </div>
            @if($addresses->isEmpty())
                <div class="tw-mt-8 tw-flow-root tw-text-center">
                    <button type="button" wire:click="$dispatch('open-modal-add-address')" class="tw-relative tw-block tw-w-full tw-rounded-lg tw-border-2 tw-border-dashed tw-border-gray-300 tw-p-12 tw-text-center hover:tw-border-gray-400 focus:tw-outline-none focus:tw-ring-2 focus:tw-ring-keppel-500 focus:tw-ring-offset-2">
                        <svg class="tw-mx-auto tw-h-12 tw-w-12 tw-text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z"></path>
                        </svg>

                        <span class="tw-mt-2 tw-block tw-text-sm tw-font-semibold tw-text-gray-900">Add a new shipping address</span>
                    </button>
                </div>
            @else
                <div class="tw-mt-8 tw-flow-root">
                    <div class="tw--mx-4 tw--my-2 tw-overflow-x-auto sm:tw--mx-6 lg:tw--mx-8">
                        <div class="tw-inline-block tw-min-w-full tw-py-2 tw-align-middle sm:tw-px-6 lg:tw-px-8">
                            <table class="tw-min-w-full tw-divide-y tw-divide-gray-300">
                                <thead>
                                <tr>
                                    <th scope="col" class="tw-py-3.5 tw-pl-4 tw-pr-3 tw-text-left tw-text-sm tw-font-semibold tw-text-gray-900 sm:tw-pl-0">
                                        Address
                                    </th>
                                    <th scope="col" class="tw-px-3 tw-py-3.5 tw-text-left tw-text-sm tw-font-semibold tw-text-gray-900">
                                        <span class="sr-only">Default Address</span>
                                    </th>
                                    <th scope="col" class="tw-relative tw-py-3.5 tw-pl-3 tw-pr-4 sm:tw-pr-0">
                                        <span class="tw-sr-only">Manage</span>
                                    </th>
                                </tr>
                                </thead>
                                <tbody class="tw-divide-y tw-divide-gray-200 tw-bg-white">
                                @foreach($addresses as $addressable)
                                    @php /** @var \App\Models\Address $addressable */ @endphp
                                    <tr>
                                        <td class="tw-whitespace-nowrap tw-py-5 tw-pl-4 tw-pr-3 tw-text-sm sm:tw-pl-0">
                                            <div class="tw-font-medium tw-text-gray-900">{{ $addressable->street }}@if($addressable->location->street_2)
                                                    , {{ $addressable->location->street_2 }}
                                                @endif</div>
                                            <div class="tw-mt-1 tw-text-gray-500">{{ $addressable->city }}
                                                , {{ $addressable->state }} {{ $addressable->postal_code }}</div>
                                        </td>

                                        <td class="tw-whitespace-nowrap tw-px-3 tw-py-5 tw-text-sm tw-text-gray-500">
                                            @if($addressable->location->is_default)
                                                <span class="tw-inline-flex tw-items-center tw-rounded-md tw-bg-theme-brand-color/10 tw-px-2 tw-py-1 tw-text-xs tw-font-medium tw-text-theme-brand-color tw-ring-1 tw-ring-inset tw-ring-theme-brand-color/20">
                                                    Default
                                                </span>
                                            @endif
                                        </td>
                                        <td class="tw-relative tw-whitespace-nowrap tw-py-5 tw-pl-3 tw-pr-4 tw-space-x-2 tw-text-right tw-text-sm tw-font-medium sm:tw-pr-0">
                                            <button type="button" wire:click="$dispatch('open-modal-edit-address', { address_id: {{ $addressable->id }} })"
                                                    class="tw-text-theme-link-color tw-no-underline hover:tw-text-theme-link-color/70">
                                                Edit
                                            </button>
                                            <button type="button" wire:click="$dispatch('open-modal-delete-address-confirmation', { address_id: {{ $addressable->id }} })"
                                                    class="tw-text-theme-link-color tw-no-underline hover:tw-text-theme-link-color/70">
                                                Delete
                                            </button>
                                        </td>
                                    </tr>
                                @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </div>
</section>
