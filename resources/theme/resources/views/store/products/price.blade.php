@php
    /** @var \App\Models\Product $product */
@endphp

<meta itemprop="priceCurrency" content="{{ setting('currency', 'USD') }}"/>
<meta itemprop="url" content="{{ url('/store/product/'.$product->slug) }}"/>
@if($product->isOutOfStock())
    <link itemprop="availability" href="https://schema.org/OutOfStock"/>
@else
    <link itemprop="availability" href="https://schema.org/InStock"/>
@endif

@php($show_unit_pricing = $product->isPricedByWeight() && setting('show_price_per_pound', true))

@if($product->isOnSale())
    <div class="tw-flex-1 tw-rounded-md ">
        <div class="tw-pt-3 tw-pb-4 tw-text-left">
            @if($product->isGiftCard())
                <div class="tw-text-sm tw-font-semibold {{ $text_class }} productListing__saleSavings- productListing__saleSavings--grid">
                    &#36;{{ money($product->getSavings()) }} bonus cash
                </div>
            @else
                <div class="tw-text-sm {{ $text_class }} productListing__saleSavings- productListing__saleSavings--grid">
                    @if($product->isBundle())
                        Bundle Savings
                    @else
                        Save
                    @endif
                    @if($show_unit_pricing)
                        &#36;{{ money($product->getUnitSavings()) }} /{{ __("messages.uom." . setting('weight_uom', 'pounds')) }}.
                    @else
                        &#36;{{ money($product->getSavings()) }}
                    @endif
                </div>
            @endif
            <div class="tw-mt-1 tw-flex tw-items-baseline">
                <div class="tw-mr-1 tw-pr-1 productListing__salePrice">
                    <div class="tw-text-2xl tw-font-medium tw-text-theme-brand-color sm:tw-text-3xl">
                        @if($show_unit_pricing)
                            <span class="tw-flex tw-items-baseline">
                                &#36;
                                <span itemprop="price">{{ money($product->getUnitPrice()) }}</span>
                                <span class="tw-text-sm productListing__price_unit" itemprop="unitText">
                                    /{{ __("messages.uom." . setting('weight_uom', 'pounds')) }}.
                                </span>
                            </span>

                        @else
                            <span>
                                &#36;<span itemprop="price">{{ money($product->getPrice()) }}</span>
                            </span>
                        @endif
                    </div>
                </div>
                @if ( ! $product->isGiftCard())
                    <div class="tw-text-sm tw-font-normal tw-text-gray-500 tw-line-through sm:tw-text-base productListing__originalPrice">
                        @if($show_unit_pricing)
                            &#36;{{ money($product->getRegularUnitPrice()) }} /{{ __("messages.uom." . setting('weight_uom', 'pounds')) }}.
                        @else
                            &#36;{{ money($product->getRegularPrice()) }}
                        @endif
                    </div>
                @endif
            </div>

            @if($show_unit_pricing)
                <div class="tw-mt-1 tw-text-xs tw-font-normal tw-text-gray-500 productListing__averageWeight">
                    Avg. {{ weight($product->weight) }}
                </div>
            @endif
        </div>
    </div>
@else
    <div class="tw-flex-1">
        <div class="tw-pt-3 tw-pb-4 tw-text-left">
            <div class="tw-flex tw-items-center">
                <div class="tw-mr-1 tw-pr-1 productListing__salePrice">
                    <div class="tw-text-xl tw-font-medium tw-text-gray-700 sm:tw-text-2xl">
                        @if($show_unit_pricing)
                            <span class="tw-flex tw-items-baseline">
                                &#36;
                                <span itemprop="price">{{ money($product->getUnitPrice()) }}</span>
                                <span class="tw-text-sm tw-font-normal tw-text-gray-700 productListing__price_unit" itemprop="unitText">
                                    /{{ __("messages.uom." . setting('weight_uom', 'pounds')) }}.
                                </span>
                            </span>

                        @else
                            <span>
                                &#36;<span itemprop="price">{{ money($product->getPrice()) }}</span>
                            </span>
                        @endif
                    </div>
                </div>
            </div>

            @if($show_unit_pricing)
                <div class="tw-mt-1 tw-text-xs tw-font-normal tw-text-gray-500 productListing__averageWeight">
                    Avg. {{ weight($product->weight) }}
                </div>
            @endif
        </div>
    </div>
@endif

