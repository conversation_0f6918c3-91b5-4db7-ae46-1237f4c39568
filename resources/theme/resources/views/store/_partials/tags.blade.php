@if(count($tags))
	<!-- Start Tags -->
	<div class="productTags">
		<ul class="productTags__list">
			<li class="productTags__tag productTags__tag--active">
				@if(request()->segment(2) == 'vendor' || request()->segment(2) == 'protocol')
					<a href="/{{ request()->segment(1) }}/{{ request()->segment(2) }}/{{ request()->segment(3) }}">All</a>
				@else
					<a href="/{{ request()->segment(1) }}/{{ request()->segment(2) }}">All</a>
				@endif
			</li>
			@if(request()->get('sale'))
				<li class="productTags__tag"><a href="{{ URL::current() }}?sale=true">On Sale</a></li>
			@else
				<li class="productTags__tag productTags__tag--active"><a href="{{ URL::current() }}?sale=true">On Sale</a></li>
			@endif
			@foreach($tags as $tag)
				@if($tag->slug == request()->get('tag'))
					<li class="productTags__tag">
						<a href="{{ $tag->storeUrl() }}">{{ $tag->title }}</a>
					</li>
				@else
					<li class="productTags__tag productTags__tag--active">
						<a href="{{ $tag->storeUrl() }}">{{ $tag->title }}</a>
					</li>
				@endif
			@endforeach
		</ul>
	</div>
	<!-- End Tags -->
@endif
