@php
    /** @var bool $has_subscription */
    /** @var App\Models\Order|null $openOrder */
    /** @var App\Contracts\Cartable $cart */

    $is_brand_style = str(theme('store_button_style', 'btn-brand'))->contains('brand');

     // Spelling out all classes here so that they are picked up by the tailwind compiler
    $callout_border_class = ! $is_brand_style ? 'tw-border-theme-brand-color/70' : 'tw-border-theme-action-color/70';
    $background_class = ! $is_brand_style ? 'tw-bg-theme-brand-color' : 'tw-bg-theme-action-color';
    $light_background_class = ! $is_brand_style ? 'tw-bg-theme-brand-color/5' : 'tw-bg-theme-action-color/5';
    $text_class = ! $is_brand_style ? 'tw-text-theme-brand-color' : 'tw-text-theme-action-color';

@endphp
<section
        class="tw-reset featuredProductsWidget featuredProductsWidget--{{ $widget->id }}"
        id="featuredProductsWidget{{ $widget->id }}"
        data-widget="{{ $widget->id}}"
        data-element="home"
>
    <div class="tw-mx-auto tw-px-4 tw-max-w-2xl sm:tw-px-6 lg:tw-max-w-7xl lg:tw-px-8">
        @if($widget->setting('header_show', true))
            <header class="tw-text-center tw-mb-4 featuredProductsWidget__header">
                <h2 class="tw-m-0 tw-text-4xl" data-widget="{{ $widget->id}}" data-element="headingText">{{ $widget->setting('header', 'Featured Products') }}</h2>
            </header>
        @endif

        @if($has_subscription)
            @php
                /** @var \App\Models\RecurringOrder|null $subscription */
                $subscription = auth()->user()->recurringOrder()->with('fulfillment', 'customer')->first();
            @endphp
            @includeIf('theme::widgets.FeaturedProducts.'.$widget->setting('layout_style', 'style1'), [
                'delivery_method' => $subscription?->fulfillment,
                'price_group_id' => $subscription?->pricingGroupId(),
            ])
        @elseif( ! is_null($openOrder))
            @includeIf('theme::widgets.FeaturedProducts.'.$widget->setting('layout_style', 'style1'), [
                'delivery_method' => $openOrder->pickup,
                'price_group_id' => $openOrder->getPricingGroup(),
            ])
        @else
            @php
                /** @var \App\Models\Pickup|null $delivery_method */
                $delivery_method = $cart->cartLocation() ?? app(\App\Services\StoreService::class)->deliveryMethod(request()->cookie('shopping_delivery_method_id'));
            @endphp
            @includeIf('theme::widgets.FeaturedProducts.'.$widget->setting('layout_style', 'style1'), [
                'delivery_method' => $delivery_method,
                'price_group_id' => $cart->cartPricingGroupId(),
            ])
        @endif
    </div>
</section>
