<section
        class="teamWidget teamWidget--{{ $widget->id}} {{ $widget->setting('alignment', 'text-center') }}"
        id="teamWidget{{ $widget->id }}"
        data-widget="{{ $widget->id}}" data-element="home"
        x-data
>
    <ul class="teamWidget__list">
        @foreach($widget->setting('items') as $member)
            <li class="teamWidget__listItem">
                <button
                        type="button"
                        x-on:click="$dispatch('legacy-modal-opened', { id: 'teamMember_{{ $widget->id }}_{{ $loop->index }}' });"
                >
                    <img
                            src="{{ \App\Models\Media::s3ToCloudfront($member->src ?? '') }}"
                            alt="{{ $member->caption }}"
                            class="teamWidget__listImage"
                    >
                    <div class="teamWidget__listCaption h5">{{ $member->caption }}</div>
                    <div class="teamWidget__listSubcaption">{{ $member->subcaption }}</div>
                </button>

                <x-theme::legacy-modal id="teamMember_{{ $widget->id }}_{{ $loop->index }}">
                    <div class="tw-relative tw-w-full tw-mx-auto tw-transform tw-rounded-lg tw-bg-white tw-text-left tw-shadow-xl sm:tw-my-8 sm:tw-w-full sm:tw-max-w-4xl">
                        <div class="tw-rounded-t-lg tw-bg-white tw-px-4 tw-pb-4 tw-pt-5 sm:tw-p-6 sm:tw-pb-4">
                            <div class="teamWidget__bioInnerContainer">
                                <div class="teamWidget__profileLeft">
                                    <img
                                            src="{{ \App\Models\Media::s3ToCloudfront($member->src ?? '') }}"
                                            alt="{{ $member->caption ?? '' }}"
                                            class="teamWidget__profileImage"
                                    >
                                </div>
                                <div class="teamWidget__profileRight">
                                    <div class="teamWidget__profileCaption h4">
                                        {{ $member->caption ?? '' }}
                                    </div>
                                    <div class="teamWidget__profileSubcaption">
                                        {{ $member->subcaption ?? '' }}
                                    </div>
                                    <div class="teamWidget__profileBio">
                                        {!! $member->bio ?? '' !!}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="tw-rounded-b-lg tw-bg-gray-50 tw-px-4 tw-py-3 sm:tw-px-6 sm:tw-flex sm:tw-flex-row-reverse">

                            <button type="button" x-on:click="close" class="tw-mt-3 tw-inline-flex tw-w-full tw-justify-center tw-rounded-md tw-bg-white tw-px-3 tw-py-2 tw-text-sm tw-font-semibold tw-text-gray-900 tw-shadow-sm tw-ring-1 tw-ring-inset tw-ring-gray-300 hover:tw-bg-gray-50 sm:tw-mt-0 sm:tw-w-auto">
                                Close
                            </button>
                        </div>
                    </div>

                </x-theme::legacy-modal>
            </li>
        @endforeach
    </ul>
</section>
