@php use App\Models\User; @endphp
@extends('theme::_layouts.main', [
    'pageTitle' => 'Referrals'
])

@php
    /** @var User $customer */
@endphp

@section('content')
    <section class="pageContainer customerProfile">
        <div class="customerProfile__container">
            <div class="customerProfile__navigationContainer">
                @include('theme::customers.partials.navigation')
            </div><!--
    -->
            <section class="customerProfile__contentContainer">
                <h1 class="customerProfile__heading h1">{{ __('messages.referrals') }}</h1>

                <p>@lang('messages.referral_instructions')</p>

                <ul class="info-boxes">
                    <li>
                        <div class="info-box-heading">@lang('Your Referral Link')</div>
                        {{ $customer->getReferralUrl() }}
                    </li>
                    <li>
                        <div class="info-box-heading">@lang('Earnings')</div>
                        &#36;{{ money($customer->referral_bonus_earned) }}
                    </li>
                </ul>

                <table class="table table-striped">
                    <tr>
                        <td>@lang('Referrals')</td>
                        <td>@lang('messages.referral_referrals')</td>
                        <td> {{ count($customer->referrals) }}</td>
                    </tr>
                    <tr>
                        <td>@lang('Your Reward')</td>
                        <td>@lang('messages.referral_rewards')</td>
                        <td> &#36;{{ $customer->present()->referralBonus() }}</td>
                    </tr>
                    <tr>
                        <td>@lang('Incentive')</td>
                        <td>@lang('messages.referral_incentive')</td>
                        <td> &#36;{{ $customer->present()->referralPayout() }}</td>
                    </tr>
                </table>
            </section>
        </div>
    </section>
@stop()
