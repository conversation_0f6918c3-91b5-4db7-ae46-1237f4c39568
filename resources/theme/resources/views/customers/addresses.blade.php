@extends('theme::_layouts.main', [
    'pageTitle' => 'Update Your Address'
])

@section('content')

    <section class="pageContainer customerProfile">
        <div class="customerProfile__container">
            <div class="customerProfile__navigationContainer">
                @include('theme::customers.partials.navigation')
            </div>
            @if(auth()->user()->hasRecurringOrder())
                <div class="tw-reset">
                    <h1 class="tw-text-xl tw-font-semibold tw-leading-6 tw-text-gray-900">{{ __('messages.manage_address') }}</h1>
                    <p class="tw-mt-4">You are currently an active subscriber. Please <a href="{{ route('messaging.contact') }}">contact customer support</a> to
                        update your shipping address.</p>
                </div>
            @else
                @livewire('theme.customer.addresses-table', compact('user'))
            @endif
        </div>
    </section>
@endsection

@section('modals')
    <livewire:theme.modals.edit-address/>
    <livewire:theme.modals.delete-address-confirmation/>
@endsection
