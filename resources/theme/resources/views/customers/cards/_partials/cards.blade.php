<form>
    <div class="tw-max-w-2xl space-y-12">
        <div class="border-b border-gray-900/10 pb-12">
            <div class="tw-border-b tw-border-gray-200 tw-pb-5">
                <div class="sm:tw-flex sm:tw-items-baseline sm:tw-justify-between">
                    <div class="sm:tw-w-0 sm:tw-flex-1">
                        <h1 id="message-heading" class="tw-text-base tw-font-normal tw-text-gray-900">Credit Card(s)</h1>
                        <p class="tw-mt-1 tw-truncate tw-text-sm tw-text-gray-500">Manage your credit cards and default payment option.</p>
                    </div>

                    <div class="tw-mt-4 tw-flex tw-items-center tw-justify-between sm:tw-ml-6 sm:tw-mt-0 sm:tw-shrink-0 sm:tw-justify-start">
                        <a href="{{ route('account.cards.create') }}" class="tw-no-underline tw-ml-3 tw-inline-flex tw-items-center tw-rounded-md tw-bg-theme-brand-color tw-px-3 tw-py-2 tw-text-sm tw-font-semibold tw-text-white tw-shadow-sm hover:tw-bg-theme-brand-color/70 focus-visible:tw-outline focus-visible:tw-outline-2 focus-visible:tw-outline-offset-2 focus-visible:tw-outline-theme-brand-color">{{ __('Add New Card') }}</a>
                    </div>
                </div>
            </div>

            <div class="tw-mt-6 tw-grid tw-grid-cols-1 tw-gap-x-6 tw-gap-y-8 sm:tw-grid-cols-6">
                <div class="tw-col-span-full">

                    <livewire:theme.customer.credit-cards lazy="on-load"/>

                </div>
            </div>
        </div>
    </div>
</form>

