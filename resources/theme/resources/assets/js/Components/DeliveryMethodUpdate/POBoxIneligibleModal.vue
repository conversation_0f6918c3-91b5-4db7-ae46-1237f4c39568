<template>
    <Modal @close="$emit('close')">
        <div>
            <div class="tw-bg-white tw-px-4 tw-pt-5 tw-pb-4 sm:tw-p-6 sm:tw-pb-4">
                <div class="sm:tw-flex sm:tw-items-start">
                    <div class="tw-mx-auto tw-flex-shrink-0 tw-flex tw-items-center tw-justify-center tw-h-12 tw-w-12 tw-rounded-full tw-bg-gray-100 tw-bg-opacity-75 sm:tw-mx-0 sm:tw-h-10 sm:tw-w-10">
                        <MapIcon aria-hidden="true" class="tw-h-6 tw-w-6 tw-gray-600" />
                    </div>
                    <div class="tw-mt-3 tw-text-center sm:tw-mt-0 sm:tw-ml-4 sm:tw-text-left">
                        <DialogTitle as="h3" class="tw-text-lg tw-leading-6 tw-font-medium tw-font-display tw-text-gray-900">
                            PO Box Ineligible
                        </DialogTitle>
                        <div class="tw-mt-2">
                            <p class="tw-text-sm tw-text-gray-500">
                                The selected delivery method does ship to PO Boxes. Please update the address or select another delivery method.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="tw-bg-gray-50 tw-px-4 tw-py-3 sm:tw-px-6 sm:tw-flex sm:tw-flex-row-reverse">
                <button ref="cancelButtonRef" class="tw-mt-3 tw-w-full tw-inline-flex tw-justify-center tw-rounded-md tw-border tw-border-gray-300 tw-shadow-sm tw-px-4 tw-py-2 tw-bg-white tw-text-base tw-font-medium tw-text-gray-700 hover:tw-bg-gray-50 focus:tw-outline-none focus:tw-ring-2 focus:tw-ring-offset-2 focus:tw-ring-theme-action-color sm:tw-mt-0 sm:tw-ml-3 sm:tw-w-auto sm:tw-text-sm" type="button" @click="$emit('close')">
                    Cancel
                </button>
            </div>
        </div>
    </Modal>
</template>

<script>
import Modal from '../Modal.vue';
import { DialogTitle } from '@headlessui/vue';
import { MapIcon } from '@heroicons/vue/outline';

export default {
    name: 'POBoxIneligibleModal',

    emits: ['close'],

    components: {
        Modal,
        DialogTitle,
        MapIcon
    },

    setup(props, { emit }) {
        return {};
    }
};
</script>
