<template>
    <div id="card-form">
        <label for="card-element" class="tw-sr-only">Card details</label>
        <div class="tw-flex tw-flex-items-center tw-space-x-4 tw-mt-1">
            <div id="card-element" class="tw-block tw-w-full tw-my-3"></div>
            <button type="button" @click.prevent="submit" :disabled="!clientSecret" class="tw-inline-flex tw-items-center tw-rounded-md tw-border tw-border-gray-300 tw-bg-white tw-px-4 tw-py-2 tw-text-sm tw-font-medium tw-text-gray-700 tw-shadow-sm hover:tw-bg-gray-50 focus:tw-outline-none focus:tw-ring-2 focus:tw-ring-theme-action-color focus:tw-ring-offset-2">
                <svg v-if="adding" class="tw-animate-spin tw--ml-1 tw-mr-3 tw-h-5 tw-w-5 tw-text-gray-200" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="tw-opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="tw-opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span v-text="adding ? 'Adding...' : 'Add'"></span>
            </button>
        </div>
        <div class="tw-mt-1 tw-text-xs tw-text-red-500" id="card-errors" role="alert"></div>
    </div>
</template>

<script>
import { CreditCardIcon } from "@heroicons/vue/outline";
import { DialogTitle } from "@headlessui/vue";
import { useCheckoutStore } from "../../stores/checkout";
import { onMounted, ref } from "vue";
import axios from "axios";

export default {
    name: "AddStripeCardForm",

    emits: ['cardAdded'],

    props: {
        user: { required: true }
    },

    components: {
        CreditCardIcon,
        DialogTitle
    },

    setup(props, { emit }) {
        const checkout = useCheckoutStore()
        const stripe = Stripe(checkout.payment_gateway.public_key)
        const elements = stripe.elements();
        const clientSecret = ref(null)
        const adding = ref(false)

        const card = elements.create("card");
        card.on('change', function(event) {
            const displayError = document.getElementById('card-errors');
            displayError.textContent = '';
            if (event.error) {
                displayError.textContent = event.error.message;
            }
        });

        const saveSetupIntent = setupIntent => {
            axios.post(`/api/users/${props.user.id}/payment-methods`, {
                setup_intent_id: setupIntent.id
            })
                .then(({ data }) => {
                    adding.value = false
                    emit('cardAdded', data)
                })
                .catch(error => {
                    const errorElement = document.getElementById('card-errors');
                    errorElement.textContent = 'There was an error. Please try again.';
                    adding.value = false
                })
        }

        const submit = () => {
            if ( ! clientSecret.value || adding.value) return;

            adding.value = true;
            const errorElement = document.getElementById('card-errors');
            errorElement.textContent = '';

            stripe.confirmCardSetup(clientSecret.value, {
                payment_method: { card }
            }).then(result => {
                if (result.error) {
                    errorElement.textContent = result.error.message;
                    adding.value = false
                    return;
                }

                saveSetupIntent(result.setupIntent)
            }).catch(error => {
                errorElement.textContent = 'There was an error. Please try again.';
                adding.value = false
            });
        }

        const fetchSetupIntentClientSecret = () => {
            axios.post('/api/setup-intents')
                .then(({ data }) => {
                    clientSecret.value = data.client_secret
                })
        }

        fetchSetupIntentClientSecret()

        onMounted(() => card.mount("#card-element"))

        return {
            adding,
            clientSecret,
            submit
        }
    }
};
</script>
