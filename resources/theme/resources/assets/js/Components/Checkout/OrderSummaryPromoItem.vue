<template>
    <li class="tw-p-px tw-rounded-md tw-bg-gradient-to-r tw-from-chestnut-rose-400 tw-to-buttercup-300">
        <div class="tw-rounded-md tw-flex tw-px-4 tw-bg-white tw-items-start tw-py-3 tw-space-x-4">
            <img v-if="item.product.cover_photo_thumbnail" :alt="item.product.title" :src="item.product.cover_photo_thumbnail" class="tw-flex-none tw-w-10 tw-h-10 tw-rounded-md tw-object-center tw-object-cover">
            <div v-else class="tw-flex-none tw-w-10 tw-h-10 tw-rounded-md tw-bg-gray-300"></div>
            <div class="tw-flex-auto">
                <h3 class="tw-text-sm" v-text="item.product.title"></h3>
                <p class="tw-mt-px tw-text-xs tw-text-gray-500">
                    Qty {{ item.quantity }}<span v-if="isPricedByWeight(item.product)"> | {{ item.weight }} {{ weightLabel(tenant.weight_uom) }}</span>
                </p>
            </div>
            <p class="tw-flex-none tw-text-sm tw-font-medium" v-text="currency.centsToDollars(itemSubtotal(item))"></p>
        </div>

    </li>
</template>

<script>
import { useTenantStore } from '../../stores/tenant';
import currency from '../../../../../../assets/js/modules/currency';
import { isPricedByWeight, itemSubtotal, weightLabel } from '../../../../../../assets/js/modules/cart';

export default {
    name: 'OrderSummaryItem',

    props: {
        item: { required: true }
    },

    setup() {
        const tenant = useTenantStore();

        return {
            tenant,
            isPricedByWeight,
            weightLabel,
            itemSubtotal,
            currency
        };
    }
};
</script>
