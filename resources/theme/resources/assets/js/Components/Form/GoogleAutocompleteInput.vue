<template>
    <div>
        <div class="tw-relative tw-rounded-md tw-shadow-sm">
            <input
                :id="id"
                ref="googleAutocompleteInput"
                :aria-describedby="errorId"
                :aria-invalid="hasError"
                :class="[
                    'tw-block tw-w-full focus:tw-outline-none sm:tw-text-sm tw-rounded-md',
                    hasError
                    ? 'tw-pr-10 tw-border-red-300 tw-text-red-900 tw-placeholder-red-300 focus:tw-ring-red-500 focus:tw-border-red-500'
                    : 'tw-border-gray-300 tw-text-gray-700 tw-placeholder-gray-300 focus:tw-ring-theme-action-color focus:tw-border-theme-action-color'
                ]"
                :name="name"
                :placeholder="placeholder"
                :type="type"
                :value="modelValue"
                v-bind="$attrs"
                @blur="handleBlur"
            />
            <div v-if="hasError" class="tw-absolute tw-inset-y-0 tw-right-0 tw-pr-3 tw-flex tw-items-center tw-pointer-events-none">
                <ExclamationCircleIcon aria-hidden="true" class="tw-h-5 tw-w-5 tw-text-red-500" />
            </div>
        </div>
        <p v-if="hasError" :id="errorId" class="tw-mt-2 tw-text-sm tw-text-red-600" v-text="error"></p>
    </div>
</template>

<script>
export default {
    name: 'GoogleAutocompleteInput'
};
</script>

<script setup>
import { ExclamationCircleIcon } from '@heroicons/vue/solid';
import { computed, ref, defineEmits, onMounted } from 'vue';
import { Loader } from '@googlemaps/js-api-loader';

const googleAutocompleteInput = ref(null);

const props = defineProps({
    apiKey: { required: true },
    modelValue: { required: true },
    id: { required: true },
    name: { required: true },
    type: { default: 'text' },
    error: { default: null },
    placeholder: { default: '' }
});

const emit = defineEmits(['place-updated', 'street-changed']);

const hasError = computed(() => !!props.error);
const errorId = computed(() => hasError ? `${props.id}-error` : undefined);

onMounted(async () => {
    const places = await new Loader({
        apiKey: props.apiKey,
        version: 'weekly'
    }).importLibrary('places');

    const autocompleteInput = new places.Autocomplete(googleAutocompleteInput.value, { fields: ['address_components', 'geometry'] });

    autocompleteInput.addListener('place_changed', () => {
        let street = '';
        let city = '';
        let state = '';
        let postal_code = '';

        autocompleteInput.getPlace()
            .address_components
            .forEach(component => {
                if (component.types.includes('street_number')) {
                    street = component.long_name;
                }
                if (component.types.includes('route')) {
                    street += ' ' + component.long_name;
                }
                if (component.types.includes('locality')) {
                    city = component.long_name;
                }
                if (component.types.includes('administrative_area_level_1')) {
                    state = component.short_name;
                }
                if (component.types.includes('postal_code')) {
                    postal_code = component.long_name;
                }
            });

        emit('place-updated', { street, city, state, postal_code });
        googleAutocompleteInput.value = street;
    });
});

const handleBlur = () => {
    if (props.modelValue !== googleAutocompleteInput.value.value) {
        emit('street-changed', { value: googleAutocompleteInput.value.value });
    }
};


</script>
