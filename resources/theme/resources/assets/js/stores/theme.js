import { defineStore } from 'pinia'

export const useThemeStore = defineStore('theme', {
    state: () => ({
        header_border_size: '0px',
        header_border_position: 'bottom',
        logo_src: null
    }),

    getters: {
        hasBorderOnHeader: state => state.header_border_size !== '0px',
        headerBorderSize: state => state.header_border_size,
        hasTopBorderOnHeader: state => state.header_border_position === 'top',
        hasBottomBorderOnHeader: state => state.header_border_position === 'bottom',
        hasLogo: state => !!state.logo_src
    },
})