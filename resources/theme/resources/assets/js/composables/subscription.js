export const useFormattedFrequency = frequency => {
    if (!frequency) return '';

    return {
        7: 'every week',
        14: 'every 2 weeks',
        28: 'every 4 weeks',
        42: 'every 6 weeks',
        56: 'every 8 weeks'
    }[frequency];
};

export const usePotentialSubscriptionDiscountTotal = (itemSubtotal, discountPercentage) => Math.round(itemSubtotal * (discountPercentage / 100));

