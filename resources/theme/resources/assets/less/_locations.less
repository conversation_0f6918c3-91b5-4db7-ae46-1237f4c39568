.locationsIndex__heading {
	padding-bottom: (@white-space / 2);
}

// Individual locations page

.locationPage--banner {
	width: 100%;
  	max-width: 100%;
  	margin: 0 auto;
  	padding: (@white-space) (@white-space / 4);
  	background-size: cover;
  	background-position: center center;
  	background-repeat: no-repeat;
  	color: #FFFFFF;
  	text-shadow: 1px 1px 2px rgba(44, 44, 44, 0.3);
  	.locationPage__bannerLink {
  		color: #FFF;
  		border-bottom: dotted 1px #FFF;
  	}
}

.locationPage__header {
	text-align: center;
	.centerThis();
	margin-top: @white-space;
	margin-bottom: @white-space;
}

.locationPage__ctaContainer {
	color: #3d3d3d;
	border-radius: (@white-space / 10);
	max-width: (@site-width / 2);
	margin: (@white-space / 2) auto;
	padding: (@white-space / 2);
}

.locationPage__ctaHeading {
	line-height: 1.5;
}

.locationPage__ctaButton {
	display: block;
	margin: (@white-space / 2) auto;
	max-width: 250px;
}

.locationPage__infoBox {
	margin-bottom: (@white-space / 2);
}

.locationPage__mapContainer {
	margin-top: @white-space;
}

.locationPage__map {
	width: auto;
	height: 500px;
}

.locationPage__infoBoxList {
	list-style-type: none;
	padding: 0;
	.centerThis();
	margin-bottom: (@white-space / 2);
	text-align: center;
	max-width: 100%;
}

.locationPage__infoBoxItem {
	text-align: center;
	display: inline-block;
	width: 25%;
	min-width: 300px;
	padding: 0 0 (@white-space / 4) 0;
	margin-right: -4px;
	vertical-align: top;
}

.locationPage__getDirectionsLink {
	font-size: 13px;
}

.locationPage__schedule, .locationPage__description, .locationPage__ctaContainer, .locationPage__fees {
	.centerThis();
	max-width: 900px;
	margin-top: @white-space;
	margin-bottom: @white-space;
}

.locationPage__ctaContainer
{
	text-align: center;
}

.locationPage__scheduleTableContainer {
	overflow: auto;
	max-height: 500px;
}

.locationPage__times {
	white-space: pre-wrap;
}

@media (max-width: @mobile-width) {
	.locationPage__getDirectionsLink, .locationPage__contactPhone {
		display: block;
		padding: (@white-space / 10);
	}

	.locationPage__infoBoxItem {
		text-align: center;
		display: block;
		width: 100%;
		min-width: 100%;
		padding: (@white-space / 2) 0 (@white-space / 2) 0;
		margin-right: 0;
		border-bottom: solid 1px #EEE;
	}

	.locationPage__infoBoxList {
		> li:first-of-type {
			padding-top: 0;
		}
	}
}	