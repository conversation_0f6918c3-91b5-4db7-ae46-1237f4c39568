.termsPage {
	.pageContainer();
	width: @site-width * 0.60;
}

.termsPage__body {
	max-height: 800px;
	overflow: scroll;
	background-color: #fafafa;
	padding: @white-space / 4;
	border: solid 1px #f0f0f0;
	margin-bottom: @white-space / 4;
}

.termsPage__form {
	display: block;
	text-align: center;
	padding: 0 (@white-space / 2);
}

.termsPage__checkbox, .termsPage__submit {
	display: inline-block;
	vertical-align: middle;
	padding: 0 @white-space / 4 0 0;
}

.termsPage__submit {
	.btn {
		width: 100px;	
	}
}

@media (max-width: @mobile-width) {
	.termsPage {
		max-width: 100%;
		margin: 0;
		padding: 0 0 (@white-space / 2) 0;
	}


	.termsPage__body {
		max-height: 100%;
	}
}	