.header--flex {
	width: 100%;
	height: auto;
	padding: 0;

	.header__container {
		margin: 0 auto;
		display: flex;
		flex-wrap: no-wrap;
		justify-content: center;
		align-items: center;
	}

	.logo {
		text-align: center;
	}

	.logo--text {
		font-size: 30px !important;
	}

	.logo__container {
		flex: 0 0 auto;
		display: inline-block;
		margin-right: auto;
	}

	.menu__container {
		flex: 1 1 auto;
		height: auto;
		display: inline-block;
	}

	.menu--link {
		padding: 12px 18px;
		margin: 0;
	}

	.logo--img {
		display: inline-block;
		min-width: 75px;
		max-width: auto;
		max-height: 300px;
	}

	.navigationMenu--toggle {
		display: none;
		margin-left: auto;
		position: absolute;
		top: 14px;
		right: 5px;
	}
}

@media (max-width: 1100px) {
	.header--flex {
		.header__container {
			flex-wrap: wrap;
		}
	}	
}		

@media (max-width: 768px) {
	.header--flex {
		.header__container {
			display: block;
		}
		.menu__container {
			display: block;
			width: 100% !important;
			padding: 0 !important;
		}
		.navigationMenu--toggle {
			display: block;
		}
		.navigationMenu--toggle-button {
			display: block;
			border: none;
			background-color: rgba(255, 255, 255, 0.95);
			color: #3d3d3d;
			padding: 6px 10px;
			border-radius: 2px;
			font-size: 22px;
			line-height: 1;
			margin: 6px 8px 6px auto;
			height: 40px;
			width: 40px;
		}
		.navigationMenu--toggle-button:hover {
			background-color: #3d3d3d;
			color: #fff;
		}
		.navigationMenu--toggle-button:focus {
			outline: none;
		}
		.logo__container {
			width: 100%;
			padding: 0 5px;
			max-height: 80px;
		}
		.logo {
			text-align: left;
			vertical-align: middle;
			line-height: 80px;
		}
		.logo--img {
			max-height: 80px;
			max-width: 100%;
			height: auto;
			padding: 3px 66px 3px 5px;
		}
	}		
}