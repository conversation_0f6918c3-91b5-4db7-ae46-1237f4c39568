.page {
  max-width: 100%;
}

.page__header {
  margin-bottom: @white-space;
  h1 {
    margin: 1em 0 0.25em 0; 
    padding: 0;
  }
}

@media (max-width: @screen-sm) {
  .page__header {
    h1 {
      font-size: 24px;
    }
  }

  .page__subtitle {
    color: @gray-light;
    padding: 0;
    font-size: 14px;
  }  
} 

.page--950 {
  max-width: 950px;
}

.page--750 {
  max-width: 750px;
}

.page--550 {
  max-width: 550px;
}

.page--450 {
  max-width: 450px;
}

.full-width {
  width: 100%;
  margin: 0px;
}

.full-width__lg {
  padding: 120px 0px;
  @media (max-width: @screen-sm) {
    padding: 90px 0px;
  }  
}

.full-width__md {
  padding: 60px 0px;
  @media (max-width: @screen-sm) {
    padding: 30px 0px;
  } 
}

.full-width__sm {
  padding: 30px 0px;
}

.home-page p {
  line-height: 1.8em;
  font-size: 19px;
  font-family: @font-family-serif;
}

.home-page {
  section:nth-of-type(odd)
  {
    background-color: @gray-lighter;
  }
}