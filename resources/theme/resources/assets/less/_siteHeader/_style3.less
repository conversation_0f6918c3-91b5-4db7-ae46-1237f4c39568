// Logo on top with navigation on bottom

.siteHeader__container--style3 {
	text-align: center;
	margin: 0 auto;
}

.logo__container--style3 {
	text-align: center;
}


.auxiliaryMenu__container {
	margin: 0 auto;
	width: 100%;
	border-bottom: solid 1px transparent;
}

/*Auxiliary Menu*/
.auxiliaryMenu {
	list-style-type: none;
	width: 100%;
	margin: 0 auto;
	padding: (@white-space / 10) ((@white-space / 4) + 18) (@white-space / 10) (@white-space / 4);
	text-align: right;
	transition: all 0.2s ease-in-out;
	> li {
		display: inline-block;
		margin: 0;
		padding: 0 0 0 18px;
		> a {
			font-size: 14px;
			display: block;
		}
		> a.cta {
			color: #fff;
			border-radius: 3px;
		}
		> a:hover {
			text-decoration: none;
		}
	}
}

.siteHeader--style3 {
	.auxiliaryMenu--float {
		position: absolute;
		top: 0;
		left: 0;
		background-color: transparent;
	}
}

.auxiliaryMenu--block {
	position: relative;
	top: 0;
	left: 0;
	background-color: inherit;
}

@media (max-width: @mobile-width) {
	.auxiliaryMenu {
		display: none;
	}
}


.auxiliaryMenu__orderStatus {
	list-style-type: none;
	margin: 0;
	padding: 0;
	color: #FFF;
}
