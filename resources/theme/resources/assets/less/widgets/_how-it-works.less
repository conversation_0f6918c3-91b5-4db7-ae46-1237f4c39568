.howItWorksWidget {
	padding: @white-space (@white-space / 3);
	height: 100%;
}

.howItWorksWidget__header {
	text-align: center;
	display: block;
	font-size: @font-size-h1;
	margin: 0 0 @white-space 0;
}

.howItWorksWidget__stepsContainer {
	max-width: @site-width * 0.75;
	margin: 0 auto;
	text-align: center;
}

.howItWorksWidget__step {
	display: inline-block;
	margin: 0 auto;
	min-width: 235px;
	max-width:  33.33333%;
	width: 100%;
	padding: (@white-space / 4);
	vertical-align: top;
	p {
		line-height: 1.5em;
	}
}

.howItWorksWidget__stepsContainer--half-width {
	.howItWorksWidget__step {
		display: block;
		min-width: 0;
		max-width: 400px;
		width: 100%;
		padding: (@white-space / 4);
	}	
}

.howItWorksWidget__stepHeader {
	text-align: center;
	margin-bottom: 14px;
	font-size: 26px;
	font-weight: bold;
	.bullet {
		margin: 0 auto 20px auto;
		font-size: 36px;
		display: flex;
		text-align: center;
		border-radius: 9999px;
		height: 58px;
		width: 58px;
		align-items: center;
		justify-content: center;
	}
}

.howItWorksWidget__cta {
	text-align: center;
	margin: 20px 0px 0px 0px;
	  > a {
	    display: inline-block;
	    font-size: 22px;
	    padding: 10px 30px;
	    margin: 22px auto 0 auto;
	  }
}

@media (max-width: @mobile-width) {
	.howItWorksWidget__stepsContainer {
		max-width:  100%;
		width: 100%;
		margin: 0 auto;
		text-align: center;
	}

	.howItWorksWidget__step {
		display: block;
		max-width:  (@site-width / 3);
		width: 100%;
		p {
			text-align: center;
		}
	}
}	