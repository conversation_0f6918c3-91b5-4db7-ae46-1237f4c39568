.cartItems {
	padding-top: 0;
	margin-bottom: (@white-space / 3);
}

.cartItems__itemContainer {
	width: 100%;
	border-bottom: solid 1px #EEE;
	padding: 0 0 15px 0;
	display: flex;
	align-items: center;
}
.cartItems__itemContainer + .cartItems__itemContainer {
	padding: 15px 0;
}

.cartItems__itemContainer.outOfStock {
	opacity:0.7;
}

.cartItems section:last-of-type {
    border-bottom: none;
    padding-bottom: 0;
}

.cartItems__outOfSockAlert {
	margin-bottom:5px;
	flex: 0 0 100%;
	font-size: 13px;
}


.cartItems__itemHeading {
	display: flex;
    flex: 1 1 auto;
    padding-right: 0.10rem;
}

.cartItems__itemPhoto {
	flex: 0 0 auto;
	display: block;
	margin-right: (@white-space / 4);
	img {
		display: inline-block;
		max-width: 100px;
		max-height: 75px;
	}
}

.cartItems__changePromoLink {
	font-size:13px;
	display:inline-block;
	margin-top:12px;
}

.cartItems__itemTitle {
	flex: 1 1 auto;
}

.cartItems__itemTitleHeading {
    font-weight: normal;
    font-size: 1.25rem;
    line-height: 1.5;
	padding: 0;
    padding-right: 0.5rem;
	margin: 0;
	a {
		color: inherit;
	}
	a:hover, a:focus {
		color: inherit;
	}
}

.cartItems__itemSubtotal {
	margin: 0.25em 0 0 0;
	color: #5d5d5d;
	font-size: 16px;
	font-weight: bolder;
}

.cartItems__itemPrice {
	color: #7d7d7d;
	font-size: 13px;
}

.cartItems__onSale {
	font-size:13px;
	color:red;
}

.cartItems__controls {
    flex: 0 1 auto;
	width: auto;
	margin-left: auto;
	text-align: right;
}


.cartItems__delete {
	margin-top: 0.15rem;
	text-align: right;
}

.btn--removeFromCart {
	font-size: 11px;
	color: #7d7d7d;
	padding: 0.75rem 0 0.75rem 0.75rem;
	background-color: transparent;
}

.qty-input-field {
	width: 65px;
}

// Fees
.cartItems__feesContainer {
	width: 100%;
	overflow: auto;
	margin-bottom: (@white-space / 2);
}

.cartItems__feesTable {
	width: 100%;
	margin-bottom: 0;
}


@media (max-width: @mobile-width) {
	.cartItems__itemTitleHeading {
		font-size: 14px !important;
	}

	.cartItems__itemPhoto {
		margin-right: (@white-space / 6);
		img {
			max-width: 90px;
			max-height: 59px;
		}
	}
}

.cartItems__itemGroup {
	border: 1px solid @gray-lighter;
	padding: 1rem;
	border-radius: 5px;
	padding-left: 1.25rem;
	padding-right: 1.25rem;
}

.cartItems__itemGroupHeading {
    font-size: 1.25rem;
    color: @gray-light;
	padding: 0 0 1rem 0;
	margin: 0;
}
