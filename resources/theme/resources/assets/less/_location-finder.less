.locationFinder {
  display: -ms-flexbox;
  display: flex;
}

.locationFinder__listContainer {
  display: inline-block;
  max-width: 300px;
  height: 600px;
  width: 30%;
  -ms-flex: 0 0 300px;
  flex: 0 0 300px;
  // overflow: hidden;
}

.locationFinder__mapContainer {
  position: relative;
  display: inline-block;
  vertical-align: top;
  width: 70%;
  -ms-flex: 1 1 0auto;
  flex: 1 1 auto;
  z-index: 100;
}

.locationFinder__individualPageContainer {
  margin-bottom: @white-space;
}

.locationFinder__search {
  padding: (@white-space / 4);
  background-color: transparent;
  box-shadow: 0px 2px 7px -2px rgba(111, 111, 111, 0.25);
}

.locationFinder__map {
  width: auto;
  height: 600px;
  vertical-align: top;
  position: relative;
}

.homeDeliveryAlert {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  text-align: center;
  color: #3d3d3d;
  padding: 0 0.75rem;
  z-index: 999;
  .homeDeliveryAlert_innerContainer {
    display: inline-block;
    background-color: rgba(255, 255, 255, 0.95);
    padding: 0.75rem 1.25rem;
    font-size: 1.25rem;
    font-weight: bold;
    border-radius: 0 0 0.25rem 0.25rem;
    box-shadow: 0 7px 14px 0 rgba(50, 50, 93, 0.1),
      0 3px 6px 0 rgba(0, 0, 0, 0.07);
  }
}

// Location List
.locationsList {
  padding: (@white-space / 4);
  height: auto;
  max-height: 600px - 115px;
  overflow-y: auto;
  list-style-type: none;
}

.locationList {
  list-style-type: none;
  padding: (@white-space / 6) 0;
  margin-bottom: (@white-space / 4);
  border-bottom: solid 1px #eee;
  > li {
    margin-bottom: (@white-space / 10);
  }
}

.locationList__heading {
  margin-bottom: 3px;
}

.locationList__heading > a {
  font-size: 16px;
  font-style: bolder;
}

.locationList__address {
  color: #aaa;
  cursor: pointer;
}

.locationList__comingSoon {
  margin: 8px auto;
  width: 100%;
  display: block;
}

.locationFinder__hostLocationLink {
  position: absolute;
  top: 10px;
  right: 45px;
  z-index: 1000;
  box-shadow: 0px 2px 7px -2px rgba(111, 111, 111, 0.25);
  & > a {
    margin-right: 2px;
  }
}

@media (max-width: 768px) {
  .locationFinder {
    display: block;
  }
  .locationFinder__listContainer {
    display: block;
    max-width: 100%;
    width: 100%;
    height: auto;
    overflow: visible;
    margin-bottom: (@white-space / 2);
  }
  .locationFinder__mapContainer {
    display: block;
    width: 100%;
  }
  .locationsList {
    height: 100%;
    max-height: none;
    overflow-y: visible;
  }
}
