.cartMenu {
	position: fixed;
	top: -75px;
	left: 0;
    z-index: 10;
	background-color: rgba(255, 255, 255, 0.98);
	border-bottom: solid 1px #f3f3f3;
	width: 100%;
	list-style-type: none;
	margin: 0;
	padding: 0;
	text-align: center;
	transition: all 0.3s ease-in-out;
	> li {
		display: inline-block;
		padding: (@white-space / 12) (@white-space / 4);
		> a {
			color: #777;
		}
	}
}

.cartMenu--show {
	top: 0;
	opacity: 100;
	box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 16px 0px;
}

@media (max-width: 768px) {
	.cartMenu {
		text-align: center;
	}
}
