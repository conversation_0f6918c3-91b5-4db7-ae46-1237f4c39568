import forms from '@tailwindcss/forms';
import typography from '@tailwindcss/typography';
import nesting from '@tailwindcss/nesting';
import aspectRatio from '@tailwindcss/aspect-ratio';

/** @type {import('tailwindcss').Config} */
export default {
    prefix: 'tw-',
    important: '.tw-reset',
    content: [
        './resources/views/**/*.blade.php',
        './resources/assets/js/**/*.js',
        './resources/assets/js/**/*.vue'
    ],
    theme: {
        extend: {
            colors: {
                'theme-brand-color': 'rgb(var(--brand-color) / <alpha-value>)',
                'theme-brand-color-inverted': 'rgb(var(--brand-color-inverted) / <alpha-value>)',
                'theme-background-color': 'rgb(var(--background-color) / <alpha-value>)',
                'theme-text-color': 'rgb(var(--text-color) / <alpha-value>)',
                'theme-link-color': 'rgb(var(--link-color) / <alpha-value>)',
                'theme-action-color': 'rgb(var(--action-color) / <alpha-value>)',
                'theme-action-color-inverted': 'rgb(var(--action-color-inverted) / <alpha-value>)',
                'theme-announcement-bar-bg-color': 'rgb(var(--announcement-bar-bg-color) / <alpha-value>)',
                'theme-announcement-bar-text-color': 'rgb(var(--announcement-bar-text-color) / <alpha-value>)',
                'theme-announcement-bar-link-color': 'rgb(var(--announcement-bar-link-color) / <alpha-value>)',
                'theme-header-bg-color': 'rgb(var(--header-bg-color) / <alpha-value>)',
                'theme-header-border-color': 'rgb(var(--header-border-color) / <alpha-value>)',
                'theme-main-navigation-bg-color': 'rgb(var(--main-navigation-bg-color) / <alpha-value>)',
                'theme-main-navigation-link-color': 'rgb(var(--main-navigation-link-color) / <alpha-value>)',
                'theme-main-navigation-link-color-hover': 'rgb(var(--main-navigation-link-color-hover) / <alpha-value>)',
                'theme-main-navigation-link-bg-color': 'rgb(var(--main-navigation-link-bg-color) / <alpha-value>)',
                'theme-auxiliary-bg-color': 'rgb(var(--auxiliary-bg-color) / <alpha-value>)',
                'theme-auxiliary-border-color': 'rgb(var(--auxiliary-border-color) / <alpha-value>)',
                'theme-auxiliary-link-color': 'rgb(var(--auxiliary-link-color) / <alpha-value>)',
                'theme-order-status-bg-color': 'rgb(var(--order-status-bg-color) / <alpha-value>)',
                'theme-order-status-color': 'rgb(var(--order-status-color) / <alpha-value>)',
                'theme-store-menu-bg_color': 'rgb(var(--store-menu-bg_color) / <alpha-value>)',
                'theme-store-menu-color': 'rgb(var(--store-menu-color) / <alpha-value>)',
                'theme-footer-bg-color': 'rgb(var(--footer-bg-color) / <alpha-value>)',
                'theme-footer-color': 'rgb(var(--footer-color) / <alpha-value>)',
                'theme-footer-link-color': 'rgb(var(--footer-link-color)  <alpha-value>)'
            },

            fontFamily: {
                'display': 'var(--display-font-family)',
                'body': 'var(--body-font-family)',
                'serif': 'var(--display-font-family)',
                'sans': 'var(--body-font-family)'
            }
        }
    },
    safelist: [
        // Google Autocomplete classes - https://developers.google.com/maps/documentation/javascript/place-autocomplete#style-autocomplete
        'pac-container', 'pac-icon', 'pac-item', 'pac-item:hover', 'pac-item-selected', 'pac-item-query', 'pac-matched'
    ],
    plugins: [
        forms,
        typography,
        nesting,
        aspectRatio
    ]
};
