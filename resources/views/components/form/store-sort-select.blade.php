@props(['selected' => null])

@php
    $options = [
        'title-asc' => 'Title: A-Z',
        'title-desc' => 'Title: Z-A',
        'unit_price-asc' => 'Price: Lowest to highest',
        'unit_price-desc' => 'Price: Highest to lowest',
        'created_at-desc' => 'Date: Newest to oldest',
        'created_at-asc' => 'Date: Oldest to newest',
        'sku-asc' => 'SKU: A-Z',
        'sku-desc' => 'SKU: Z-A',
    ];
@endphp

<select {{ $attributes }}>
    @foreach($options as $key => $label)
        <option value="{{ $key }}" @selected($key === $selected)>{{ $label }}</option>
    @endforeach
</select>
