<table class="table table-striped table-settings">
    <tbody>
    <tr>
        <td>
            <h2>Products to Display</h2>
            <p>Control which products to display with a collection.</p>
        </td>
        <td>
            <x-form.collection-select
                    class="form-control"
                    name="collection_id"
                    :selected="(int) (isset($widget->settings->collection) ? $widget->settings->collection : 0)"
            />
        </td>
    </tr>

    <tr>
        <td>
            <h2>Product Count</h2>
            <p>The maximum number of products that will be displayed at one time.</p>
        </td>
        <td>
            <input type="text" name="settings[count]" value="{{ $widget->settings->count ?? '' }}" class="form-control">
        </td>
    </tr>

    <tr>
        <td>
            <h2>Header</h2>
            <p>Will display above the products. Leave blank if you wish to not display a header.</p>
        </td>
        <td>
            <input type="text" name="settings[header]" value="{{ $widget->settings->header ?? '' }}" class="form-control">
        </td>
    </tr>

    <tr>
        <td>
            <h2>Photo Width</h2>
            <p>The width of the photo in pixels.</p>
        </td>
        <td>
            <input type="text" name="settings[width]" value="{{ $widget->settings->width ?? '' }}" class="form-control"
                   placeholder="Example: 200px">
        </td>
    </tr>

    <tr>
        <td>
            <h2>Photo Height</h2>
            <p>The height of the photo in pixels.</p>
        </td>
        <td>
            <input type="text" name="settings[height]" value="{{ $widget->settings->height ?? '' }}" class="form-control"
                   placeholder="Example: 200px">
        </td>
    </tr>

    <tr>
        <td>
            <h2>Background Color</h2>
            <p>The background color of the widget container.</p>
        </td>
        <td>
            <input type="text" name="settings[background]" value="{{ $widget->settings->background ?? '#FFF' }}" class="form-control colorpicker">
        </td>
    </tr>
    </tbody>
</table>
