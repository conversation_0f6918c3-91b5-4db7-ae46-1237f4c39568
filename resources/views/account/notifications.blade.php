@extends('layouts.main')

@section('toolbar-breadcrumb')
    <li>Account / Notifications</li>
@stop

@section('content')
    <div class="row">
        <div class="sidebar sidebar-1 sidebar-left">
            @include('account.partials.navigation')
        </div>
        <div class="content">
            <div class="panel">
                <div class="panel-heading flex align-items-m">
                    <div class="flex-item">
                        Admin Notfications
                    </div>
                </div>
                <div class="panel-body br-sm br-t pa-0">
                    <form action="/admin/account/notifications" method="POST" id="notificationsSettingsForm">
                        @csrf
                        @method('PUT')
                        <table class="table table-settings table-striped">
                            <tbody>
                            <tr>
                                <td>
                                    <h2>Contact Form Notification</h2>
                                    <p>Send an email notification to
                                        <span class="text-gray-4">{{ $user->email }}</span>
                                        when someone submits the contact form.
                                    </p>
                                </td>
                                <td>
                                    <div class="radio">
                                        <label class="mr-sm">
                                            <input tabindex="1" type="radio" name="settings[email_notify_contact_form]" value="1" @if($user->setting('email_notify_contact_form')) checked @endif>
                                            Yes
                                        </label>
                                        <label>
                                            <input tabindex="1" type="radio" name="settings[email_notify_contact_form]" value="0" @if(!$user->setting('email_notify_contact_form')) checked @endif>
                                            No
                                        </label>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <h2>New Order Notification</h2>
                                    <p>Send an email notification to
                                        <span class="text-gray-4">{{ $user->email }}</span>
                                        when a customer places an order.
                                    </p>
                                </td>
                                <td>
                                    <div class="radio">
                                        <label class="mr-sm">
                                            <input tabindex="1" type="radio" name="settings[email_notify_new_orders]" value="1" @if($user->setting('email_notify_new_orders')) checked @endif>
                                            Yes
                                        </label>
                                        <label>
                                            <input tabindex="1" type="radio" name="settings[email_notify_new_orders]" value="0" @if(!$user->setting('email_notify_new_orders')) checked @endif>
                                            No
                                        </label>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <h2>Order Cancellation Notification</h2>
                                    <p>Send an email notification to
                                        <span class="text-gray-4">{{ $user->email }}</span>
                                        when a customer cancels an order.
                                    </p>
                                </td>
                                <td>
                                    <div class="radio">
                                        <label class="mr-sm">
                                            <input tabindex="1" type="radio" name="settings[email_notify_cancellation]" value="1" @if($user->setting('email_notify_cancellation')) checked @endif>
                                            Yes
                                        </label>
                                        <label>
                                            <input tabindex="1" type="radio" name="settings[email_notify_cancellation]" value="0" @if(!$user->setting('email_notify_cancellation')) checked @endif>
                                            No
                                        </label>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <h2>Order Comment Notification</h2>
                                    <p>Send an email notification to
                                        <span class="text-gray-4">{{ $user->email }}</span>
                                        when a customer adds a comment to their order.
                                    </p>
                                </td>
                                <td>
                                    <div class="radio">
                                        <label class="mr-sm">
                                            <input tabindex="1" type="radio" name="settings[email_notify_order_comment]" value="1" @if($user->setting('email_notify_order_comment')) checked @endif>
                                            Yes
                                        </label>
                                        <label>
                                            <input tabindex="1" type="radio" name="settings[email_notify_order_comment]" value="0" @if(!$user->setting('email_notify_order_comment')) checked @endif>
                                            No
                                        </label>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <h2>Proposal Notification</h2>
                                    <p>Send an email notification to
                                        <span class="text-gray-4">{{ $user->email }}</span>
                                        when someone submits a pickup proposal.
                                    </p>
                                </td>
                                <td>
                                    <div class="radio">
                                        <label class="mr-sm">
                                            <input tabindex="1" type="radio" name="settings[email_notify_new_proposals]" value="1" @if($user->setting('email_notify_new_proposals')) checked @endif>
                                            Yes
                                        </label>
                                        <label>
                                            <input tabindex="1" type="radio" name="settings[email_notify_new_proposals]" value="0" @if(!$user->setting('email_notify_new_proposals')) checked @endif>
                                            No
                                        </label>
                                    </div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </form>
                </div>
                <div class="panel-footer text-right">
                    <button type="submit" class="btn btn-action" @click.prevent="submitForm('notificationsSettingsForm')">Save</button>
                </div>
            </div>
        </div>
    </div>
@stop
