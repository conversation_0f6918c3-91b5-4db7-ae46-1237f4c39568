@extends('layouts.main')

@section('toolbar-breadcrumb')
    <li>Account / API Tokens</li>
@stop
@section('content')
    <div class="flex">
        <div class="sidebar sidebar-1 sidebar-left">
            @include('account.partials.navigation')
        </div>
        <div class="flex-1 px-4 sm:px-6 lg:px-8">
            <div class="sm:flex sm:items-center">
                <div class="sm:flex-auto">
                    <h1 class="text-xl font-semibold text-gray-900">API Tokens</h1>
                    <p class="m-0 mt-2 text-sm text-gray-700">A list of all the active API tokens associated with your user account.</p>
                </div>
            </div>
            @unless(empty($token))
                <div class="flex p-4 mt-4 text-sm text-blue-700 bg-blue-100 rounded-lg" role="alert">
                    <svg aria-hidden="true" class="flex-shrink-0 inline w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="sr-only">New token INfo</span>
                    <div>
                        <span class="font-medium"><p class="mb-0">Copy your token now. It will not be shown again!</p></span>
                        <ul class="mt-3 ml-2 text-blue-700 list-disc list-inside">
                            <li><b>{{ $token ?? '2' }}</b></li>
                        </ul>
                    </div>
                </div>
            @endunless
            <div class="mt-4 bg-white shadow sm:rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900">Generate Token</h3>
                    <div class="mt-2 max-w-xl text-sm text-gray-500">
                        <p>Generate a new API token that can be used with the GrazeCart API.</p>
                    </div>
                    <form action="{{ route('admin.account.api-tokens.store') }}" method="POST" class="mt-5 sm:flex sm:items-center">
                        @csrf
                        <div class="w-full sm:max-w-xs">
                            <label for="token_name" class="sr-only">Name</label>
                            <input type="text" name="token_name" id="token_name" placeholder="Enter a helpful name" class="shadow-sm focus:ring-keppel-500 focus:border-keppel-500 block w-full sm:text-sm border-gray-300 rounded-md">
                        </div>
                        <button type="submit" class="mt-3 w-full inline-flex items-center justify-center px-4 py-2 border border-transparent shadow-sm font-medium rounded-md text-white bg-keppel-600 hover:bg-keppel-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-keppel-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                            Generate
                        </button>
                    </form>
                </div>
            </div>
            <div class="mt-8 flex flex-col">
                <div class="-my-2 -mx-4 overflow-x-auto sm:-mx-6 lg:-mx-8">
                    <div class="inline-block min-w-full py-2 align-middle md:px-6 lg:px-8">
                        <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
                            <table class="min-w-full divide-y divide-gray-300">
                                <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">Name</th>
                                    <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Last Used</th>
                                    <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Created</th>
                                    <th scope="col" class="relative py-3.5 pl-3 pr-4 sm:pr-6">
                                        <span class="sr-only">Revoke</span>
                                    </th>
                                </tr>
                                </thead>
                                <tbody class="divide-y divide-gray-200 bg-white">
                                @foreach(auth()->user()->tokens as $user_token)
                                    <tr>
                                        <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-6">{{ $user_token->name }}</td>
                                        <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">{{ $user_token->last_used_at?->format('M jS, Y | h:iA') ?? '-' }}</td>
                                        <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">{{ $user_token->created_at->format('M jS, Y | h:iA') }}</td>
                                        <td class="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
                                            <form action="{{ route('admin.account.api-tokens.destroy', $user_token) }}" method="POST">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="text-red-600 hover:text-red-900">Revoke
                                                    <span class="sr-only">, Token</span>
                                                </button>
                                            </form>
                                        </td>
                                    </tr>
                                @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@stop
