<div class="mt-4 bg-white shadow px-4 py-5 sm:rounded-lg sm:p-6">
    <div class="sm:flex sm:items-center">
        <div class="sm:flex-auto">
            <h1 class="text-xl font-semibold text-gray-900">Delivery methods</h1>
            <p class="mt-2 text-sm text-gray-700">A list of all pickup locations and delivery methods using this schedule.</p>
        </div>
        <div class="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
{{--            <button type="button" class="inline-flex items-center justify-center rounded-md border border-transparent bg-keppel-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-keppel-700 focus:outline-none focus:ring-2 focus:ring-keppel-500 focus:ring-offset-2 sm:w-auto">Add user</button>--}}
        </div>
    </div>
    @if($schedule->pickups->isNotEmpty())
        <div class="mt-8 flex flex-col">
            <div class="-my-2 -mx-4 overflow-x-auto sm:-mx-6 lg:-mx-8">
                <div class="inline-block min-w-full py-2 align-middle md:px-6 lg:px-8">
                    <table class="min-w-full divide-y divide-gray-300">
                        <thead>
                            <tr>
                                <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6 md:pl-0">Name</th>
                                <th scope="col" class="py-3.5 px-3 text-left text-sm font-semibold text-gray-900">Type</th>
                                <th scope="col" class="relative py-3.5 pl-3 pr-4 sm:pr-6 md:pr-0">
                                    <span class="hidden">Edit</span>
                                </th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-200">
                            @foreach($schedule->pickups as $pickup)
                                <tr>
                                    <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-6 md:pl-0">{{ $pickup->title }}</td>
                                    <td class="whitespace-nowrap py-4 px-3 text-sm text-gray-500"> {{ $pickup->isPickup() ? 'Pickup location' : 'Delivery zone' }}</td>
                                    <td class="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6 md:pr-0">
                                        <a href="{{ route($pickup->isPickup() ? 'admin.pickups.edit' : 'admin.delivery.edit', [$pickup]) }}" class="text-keppel-600 hover:text-keppel-900">View<span class="hidden">, {{ $pickup->title }}</span></a>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    @else
        <div class="mt-8 text-center">
            <svg class="mx-auto h-12 w-12 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M11.25 11.25l.041-.02a.75.75 0 011.063.852l-.708 2.836a.75.75 0 001.063.853l.041-.021M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9-3.75h.008v.008H12V8.25z" />
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">No delivery methods</h3>
            <p class="mt-1 text-sm text-gray-500">Get started by assigning this schedule to a pickup location or delivery zone.</p>
        </div>
    @endif
</div>