@extends('layouts.main', ['pageTitle' => $schedule->title])

@php /** @var App\Models\Schedule $schedule */ @endphp

@section('toolbar-breadcrumb')
    <li><a href="/admin/schedules">Schedules</a></li>
    <li>{{ $schedule->title }}</li>
@endsection

@section('toolbar-buttons')
    <button class="btn btn-light mr-sm" @click="showModal('sendNotificationModal')">Send Notification</button>

    <div class="dropdown mr-sm">
        <button type="button" class="btn btn-light dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
            <i class="fas fa-gear"></i> Actions <i class="fas fa-caret-down"></i>
        </button>
        <ul class="dropdown-menu pull-right">
            <li><a href="#" @click="showModal('deleteScheduleModal')">Delete</a></li>
        </ul>
    </div>
@endsection

@section('content')

    @php
        $current_tab = Request::get('tab', 'dates');
    @endphp

    <div class="relative border-b border-gray-200 pb-5 sm:pb-0">
        <div class="sm:flex sm:items-baseline sm:justify-between">
            <div class="sm:w-0 sm:flex-1">
                <h1 id="message-heading" class="text-lg font-medium text-gray-900">{{ $schedule->title }}</h1>
            </div>
            <div class="mt-4 flex items-center justify-between space-x-2 sm:mt-0 sm:ml-6 sm:flex-shrink-0 sm:justify-start">
                @if ($schedule->isCustom())
                    <span class="inline-flex items-center rounded-full bg-yellow-100 px-3 py-0.5 text-sm font-medium text-yellow-800">Custom</span>
                @else
                    @if( ! $schedule->isActive())
                        <span class="inline-flex items-center rounded-full bg-gray-100 px-3 py-0.5 text-sm font-medium text-gray-800">Inactive</span>
                    @endif
                    <span class="inline-flex items-center rounded-full bg-blue-100 px-3 py-0.5 text-sm font-medium text-blue-800">Repeating</span>
                @endif
            </div>
        </div>
        <div class="mt-4">
            <!-- Dropdown menu on small screens -->
            <div class="sm:hidden">
                <label for="current-tab" class="hidden">Select a tab</label>
                <select id="current-tab" name="current-tab" onchange="setActiveTab(this.value)" class="block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:border-keppel-500 focus:outline-none focus:ring-keppel-500 sm:text-sm">
                    <option value="dates" @if($current_tab === 'dates') selected @endif>Dates</option>
                    <option value="settings" @if($current_tab === 'settings') selected @endif>Settings</option>
                    <option value="notifications" @if($current_tab === 'notifications') selected @endif>Notifications</option>
                    <option value="delivery-methods" @if($current_tab === 'delivery-methods') selected @endif>Delivery methods</option>
                </select>
            </div>
            <!-- Tabs at small breakpoint and up -->
            <div class="hidden sm:block">
                <nav class="-mb-px flex space-x-8">
                    <a href="{{ route('admin.schedules.edit', ['schedule' => $schedule, 'tab' => 'dates']) }}" class="@if($current_tab === 'dates') border-keppel-500 text-keppel-600 @else border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 @endif whitespace-nowrap pb-4 px-1 border-b-2 font-medium text-sm">Dates</a>
                    <a href="{{ route('admin.schedules.edit', ['schedule' => $schedule, 'tab' => 'settings']) }}" class="@if($current_tab === 'settings') border-keppel-500 text-keppel-600 @else border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 @endif whitespace-nowrap pb-4 px-1 border-b-2 font-medium text-sm" aria-current="page">Settings</a>
                    <a href="{{ route('admin.schedules.edit', ['schedule' => $schedule, 'tab' => 'notifications']) }}" class="@if($current_tab === 'notifications') border-keppel-500 text-keppel-600 @else border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 @endif whitespace-nowrap pb-4 px-1 border-b-2 font-medium text-sm">Notifications</a>
                    <a href="{{ route('admin.schedules.edit', ['schedule' => $schedule, 'tab' => 'delivery-methods']) }}" class="@if($current_tab === 'delivery-methods') border-keppel-500 text-keppel-600 @else border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 @endif whitespace-nowrap pb-4 px-1 border-b-2 font-medium text-sm">Delivery
                        methods</a>
                </nav>
            </div>
        </div>
    </div>

    @include("schedules.partials.".$current_tab)

    @include('schedules.partials.delete-schedule-modal')
    @include('schedules.partials.create-date-modal')
    @include('schedules.partials.notification-modal')
    @if($schedule->delivery_frequency === 0)
        @include('schedules.partials.activate-schedule-multi-day-modal')
    @else
        @include('schedules.partials.activate-schedule-modal')
    @endif
@endsection
@section('scripts')
    <script>
        let toggleDisabled = function(element, state) {
            if (state == true) {
                $(element).prop('disabled', false);
            } else {
                $(element).prop('disabled', true);
            }
        };

        document.addEventListener('DOMContentLoaded', () => {

            $('.pickup-date').daterangepicker({
                singleDatePicker: true,
                showDropdowns: true,
                parentEl: '#createDateModal .modal-dialog',
                locale: {
                    format: 'YYYY-MM-DD'
                }
            });

            $('.order-window').daterangepicker({
                timePicker: false,
                timePickerIncrement: 30,
                parentEl: '#createDateModal .modal-dialog',
                locale: {
                    format: 'YYYY-MM-DD'
                }
            });

            $('.repeat-endsection').daterangepicker({
                singleDatePicker: true,
                showDropdowns: true,
                parentEl: '#createDateModal .modal-dialog',
                locale: {
                    format: 'YYYY-MM-DD'
                }
            });

            $('#repeatingDatesCheckbox').on('change', function() {
                var checkbox = $(this);
                if (checkbox.is(':checked')) {
                    $('#repeatingDatesContainer').removeClass('hidden');
                } else {
                    $('#repeatingDatesContainer').addClass('hidden');
                }
            });

            $('input:radio').click(function() {
                if ($('#ordercapacity_yes').is(':checked')) {
                    $('#capacity_limit').removeAttr('disabled');
                    $('#capacity_limit').focus();
                } else {
                    $('#capacity_limit').attr('disabled', 'disabled');
                }
            });

            toggleDisabled('.reminder', {{ $schedule->reminder_enabled }});
            toggleDisabled('.secondary_reminder', {{ $schedule->secondary_reminder_enabled }});
            toggleDisabled('.subscription_reminder', {{ $schedule->subscription_reminder_enabled }});

            var orderCapacityCheckedValue = $('#ordercapacity_yes').is(':checked');
            if (orderCapacityCheckedValue) {
                $('#capacity_limit').removeAttr('disabled');
            }
        });

        function setActiveTab(tab) {
            window.location = "{{ route('admin.schedules.edit', $schedule->id) }}?tab=" + tab;
        }
    </script>
@endsection
