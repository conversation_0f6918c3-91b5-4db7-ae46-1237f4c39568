<form action="{{ route('admin.products.update', $product->id) }}" method="POST" id="productForm">
    @csrf
    @method('put')
    <div class="panel">
        <div class="panel-body pa-0">
            <table class="table   table-striped table-settings">
                <tbody>
                {{--Product Visibility--}}
                <tr>
                    <td>
                        <h2>Visibility</h2>
                        <p>Should this product be visible in the store?</p>
                    </td>
                    <td>
                        <div class="radio">
                            <label class="mr-sm">
                                <input tabindex="1" type="radio" name="visible" value="1" @if($product->visible) checked @endif> Yes
                            </label>
                            <label>
                                <input tabindex="1" type="radio" name="visible" value="0" @if(!$product->visible) checked @endif> No
                            </label>
                        </div>
                    </td>
                </tr>

                {{--Product Taxable--}}
                <tr>
                    <td>
                        <h2>Taxable</h2>
                        <p>Should tax be applied to this product?</p>
                    </td>
                    <td>
                        <div class="radio">
                            <label class="mr-sm">
                                <input tabindex="1" type="radio" name="taxable" value="1" @if($product->taxable) checked @endif> Yes
                            </label>
                            <label>
                                <input tabindex="1" type="radio" name="taxable" value="0" @if(!$product->taxable) checked @endif> No
                            </label>
                        </div>
                    </td>
                </tr>
                {{--Product SKU--}}
                <tr>
                    <td>
                        <h2>Product SKU</h2>
                        <p>Stock-keeping unit is a unique identifier for each distinct product.</p>
                    </td>
                    <td>
                        <input type="text" name="sku" value="{{ $product->sku }}" class="form-control" tabindex="1"/>
                    </td>
                </tr>

                <tr>
                    <td>
                        <h2>Barcode</h2>
                    </td>
                    <td>
                        <input type="text" name="barcode" value="{{ $product->barcode }}" class="form-control" tabindex="1"/>
                    </td>
                </tr>

                <tr>
                    <td>
                        <h2>Category</h2>
                        <p>Set the category used to organize this product.</p>
                    </td>
                    <td>
                        <x-form.category-select
                                class="form-control"
                                name="category_id"
                                :selected="(int) $product->category_id"
                        />
                    </td>
                </tr>

                {{--Storage Class--}}
                <tr>
                    <td>
                        <h2>Packing Group</h2>
                        <p>This is a way to organize your products based on how they are packed.</p>
                    </td>
                    <td>
                        <x-form.packing-group-select
                                class="form-control"
                                name="inventory_type"
                                :selected="(int) $product->packingGroup->id"
                        />
                    </td>
                </tr>
                <tr>
                    <td>
                        <h2>Notes</h2>
                        <p>Any internal notes you want to keep about this product. This will NOT be seen by the customer.</p>
                    </td>
                    <td>
                        <textarea class="form-control" name="notes" tabindex="1" rows="5">{{ $product->notes }}</textarea>
                    </td>
                </tr>

                </tbody>
            </table>
        </div>
        <div class="panel-footer text-right">
            <button class="btn btn-action" @click="submitForm('productForm', $event)">Save</button>
        </div>
    </div>
    <div class="panel">
        <div class="panel-body pa-0">
            <table class="table   table-striped table-settings">
                <tbody>
                {{--Product Vendor--}}
                <tr>
                    <td>
                        <h2>Packing List Display</h2>
                        <p>Set how to display this product on the packing list when multiple quantities are purchased within a single order. The options
                            are: </p>
                        <ul class="ml-4 space-y-1 text-xs list-disc">
                            <li>Grouped as one line item, displaying the total quantity ordered <strong>(recommended)</strong></li>
                            <li>Itemized as multiple line items, each displaying a quantity of 1</li>
                        </ul>

                        <div class="mt-4 flex space-x-2">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M11.25 11.25l.041-.02a.75.75 0 011.063.852l-.708 2.836a.75.75 0 001.063.853l.041-.021M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9-3.75h.008v.008H12V8.25z"/>
                            </svg>
                            <p class="m-0">This setting has no impact on the "consolidated" packing list style</p>

                        </div>
                    </td>
                    <td>
                        <div class="radio">
                            <label class="mr-sm">
                                <input tabindex="1" type="radio" name="is_grouped" value="1" @if($product->is_grouped) checked @endif> Grouped
                            </label>
                            <label>
                                <input tabindex="1" type="radio" name="is_grouped" value="0" @if(!$product->is_grouped) checked @endif> Itemized
                            </label>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>
                        <h2>Product Vendor</h2>
                        <p>Associate this product with a specific vendor.</p>
                    </td>
                    <td>
                        <x-form.vendor-select
                                class="form-control"
                                name="vendor_id"
                                tabindex="1"
                                placeholder="N/A"
                                :selected="(int) $product->vendor_id"
                        />
                    </td>
                </tr>
                {{--Search Tags--}}
                <tr>
                    <td>
                        <h2>Keywords</h2>
                        <p>Add keywords separated by spaces to optimize how this product will show up in the store search.</p>
                    </td>
                    <td>
                        <textarea name="keywords" rows="5" class="form-control">{{ $product->keywords }}</textarea>
                    </td>
                </tr>
                <tr>
                    <td>
                        <h2>Limit Per Customer</h2>
                        <p>Specify the maximum quantity a customer can add per order.</p>
                    </td>
                    <td>
                        <input type="number" name="settings[quantity_limit]" value="{{ $product->setting('quantity_limit') }}" class="form-control">
                    </td>
                </tr>
                @if($product_type_id !== \App\Support\Enums\ProductType::PREORDER->value)
                    <tr>
                        <td>
                            <h2>Subtotal Minimum</h2>
                            <p>The cart/order subtotal must be at least this amount to purchase this product. No minimum is enforced when this field is
                                blank.</p>
                        </td>
                        <td>
                            <div class="input-group">
                                <span class="input-group-addon">$</span>
                                <input type="number" min="0" name="settings[order_minimum]" class="form-control flex-item" value="{{ $product->setting('order_minimum') }}"/>
                            </div>
                        </td>
                    </tr>
                @endif
                {{--Availability--}}
                <tr>
                    <td>
                        <h2>Exclusivity</h2>
                        <p>You can exclude this product from being available at particular locations. By default a product will be available at all
                            locations.
                            Select any of the locations that you DO NOT want this product to be available at.</p>
                    </td>
                    <td>
                        <input type="hidden" name="pickups" value="0">
                        <x-form.delivery-method-select
                                class="form-control select2"
                                name="pickups[]"
                                tabindex="1"
                                data-placeholder="No locations excluded"
                                multiple
                                style="width: 100%"
                                :selected="$product->pickups()->pluck('pickups.id')->toArray()"
                        />
                    </td>
                </tr>

                {{--Product Location Identifier--}}
                <tr>
                    <td>
                        <h2>Storage Location ID</h2>
                        <p>This can be used for sorting items on the order edit screen or on packing lists to correspond to how items are stored in your
                            warehouse.</p>
                    </td>
                    <td>
                        <input type="text" name="custom_sort" value="{{ $product->custom_sort }}" class="form-control" tabindex="1"/>
                    </td>
                </tr>

                {{--Accounting Class--}}
                <tr>
                    <td>
                        <h2>Accounting Class ID</h2>
                        <p>This is used when filtering products for accounting purposes.</p>
                    </td>
                    <td>
                        <input type="text" name="accounting_class" value="{{ $product->accounting_class }}" class="form-control">
                    </td>
                </tr>

                <tr>
                    <td>
                        <h2>Hide From Search</h2>
                        <p>Should this product show when searching the store?</p>
                    </td>
                    <td>
                        <div class="radio">
                            <label class="mr-sm">
                                <input tabindex="1" type="radio" name="hide_from_search" value="1" @if($product->hide_from_search) checked @endif> Hide From
                                Search
                            </label>
                            <label>
                                <input tabindex="1" type="radio" name="hide_from_search" value="0" @if(!$product->hide_from_search) checked @endif> Show In
                                Search
                            </label>
                        </div>
                    </td>
                </tr>

                {{--Location URL--}}
                <tr>
                    <td>
                        <h2>URL Slug</h2>
                        <p>This is the unique identifier used in URLs for this product. Use caution when changing this as any links or bookmarks pointing to
                            this product could be broken.</p>
                    </td>
                    <td>
                        <input type="text" name="slug" value="{{ $product->slug }}" class="form-control">
                    </td>
                </tr>

                <tr>
                    <td>
                        <h2>Standard callout message</h2>
                        <p>Set a short message that highlights this product. Note: an active sale callout message takes precedence over this message.</p>
                    </td>
                    <td>
                        <input type="text" name="settings[standard_callout_message]" value="{{ $product->setting('standard_callout_message') }}" class="form-control">
                    </td>
                </tr>
                <tr>
                    <td>
                        <h2>Sale callout message</h2>
                        <p>Set a short message that only shows when the product is actively on sale. Note: This message takes precedence over the standard
                            callout message.</p>
                    </td>
                    <td>
                        <input type="text" name="settings[sale_message]" value="{{ $product->setting('sale_message') }}" class="form-control">
                    </td>
                </tr>
                <tr>
                    <td>
                        <h2>Cart Action Label</h2>
                        <p>Set a custom "add-to-cart" label. This will override the default label except for the Subscription Products - Add to Order</p>
                        <ul>
                            <li><small>Standard: Add to Cart</small></li>
                            <li><small>Gift Card: Buy Now</small></li>
                            <li><small>Pre-order: Pre-order</small></li>
                        </ul>
                    </td>
                    <td>
                        <input type="text" name="settings[cart_action_label]" value="{{ $product->setting('cart_action_label') }}" class="form-control">
                    </td>
                </tr>


                @if($product_type_id !== \App\Support\Enums\ProductType::PREORDER->value)
                    <tr>
                        <td>
                            <h2>Link Externally</h2>
                            <p>Should this product link to a page outside of your site.</p>
                        </td>
                        <td>
                            <div class="radio">
                                <label class="mr-sm">
                                    <input tabindex="1" type="radio" name="settings[links_externally]" value="1" @if($product->setting('links_externally')) checked @endif>
                                    Yes
                                </label>
                                <label>
                                    <input tabindex="1" type="radio" name="settings[links_externally]" value="0" @if(!$product->setting('links_externally')) checked @endif>
                                    No
                                </label>
                            </div>
                        </td>
                    </tr>
                    @if($product->setting('links_externally'))
                        <tr>
                            <td>
                                <h2>External Link Text</h2>
                            </td>
                            <td>
                                <input type="text" name="settings[links_externally:text]" value="{{ $product->setting('links_externally:text', 'More Info') }}" class="form-control">
                            </td>
                        </tr>

                        <tr>
                            <td>
                                <h2>External Link URL</h2>
                            </td>
                            <td>
                                <input type="text" name="settings[links_externally:url]" value="{{ $product->setting('links_externally:url', null) }}" class="form-control" placeholder="https://some-url.com">
                            </td>
                        </tr>
                    @endif
                @endif
                </tbody>
            </table>
        </div>
        <div class="panel-footer text-right">
            <button class="btn btn-action" @click="submitForm('productForm', $event)">Save</button>
        </div>
    </div>
</form>    
