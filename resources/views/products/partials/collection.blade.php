<div class="panel ">
    <div class="panel-heading">Collections</div>
    <div class="panel-body">
        <div class="select">
            <div class="input-group">
                <input type="text" class="form-control" autocomplete="off" v-model="q" v-on="focus: showResults = true, blur: showResults = false" placeholder="add to collection" tabindex="1">
                <div class="input-group-btn">
                    <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false"><i class="fas fa-gear"></i> <span class="caret"></span></button>
                    <ul class="dropdown-menu pull-right">
                        <li><a href="/admin/collections">Edit Collections</a></li>
                    </ul>
                </div>
            </div>
            <ul class="select-results" v-if="showResults">
                <li v-show="!filteredCount">
                    <a href="#"><b>No results found</b></a>
                </li>
                <li v-repeat="result: results | filterBy q | count">
                    <span v-on="mousedown: toggleCollection(result, $event)"><i class="fas fa-lg" v-class="result.active ? 'fa-check-circle-o' : 'fa-circle-o'"></i> @{{ result.title }}</span>
                </li>
            </ul>
        </div>
        <div class="collections">
            <span v-if="!collectionsProductBelongsTo.length"><em>This product is not in any collections.</em></span>
            <ul class="list-group">
                <li class="list-group-item" v-repeat="collection: results | collectionsProductBelongsTo" v-transition="collection">
                    <a href="/admin/collections/@{{ collection.id }}/edit">@{{ collection.title }}</a> <a href="#" class="pull-right" v-on="click: toggleCollection(collection, $event)"><i class="fas fa-times"></i></a>
                </li>
            </ul>
        </div>
    </div>
</div>