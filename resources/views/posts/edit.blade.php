@extends('layouts.main', ['pageTitle' => $post->title])

{{--Toolbar--}}
@section('toolbar-breadcrumb')
    <li>
        <a href="{{ route('admin.posts.index', session('posts-filtered', [])) }}">Blog Posts</a>
    </li>
    <li class="toolbar-title">{{ $post->title }}</li>
@stop
@section('toolbar-buttons')
    @if($post->isDraft())
        @include('posts.partials.draft-toolbar')
    @else
        @include('posts.partials.published-toolbar')
    @endif
@stop

@section('content')
    @include('posts.partials.delete-post-modal')
    <div class="row">
        <div class="content">
            <div class="panel">
                <div class="panel-body">
                    <form action="{{ route('admin.posts.update', $post->id) }}" method="POST" id="postForm">
                        @csrf
                        @method('put')

                        <div class="form-group">
                            <label for="title">Post Title</label>
                            <input type="text" name="title" class="form-control" value="{{ old('title', $post->title) }}" />
                        </div>

                        <div class="form-group">
                            <label for="author">Author</label>
                            <x-form.staff-select
                                    class="form-control"
                                    name="user_id"
                                    placeholder="Anonymous"
                                    :selected="(int) $post->user_id"
                            />
                        </div>

                        <div class="form-group">
                            <label for="published_date">Published Date</label>
                            <input type="text" class="form-control" id="publishDate" name="published_at" value="{{ old('published_at', $post->published_at->format('m/d/Y')) }}">
                        </div>

                        <div class="form-group">
                            <label for="title">Status</label>
                            <select class="form-control" name="status">
                                {!! selectOptions(['published' => 'Published', 'draft' => 'Draft'], $post->status) !!}
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="title">Comments</label>
                            <select class="form-control" name="settings[comments_enabled]">
                                {!! selectOptions([
                                    1 => 'Enabled',
                                    0 => 'Disabled'
                                ], $post->setting('comments_enabled', 1)) !!}
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="title">Post Summary</label>
                            <textarea name="summary" class="form-control" rows="6">{{ old('summary', $post->summary) }}</textarea>
                        </div>

                        <div class="form-group">
                            <text-editor class="prose max-w-full" name="body" :rows="20" :fixed="false" content="{{ old('body', $post->body) }}"></text-editor>
                        </div>
                    </form>
                </div>
                <div class="panel-footer text-right">
                    <button type="submit" class="btn btn-action" @click="submitForm('postForm')">Save</button>
                </div>
            </div>
        </div>

        <div class="sidebar sidebar-1 sidebar-right">
            <div class="panel">
                <div class="panel-heading">Cover Photo</div>
                <div class="panel-body">
                    <cover-photo
                            url="/admin/posts/{{ $post->id }}/photos"
                            src="{{ $post->cover_photo }}"
                            field="cover_photo"
                            :tags="['Blog']"
                    ></cover-photo>
                </div>
            </div>

            <tags id="{{ $post->id }}" model="posts"></tags>
        </div>
    </div>
@stop
@section('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', () => {

            $('#publishDate').daterangepicker({
                singleDatePicker: true,
                showDropdowns: true,
                locale: {
                    format: 'MM/DD/YYYY'
                }
            });
        });

        function publishPost() {
            $('#postForm').append('<input type="text" name="status" value="published">').submit();
        }
    </script>

    @include('partials.check-for-unsaved-changes')
@stop
