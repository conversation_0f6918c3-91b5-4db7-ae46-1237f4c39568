@php /** @var \App\Services\SubscriptionSettingsService $subscription_settings_service */ @endphp
<div class="space-y-6 pb-12 sm:px-6 lg:col-span-9 lg:px-0">
    <form action="{{ route('admin.integrations.subscribe-save.update') }}" method="POST">
        @csrf
        @method('PUT')
        <div class="shadow sm:overflow-hidden sm:rounded-lg">
            <div class="space-y-6 bg-white py-6 px-4 sm:p-6">
                <div class="md:grid md:grid-cols-3 md:gap-6">
                    <div class="md:col-span-1">
                        <h3 class="text-lg font-medium leading-6 text-gray-900">Eligibility</h3>
                        <p class="mt-1 text-sm text-gray-500">Set which products, if any, are not eligible to be on subscriptions. These products are still available to add as one-time add-on items to their subscription order but do not carry over to the next order.</p>
                    </div>
                    <div class="mt-5 space-y-6 md:col-span-2 md:mt-0">
                        <div class="grid grid-cols-3 gap-6">
                            <div class="col-span-3">
                                <div>
                                    <div>
                                        <input type="hidden" name="settings[recurring_orders_excluded_products]" value="" />
                                        <label for="recurring_orders_excluded_products" class="block text-sm font-medium text-gray-700">Excluded products</label>
                                        <select
                                                class="form-control select2--products-multiple"
                                                name="settings[recurring_orders_excluded_products][]"
                                                tabindex="1"
                                                data-placeholder="Select product"
                                                id="recurring_orders_excluded_products"
                                                multiple="multiple"
                                        >
                                            @foreach($subscription_settings_service->excludedProducts() as $product)
                                                <option value="{{ $product->id }}" selected>{{ $product->title }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 px-4 py-3 text-right sm:px-6">
                <button type="submit" class="inline-flex justify-center rounded-md border border-transparent bg-keppel-600 py-2 px-4 text-sm font-medium text-white shadow-sm hover:bg-keppel-700 focus:outline-none focus:ring-2 focus:ring-keppel-500 focus:ring-offset-2">Save</button>
            </div>
        </div>
    </form>
</div>
