@extends('settings.layout', ['settingTitle' => 'Store'])

@php
    use App\Services\SettingsService;
    $settings_service = app(SettingsService::class);
@endphp

@section('setting_toolbar')
    <button class="btn btn-success" @click="submitForm('settingsForm')">Save</button>
@stop

@section('setting_content')
    <form action="{{ route('admin.settings.update') }}" method="POST" id="settingsForm">
        @csrf
        @method('PUT')
        <div class="panel">
            <div class="panel-body pa-0">
                <table class="table   table-striped table-settings">
                    {{--Store items--}}
                    <tr>
                        <td>
                            <h2>Products per page</h2>
                            <p>The maximum number of products that should be displayed at one time.</p>
                        </td>
                        <td>
                            <input type="number" name="settings[store_products_per_page]" class="form-control"
                                   value="{{ old('store_products_per_page', setting('store_products_per_page')) }}"/>
                        </td>
                    </tr>

                    <tr>
                        <td>
                            <h2>Weighted Items Unit Of Measurement</h2>
                            <p>Display the unit of measurement for items sold by the weight.</p>
                        </td>
                        <td>
                            <select class="form-control" name="settings[weight_uom]" id="weight-uom-select">
                                {!! selectOptions(['pounds' => 'Pounds', 'kg' => 'Kilograms'], setting('weight_uom', 'pound')) !!}
                            </select>
                        </td>
                    </tr>

                    <tr>
                        <td>
                            <h2>Weighted price display</h2>
                            <p>Set how weighted products prices are displayed to customers. For example, a weighted product priced at $2.50/lb with an average
                                weight of 1.5lb would display in one of the two following ways:</p>
                            <div class="flex justify-between">
                                <div>
                                    <p class="m-0">Price per weight</p>
                                    <p class="m-0 mt-1">$2.50 /lb <br/>Avg 1.5 lb</p>
                                </div>
                                <div>
                                    <p class="m-0">Estimated package price</p>
                                    <p class="m-0 mt-1">$3.75</p>
                                </div>

                            </div>

                        </td>
                        <td>
                            <div class="radio">
                                <label class="mr-sm">
                                    <input tabindex="1" type="radio" name="settings[show_price_per_pound]" value="1" @if(setting('show_price_per_pound', true)) checked @endif>
                                    Price per weight
                                </label>
                                <label>
                                    <input tabindex="1" type="radio" name="settings[show_price_per_pound]" value="0" @if(!setting('show_price_per_pound', true)) checked @endif>
                                    Estimated package price
                                </label>
                            </div>
                        </td>
                    </tr>

                    @if(setting('weight_uom', 'pounds') === "pounds")
                        <tr id="pounds-to-ounces">
                            <td>
                                <h2>Convert Pounds to Ounces</h2>
                                <p>When a weight is less than 1 pound then display it as ounces instead.</p>
                            </td>
                            <td>
                                <div class="radio">
                                    <label class="mr-sm">
                                        <input tabindex="1" type="radio" name="settings[convert_pounds_to_ounces]" value="1" @if(setting('convert_pounds_to_ounces', true)) checked @endif>
                                        Yes
                                    </label>
                                    <label>
                                        <input tabindex="1" type="radio" name="settings[convert_pounds_to_ounces]" value="0" @if(!setting('convert_pounds_to_ounces', true)) checked @endif>
                                        No
                                    </label>
                                </div>
                            </td>
                        </tr>
                    @endif

                    <tr>
                        <td>
                            <h2>Currency</h2>
                            <p>The currency that will be used when making credit card charges.</p>
                        </td>
                        <td>
                            <select class="form-control" name="settings[currency]">
                                {!! selectOptions(['usd' => 'USD', 'cad' => 'CAD'], setting('currency', 'usd')) !!}
                            </select>
                        </td>
                    </tr>

                    <tr>
                        <td>
                            <h2>Require address at checkout</h2>
                            <p>Set whether to capture customer addresses at checkout even if they are ordering for pickup.</p>
                        </td>
                        <td>
                            <div class="radio">
                                <label class="mr-sm">
                                    <input tabindex="1" type="radio" name="settings[require_address_at_checkout]" value="1" @if($settings_service->requiresAddressAtCheckout()) checked @endif>
                                    Yes
                                </label>
                                <label>
                                    <input tabindex="1" type="radio" name="settings[require_address_at_checkout]" value="0" @if( ! $settings_service->requiresAddressAtCheckout()) checked @endif>
                                    No
                                </label>
                            </div>
                        </td>
                    </tr>

                    <tr>
                        <td>
                            <h2>Allow PO Boxes</h2>
                            <p>Allow customer's to enter a PO Box for their address during checkout.</p>
                        </td>
                        <td>
                            <div class="radio">
                                <label class="mr-sm">
                                    <input tabindex="1" type="radio" name="settings[ship_to_pobox]" value="1" @if($settings_service->allowsPOBoxShipping()) checked @endif>
                                    Yes
                                </label>
                                <label>
                                    <input tabindex="1" type="radio" name="settings[ship_to_pobox]" value="0" @if( ! $settings_service->allowsPOBoxShipping()) checked @endif>
                                    No
                                </label>
                            </div>
                        </td>
                    </tr>

                    {{--Default Collection--}}
                    <tr>
                        <td>
                            <h2>Storefront</h2>
                            <p>The collection of products or custom page to display on the store landing page.<br/>Your store landing page is located at
                                <strong>{{ config('app.url') }}/store</strong></p>
                        </td>
                        <td>
                            <x-form.storefront-select
                                    class="form-control"
                                    name="settings[store_default_collection]"
                                    placeholder="Show all items"
                                    :selected="setting('store_default_collection')"
                            />
                        </td>
                    </tr>

                    <tr>
                        <td>
                            <h2>Featured Storefront Collection</h2>
                            <p>This only applies on the new storefront layout.</p>
                        </td>
                        <td>
                            <x-form.collection-select
                                    class="form-control"
                                    name="settings[featured_store_collection]"
                                    placeholder="None"
                                    :selected="setting('featured_store_collection')"
                                    sort="asc"
                            />


                            <label class="mt-4" for="settings[featured_store_collection_url]">URL Override</label>
                            <input type="text" name="settings[featured_store_collection_url]" class="tw-mt-2 form-control"
                                   value="{{ old('featured_store_collection_url', setting('featured_store_collection_url')) }}"/>
                            <p class="mt-1 text-gray-100">Override the "Shop All" URL instead of the collection page. Leave blank to navigate to the collection page.</p>
                        </td>
                    </tr>

                    <tr>
                        <td>
                            <h2>Storefront Sort Order</h2>
                            <p>The order products will appear when browsing the store by all products, vendors, or protocols. When browsing by a collection
                                products will be ordered by the sort order defined on the collection.</p>
                        </td>
                        <td>
                            <x-form.store-sort-select
                                    class="form-control"
                                    name="settings[store_sort_order]"
                                    :selected="setting('store_sort_order')"
                            />
                        </td>
                    </tr>

                    <tr>
                        <td>
                            <h2>Storefront Page Title</h2>
                            <p>This page title that will appear in the browser window and search results.</p>
                        </td>
                        <td>
                            <input
                                    type="text"
                                    name="settings[store_page_title]"
                                    class="form-control"
                                    value="{{ setting('store_page_title', 'Shop Now') }}"
                            >
                        </td>
                    </tr>

                    <tr>
                        <td>
                            <h2>Storefront Page Decription</h2>
                            <p>This page description that will appear in search results.</p>
                        </td>
                        <td>
                            <input
                                    type="text"
                                    name="settings[store_page_description]"
                                    class="form-control"
                                    value="{{ setting('store_page_description') }}"
                            >
                        </td>
                    </tr>

                    @if(theme('store_menu_layout') != 'style2')
                        <tr>
                            <td>
                                <h2>Show Vendors</h2>
                                <p>Show vendors list in store sidebar.</p>
                            </td>
                            <td>
                                <div class="radio">
                                    <label class="mr-sm">
                                        <input tabindex="1" type="radio" name="settings[store_show_vendors]" value="1" @if(setting('store_show_vendors')) checked @endif>
                                        Enabled
                                    </label>
                                    <label>
                                        <input tabindex="1" type="radio" name="settings[store_show_vendors]" value="0" @if(!setting('store_show_vendors')) checked @endif>
                                        Disabled
                                    </label>
                                </div>
                            </td>
                        </tr>

                        <tr>
                            <td>
                                <h2>Show Protocols</h2>
                                <p>Show protocols list in store sidebar.</p>
                            </td>
                            <td>
                                <div class="radio">
                                    <label class="mr-sm">
                                        <input tabindex="1" type="radio" name="settings[store_show_protocols]" value="1" @if(setting('store_show_protocols')) checked @endif>
                                        Enabled
                                    </label>
                                    <label>
                                        <input tabindex="1" type="radio" name="settings[store_show_protocols]" value="0" @if(!setting('store_show_protocols')) checked @endif>
                                        Disabled
                                    </label>
                                </div>
                            </td>
                        </tr>
                    @endif
                    <tr>
                        <td>
                            <h2>Order Deadline Cut-Off Time</h2>
                            <p>The hour of the day that order deadlines should be cut-off.</p>
                        </td>
                        <td>
                            <x-form.deadline-hour-select
                                    class="form-control"
                                    name="settings[order_deadline_hour]"
                                    :selected="$settings_service->deadlineHour()"
                            />
                        </td>
                    </tr>

                    <tr>
                        <td>
                            <h2>Only Show Closest Shipping Zone</h2>
                            <p>Only show the closest shipping zone during registration or when changing delivery preference.</p>
                        </td>
                        <td>
                            <div class="radio">
                                <label class="mr-sm">
                                    <input tabindex="1" type="radio" name="settings[show_closest_zone]" value="1" @if(setting('show_closest_zone', true)) checked @endif>
                                    Show Closest
                                </label>
                                <label>
                                    <input tabindex="1" type="radio" name="settings[show_closest_zone]" value="0" @if(!setting('show_closest_zone', true)) checked @endif>
                                    Show All
                                </label>
                            </div>
                        </td>
                    </tr>

                    <tr>
                        <td>
                            <h2>Checkout flow</h2>
                            <p>Use the standard single-page checkout flow or the legacy multi-page checkout flow</p>
                        </td>
                        <td>
                            <div class="radio">
                                <label class="mr-sm">
                                    <input tabindex="1" type="radio" name="settings[one_page_checkout]" value="1" @if(setting('one_page_checkout', false)) checked @endif>
                                    Standard
                                </label>
                                <label>
                                    <input tabindex="1" type="radio" name="settings[one_page_checkout]" value="0" @if(!setting('one_page_checkout', false)) checked @endif>
                                    Multi-page (Legacy)
                                </label>
                            </div>
                        </td>
                    </tr>

                    <tr>
                        <td>
                            <h2>Cart Service</h2>
                            <p>Select the cart service provider</p>
                        </td>
                        <td>
                            <div class="radio">
                                <label class="mr-sm">
                                    <input tabindex="1" type="radio" name="settings[cart_service]" value="order" @if($settings_service->cartService() !== 'database') checked @endif>
                                    Unconfirmed orders (Legacy)
                                </label>
                                <label>
                                    <input tabindex="1" type="radio" name="settings[cart_service]" value="database" @if($settings_service->cartService() === 'database') checked @endif>
                                    Standard
                                </label>
                            </div>
                        </td>
                    </tr>

                </table>
            </div>
            <div class="panel-footer text-right">
                <button class="btn btn-action" @click="submitForm('settingsForm', $event)">Save</button>
            </div>
        </div>
    </form>
    <div class="panel panel-body">
        Control the look of your store from <strong>My Site / Theme / Store</strong>
    </div>
@stop

@section('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            $('#weight-uom-select').on('change', function() {
                if ($(this).val() == 'kg') {
                    $('#pounds-to-ounces').find('input').each(function() {
                        if ($(this).val() == 0) {
                            $(this).prop('checked', true);
                        }
                    });
                    $('#pounds-to-ounces').hide();
                }
            });
        });
    </script>
@stop

