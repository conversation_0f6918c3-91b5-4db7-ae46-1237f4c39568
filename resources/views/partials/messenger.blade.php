<div class="gc-modal gc-modal-mask" id="messengerModal" @click="hideModal('messengerModal')">
    <div class="gc-modal-wrapper">
        <div class="gc-modal-container" @click.stop>
            <form method="POST" action="/admin/notify" id="sendMessageForm">
                @csrf
                <div class="gc-modal-header">
                    Send Message
                </div>

                <div class="gc-modal-body">
                    <div class="form-group">
                        <label>Recipients</label>
                        <x-form.delivery-method-select
                                class="form-control select2"
                                name="pickup_id[]"
                                tabindex="1"
                                data-placeholder="Select locations"
                                multiple
                                style="width: 100%"
                                :selected="request('pickup_id')"
                        />
                    </div>
                    <div class="form-group">
                        <label>Subject</label>
                        <input type="text" class="form-control" name="subject" value="{{ old('subject') }}">
                    </div>
                    <div class="form-group">
                        <label>Message</label>
                        <textarea class="form-control" rows="10" name="message">{{ old('message') }}</textarea>
                    </div>
                </div>

                <div class="gc-modal-footer">
                    <button type="button" class="btn btn-alt" @click="hideModal('messengerModal')">Cancel</button>
                    <button type="submit" class="btn btn-action"><i class="fas fa-envelope"></i> Send</button>
                </div>
            </form>
        </div>
    </div>
</div>
