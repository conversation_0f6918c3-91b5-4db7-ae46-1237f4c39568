@extends('layouts.main', ['pageTitle' => $vendor->title])

@section('toolbar-breadcrumb')
    <li><a href="{{ route('admin.vendors.index') }}">Vendors</a></li>
    <li>{{ $vendor->title }}</li>
@stop
@section('toolbar-buttons')
    <button class="btn btn-success flex-item mr-sm" @click="submitForm('updateResourceForm')">Save</button>

    <div class="dropdown flex-item">
        <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
            <i class="fas fa-gear"></i> Actions <i class="fas fa-caret-down"></i>
        </button>
        <ul class="dropdown-menu pull-right">
            <li><a href="#" @click="showModal('hideVendorModal')">Product Visibility</a></li>
            <li><a href="#" @click="showModal('deleteVendorModal')">Delete</a></li>
        </ul>
    </div>
@stop

@section('content')
    @include('vendors.partials.hide-vendor-modal')
    @include('vendors.partials.delete-vendor-modal')
    <div class="row">
        <div class="content">
            <div class="panel">
                <div class="panel-body">
                    <form action="{{ route('admin.vendors.update', $vendor->id) }}" method="POST" id="updateResourceForm">
                        @csrf
                        @method('put')

                        {{--Vendor Title--}}
                        <div class="form-group">
                            <label for="title">Vendor Name:</label>
                            <input type="text" name="title" value="{{ $vendor->title }}" class="form-control">
                        </div>

                        {{--Vendor Phone--}}
                        <div class="form-group">
                            <label for="phone">Contact Phone:</label>
                            <input type="text" name="phone" value="{{ $vendor->phone }}" class="form-control">
                        </div>

                        {{--Vendor Email--}}
                        <div class="form-group">
                            <label for="email">Contact Email:</label>
                            <input type="text" name="email" value="{{ $vendor->email }}" class="form-control">
                        </div>

                        {{--Vendor Website URL--}}
                        <div class="form-group">
                            <label for="website">Website URL:</label>
                            <input type="text" name="website" value="{{ $vendor->website }}" class="form-control">
                        </div>

                        
                        <div class="form-group">
                            <label for="title">Description</label>
                            <text-editor class="prose max-w-full" name="description" content="{{ $vendor->description }}"></text-editor>
                        </div>
                    </form>
                </div>
                <div class="panel-footer text-right">
                    <button class="btn btn-action" @click="submitForm('updateResourceForm')">Save</button>
                </div>
            </div>
        </div>

        <div class="sidebar sidebar-1 sidebar-right"> 
            <div class="panel ">
                <div class="panel-body">
                    <cover-photo
                            url="/admin/vendors/{{ $vendor->id }}/photo"
                            src="{{ $vendor->cover_photo }}"
                            field="cover_photo"
                    ></cover-photo>
                </div>
            </div>
        </div>
    </div>
@stop

