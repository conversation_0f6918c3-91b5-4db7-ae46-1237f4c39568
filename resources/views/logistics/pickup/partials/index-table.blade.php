@include('partials.saved-filters')
<div class="panel">
    <div class="panel-heading">
        <form action="/admin/logistics/pickups" method="GET" class="hidden-print">
            <input type="hidden" name="field" value="{{ request()->get('field', 'inventory') }}">
            <div class="flex align-items-m">
                <button
                        type="button"
                        class="btn btn-white flex-item br-right-0 br--l"
                        @click.stop="showPanel('filterPanel')"
                        tabindex="1"
                >Filter
                    <span class="hide-mobile">Locations</span>
                    <i class="fas fa-caret-down"></i></button>
                <div class="flex-item-fill">
                    <button type="submit" class="btn btn-clear btn-input-overlay"><i class="fas fa-search"></i></button>
                    <input
                            tabindex="1"
                            type="text"
                            class="form-control input-overlay-left br--r"
                            placeholder="Search locations by name..."
                            name="locations"
                            value="{{ Request::get('locations') }}"
                    >
                </div>
            </div>
            @include('logistics.pickup.partials.filter-form')
        </form>
        @include('partials.applied-filters', ['filter_resource' => 'pickup'])
    </div>
    <div class="panel-body pa-0">
        <div class="table-responsive">
            <table x-data class="table table-striped table-full table-list">
                <thead>
                <tr>
                    <th>{!! sortTable('Name', 'title') !!}</th>
                    <th>{!! sortTable('Display Name', 'display_name') !!}</th>
                    <th>{!! sortTable('City', 'city') !!}</th>
                    <th>{!! sortTable('State', 'state') !!}</th>
                    <th>{!! sortTable('Status', 'status_id') !!}</th>
                    <th></th>
                </tr>
                </thead>
                <tbody>
                @foreach($pickups as $pickup)
                    <tr>
                        <td>
                            <a href="{{ route('admin.pickups.edit', [$pickup->id]) }}">{{ $pickup->title }}</a>
                            &nbsp;<span class="label label-light _{!! (int) !$pickup->visible !!}">Hidden</span>
                            @if($pickup->id == auth()->user()->pickup_point)
                                &nbsp;
                                <span class="label label-light">My Default Location</span>
                            @endif
                        </td>
                        <td>{{ $pickup->present()->title() }}</td>
                        <td data-label="City">{{ $pickup->city }}</td>
                        <td data-label="State">{{ $pickup->state }}</td>
                        <td data-label="Status">{{ $pickup->status() }}</td>
                        <td>
                            <button type="button" x-on:click="$dispatch('open-modal-delete-delivery-method-confirmation', { delivery_method_id: {{ $pickup->id }} })">
                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0" />
                                </svg>
                            </button>
                        </td>
                    </tr>
                @endforeach
                @if(!$pickups->count())
                    <tr>
                        <td colspan="100%">No pickup points found.</td>
                    </tr>
                @endif
                </tbody>
            </table>
        </div>
    </div>
</div>
