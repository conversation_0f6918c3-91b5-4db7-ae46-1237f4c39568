@extends('print_templates.layout')

@section('content')
@foreach($orders as $order)

@php /** @var \App\Models\Order $order */ @endphp

<!-- Start Page -->
<div class="pageContainer">
    <div class="pageInnerContainer">
        <!-- Start Details Container -->
        <div class="detailsContainer">

            <div class="detailsInnerContainer">
                <div class="pageHeading">
                    Order #{{ $order->id }}
                </div>
                <div class="customerName">{{ $order->customer_last_name }}, {{ $order->customer_first_name }}</div>

                <ul class="customerDetailsList">
                    {!! $order->first_time_order ? '<li>&#9733; New Customer</li>' : '' !!}
                    {!! $order->flagged ? '<li>&#9873; Flagged</li>' : '' !!}
                    <li>{{ $order->customer_phone }}</li>
                    <li>{{ $order->customer_email }}</li>
                    <li>
                        <div>{{ $order->shipping_street }} {{ $order->shipping_street_2 }}</div>
                        <div>{{ $order->shipping_city }}, {{ $order->shipping_state }} {{ $order->shipping_zip }}</div>
                    </li>

                </ul>

                <div class="fulfillmentLocation">
                    {{ $order->present()->pickupTitle() }}
                    <div class="scheduleTitle">{{ $order->present()->scheduleTitle() }}</div>
                </div>
                <ul class="customerDetailsList">
                    <li>{{ $order->present()->pickupDate() }}</li>
                </ul>

                <div class="parcelsContainer">
                    <div style="margin-bottom: 1rem;">
                        Weight: <strong>{{ $order->weight }}</strong> {{ __("messages.uom." . setting('weight_uom', 'pounds')) }}.
                    </div>
                    <div class="parcels">
                        Parcels: {{ $order->present()->totalContainers() ? $order->present()->totalContainers() : '__________'}}
                    </div>
                    <div class="packedBy">
                        Packed by:
                    </div>
                </div>

                <!-- Start Order Notes -->
                @if($order->customer_notes)
                    <div class="customerNotes">
                        <div>Customer Notes:</div>
                        {{ $order->customer_notes }}
                    </div>
                @endif
                @if($order->packing_notes)
                    <div class="packingNotes">
                        <div>Packing Notes:</div>
                        {{ $order->packing_notes }}
                    </div>
                @endif
                <!-- End Order Notes -->
            </div>
        </div>
        <!-- End Details Container -->


        <!-- Start Order Container -->
        <div class="orderContainer">
            <div class="orderItemsContainer">

                <!-- Start Order Items -->
                @foreach($order->items->groupBy('inventory_type') as $key => $itemGroup)
                    <table class="orderItemsTable {{ $itemGroup->count() >= 15 ? 'fullPage' : 'sharePage' }}">
                        <thead class="packingGroupHeader">
                            <tr><th colspan="100%">{{ packingGroup($key) }}</th></tr>
                        </thead>
                        <tbody>
                            @foreach($itemGroup as $item)
                                @include('print_templates.packing.sevensonsfarms._items')
                            @endforeach
                        </tbody>
                    </table>
                @endforeach
                <!-- End Order Items -->
            </div>
        </div>
        <!-- End Order Container -->
    </div>
    <div class="orderFooter">
        <div class="orderFooter_divider"><hr></div>
        <div class="orderFooter_message">END OF ORDER #{{ $order->id }}</div>
        <div class="orderFooter_divider"><hr></div>
    </div>
</div>
@endforeach
@stop
