<form action="{{ route('admin.users.update', $user->id) }}" method="POST" id="updateResourceForm">
    @csrf
    @method('put')
    <input type="hidden" name="tab" value="{{ request('tab') }}">
    <div class="panel panel-tabs">
        <div class="panel-body pa-0">
            <table class="table table-settings">
                <tbody>
                {{--User Can Back-order--}}
                <tr>
                    <td>
                        <h2>Back Ordering</h2>
                        <p>Allow this user to back order products.</p>
                    </td>
                    <td>
                        <div class="radio">
                            <label class="mr-sm">
                                <input tabindex="1" type="radio" name="can_backorder" value="1" @if($user->can_backorder) checked @endif> Yes
                            </label>
                            <label>
                                <input tabindex="1" type="radio" name="can_backorder" value="0" @if(!$user->can_backorder) checked @endif> No
                            </label>
                        </div>
                    </td>
                </tr>

                {{--Exempt From Fees--}}
                <tr>
                    <td>
                        <h2>Exempt From Fees</h2>
                        <p>Exempt this user form incurring any delivery or additional fees when placing an order.
                            <br/>This is a great way to reward your location leaders or loyal customers.</p>
                    </td>
                    <td>
                        <div class="radio">
                            <label class="mr-sm">
                                <input tabindex="1" type="radio" name="exempt_from_fees" value="1" @if($user->exempt_from_fees) checked @endif> Exempt
                            </label>
                            <label>
                                <input tabindex="1" type="radio" name="exempt_from_fees" value="0" @if(!$user->exempt_from_fees) checked @endif> Not Exempt
                            </label>
                        </div>
                    </td>
                </tr>

                <tr>
                    <td>
                        <h2>Exempt From Tax</h2>
                        <p>Exempt this user form incurring any tax.</p>
                    </td>
                    <td>
                        <div class="radio">
                            <label class="mr-sm">
                                <input tabindex="1" type="radio" name="settings[exempt_from_tax]" value="1" @if($user->setting('exempt_from_tax')) checked @endif>
                                Exempt
                            </label>
                            <label>
                                <input tabindex="1" type="radio" name="settings[exempt_from_tax]" value="0" @if(!$user->setting('exempt_from_tax')) checked @endif>
                                Not Exempt
                            </label>
                        </div>
                    </td>
                </tr>

                <tr>
                    <td>
                        <h2>Pricing Group</h2>
                        <p>Determines which pricing this customer will see when shopping the store.</p>
                    </td>
                    <td>
                        <x-form.pricing-group-select
                                class="form-control"
                                name="pricing_group_id"
                                placeholder="Retail"
                                :selected="(int) $user->pricing_group_id"
                        />
                    </td>
                </tr>

                {{--User Default Location--}}
                <tr>
                    <td>
                        <h2>Default Delivery Preference</h2>
                    </td>
                    <td>
                        <x-form.delivery-method-by-state-select
                                class="form-control"
                                name="pickup_point"
                                :selected="(int) $user->pickup_point"
                        />
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
        <div class="panel-footer text-right">
            <button class="btn btn-action" @click="submitForm('updateResourceForm', $event)">Save</button>
        </div>
    </div>

    <div class="panel panel-tabs">
        <div class="panel-body pa-0">
            <table class="table table-striped table-settings">
                <tbody>
                <tr>
                    <td>
                        <h2>Marketing Notifications</h2>
                        <p>If enabled, allows this user to recieve marketing notifications. These would include order deadline reminders or messages sent from
                            the bulk messenger.</p>
                    </td>
                    <td>
                        @if($user->order_deadline_email_reminder)
                            <button type="submit" class="btn btn-danger btn-sm" name="order_deadline_email_reminder" value="0">Do not send notifications
                            </button>
                        @else
                            <p>This user has opted out of receiving marketing notifications. They can opt back in from their account setting if they wish.</p>
                        @endif
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
    </div>

    @if(setting('enable_referrals'))
        <div class="panel panel-tabs">
            <div class="panel-body">
                <table class="table table-striped table-settings">
                    <tbody>
                    <tr>
                        <td>
                            <h2>Referral Bonus</h2>
                            <p>This is how much store credit this user will receive when they refer a new customer. This will be applied in addition to the
                                global Referral Bonus setting.</p>
                        </td>
                        <td>
                            <div class="input-group">
                                <span class="input-group-addon">${{ money(setting('referral_bonus', 0)) }}&nbsp; +</span>
                                <input type="text" name="referral_bonus" value="{{ old('referral_bonus', money($user->referral_bonus)) }}" class="form-control"/>
                                <span class="input-group-addon">= &nbsp;<strong>&#36;{{ $user->present()->referralBonus() }}</strong></span>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <h2>Referral Registration Incentive</h2>
                            <p>The amount of store credit a new sign-up will receive when using this user's referral link. This will be applied in addition to
                                the global Registration Incentive setting.</p>
                        </td>
                        <td>
                            <div class="input-group">
                                <span class="input-group-addon">
                                    ${{ number_format( setting('user_registration_credit', 0) + money(setting('referral_payout')), 2,'.',',') }}&nbsp; +
                                </span>
                                <input type="text" name="referral_payout" value="{{ old('referral_payout', money($user->referral_payout)) }}" class="form-control"/>
                                <span class="input-group-addon"> = &nbsp;<strong>&#36;{{ $user->present()->referralPayout() }}</strong></span>
                            </div>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
            <div class="panel-footer text-right">
                <button class="btn btn-action" @click="submitForm('updateResourceForm', $event)">Save</button>
            </div>
        </div>
    @endif
</form>
