@php
    /** @var \App\Models\RecurringOrder|null $recurringOrder */
@endphp

@if ( ! is_null($recurringOrder))
    <div class="bg-white shadow overflow-hidden sm:rounded-lg">
        <div class="px-4 py-5 sm:px-6">
            <p class="m-0 max-w-2xl text-sm text-gray-500">This customer has an active subscription. It can be <a href="{{ route('admin.subscriptions.edit', ['subscription' => $recurringOrder]) }}">managed here</a>.</p>
        </div>
    </div>
@else
    <div class="bg-white shadow overflow-hidden sm:rounded-lg">
        <div class="px-4 py-5 sm:px-6">
            <p class="m-0 max-w-2xl text-sm text-gray-500">This customer does not have an active subscription.</p>
        </div>
    </div>
@endif

