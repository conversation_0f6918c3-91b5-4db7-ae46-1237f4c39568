<div class="gc-modal gc-modal-mask" id="createCustomerModal" @click="hideModal('createCustomerModal')">
    <div class="gc-modal-wrapper">
        <div class="gc-modal-container" @click.stop>
            <form action="/admin/users" method="POST" id="storeCustomerForm">
                @csrf
                <div class="gc-modal-header">
                    Create a Customer
                </div>

                <div class="gc-modal-body">
                    {{--First_name--}}
                    <div class="form-group">
                        <label for="first_name">First Name</label>
                        <input type="text" name="first_name" class="form-control" value="{{ old('first_name') }}" />
                    </div>

                    {{--Last_name--}}
                    <div class="form-group">
                        <label for="last_name">Last Name</label>
                        <input type="text" name="last_name" class="form-control" value="{{ old('last_name') }}" />
                    </div>

                    {{--Company_name--}}
                    <div class="form-group">
                        <label for="company_name">Company Name</label>
                        <input type="text" name="company_name" class="form-control" value="{{ old('company_name') }}" />
                    </div>

                    {{--Email--}}
                    <div class="form-group">
                        <label for="email">Email</label>
                        <input type="text" name="email" class="form-control" value="{{ old('email') }}" />
                    </div>

                    {{--Password--}}
                    <div class="form-group">
                        <label for="password">Password</label>
                        <input type="password" name="password" class="form-control" value="{{ old('password') }}" />
                    </div>

                    {{--Pickup Point--}}
                    <div class="form-group">
                        <label for="pickup_point">Default Delivery Preference</label>
                        <x-form.delivery-method-by-state-select
                                class="form-control"
                                name="pickup_point"
                                placeholder="Select a default pickup location"
                                :selected="(int) old('pickup_point')"
                        />
                    </div>
                </div>

                <div class="gc-modal-footer">
                    <button type="button" class="btn btn-alt" @click="hideModal('createCustomerModal')">Cancel</button>
                    <button type="submit" class="btn btn-action" @click.prevent="submitForm('storeCustomerForm')">Create Customer</button>
                </div>
            </form>
        </div>
    </div>
</div>
