@php
    /** @var App\Models\Pickup|null $delivery_method */
@endphp
<x-modal>
    <form
            wire:submit="submit"
            class="relative transform overflow-hidden rounded-lg bg-white text-left shadow-xl sm:mx-auto sm:my-8 sm:w-full sm:max-w-lg"
    >
        <div class="bg-white px-4 pb-4 pt-5 sm:p-6 sm:pb-4">
            <div class="sm:flex sm:items-start">
                <div class="mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                    <svg class="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z" />
                    </svg>
                </div>
                <div class="mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left">
                    <h3 class="text-base font-semibold leading-6 text-gray-900" id="modal-title">Delete delivery method</h3>
                    <div class="mt-2">
                        <p class="text-sm text-gray-500">Are you sure you want to delete this delivery method? All of its data will be permanently removed. This
                            action cannot be undone. Please type the delivery method name <strong><em>{{ $delivery_method?->title }}</em></strong> to confirm
                            delete.
                        </p>
                    </div>
                    <div class="mt-4">
                        <label for="name" class="hidden">Page name</label>
                        <div class="relative mt-2 rounded-md shadow-sm">
                            <input type="text" wire:model="name" name="name" id="name" class="block w-full rounded-md border-0 py-1.5  ring-1 ring-inset pr-10  focus:ring-2 focus:ring-inset sm:text-sm sm:leading-6 @error('name') text-red-900 ring-red-300 placeholder:text-red-300 focus:ring-red-500 @else text-gray-900 ring-gray-300 placeholder:text-gray-400 focus:ring-keppel-600 @enderror" placeholder="{{ $delivery_method?->title }}" required @error('name') aria-invalid="true" @enderror aria-describedby="name-error">
                            @error('name')
                            <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                                <svg class="h-5 w-5 text-red-500" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            @enderror
                        </div>
                        @error('name') <p class="mt-2 text-sm text-red-600" id="name-error">{{ $message }}</p> @enderror
                    </div>
                </div>
            </div>
        </div>
        <div class="bg-gray-50 px-4 py-3 sm:flex sm:flex-row-reverse sm:px-6">
            <button type="submit" wire:loading.attr="disabled" class="inline-flex w-full justify-center rounded-md bg-red-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-red-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-keppel-600 sm:ml-3 sm:w-auto">
                <span wire:loading.remove wire:target="submit">Delete</span>
                <svg wire:loading.inline wire:target="submit" class="animate-spin h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
            </button>
            <button type="button" wire:click="close" class="mt-3 inline-flex w-full justify-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 sm:mt-0 sm:w-auto">
                Cancel
            </button>
        </div>
    </form>
</x-modal>
