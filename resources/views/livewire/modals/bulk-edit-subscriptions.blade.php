<x-modal>
    <form
            wire:submit.prevent.stop="submit"
            x-data="{ action: @entangle('action').live }"
            class="relative transform rounded-lg bg-white text-left shadow-xl sm:mx-auto sm:my-8 sm:w-full sm:max-w-xl"
    >
        <div class="rounded-t-lg bg-white px-4 pb-4 pt-5 sm:p-6 sm:pb-4">
            <div class="sm:flex sm:items-start">

                <div class="mt-3 w-full text-center sm:mt-0 sm:text-left">
                    <h3 class="text-lg font-bold leading-6 text-gray-900" id="modal-title">Bulk Edit Subscriptions</h3>
                    <div class="mt-2">

                        <div class="border-b border-gray-200">
                            <div class="-mb-px flex space-x-8">
                                <button href="#" type="button" x-on:click="action = 'date'" :class="{'border-keppel-500 text-keppel-600': action === 'date', 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700': action !== 'date' }" class="whitespace-nowrap border-b-2 border-transparent px-1 py-3 text-sm font-medium text-gray-500 hover:border-gray-300 hover:text-gray-700">Delivery Date</button>
                                <button href="#" type="button" x-on:click="action = 'product'" :class="{'border-keppel-500 text-keppel-600': action === 'product', 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700': action !== 'product' }" class="whitespace-nowrap border-b-2 border-keppel-500 px-1 py-3 text-sm font-medium text-keppel-600">Product</button>
                            </div>
                        </div>


                        <div x-show="action === 'date'" class="mt-2 text-left">
                            <p class="m-0 text-sm text-gray-500">Update the delivery date of {{ $subscription_count }} {{ str('subscription')->plural($subscription_count) }}.</p>
                            <label for="delivery_date" class="mt-6 font-medium text-gray-900">New Delivery Date</label>
                            <div class="mt-2 relative w-full max-w-xs">
                                <input type="date" id="delivery_date" wire:model.live="delivery_date" class="block w-full rounded-md border border-gray-300 focus:outline-none focus:border-keppel-500 focus:ring-keppel-500 sm:text-sm @error('delivery_date') border-red-300 pr-10 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500 @enderror" aria-invalid="true" aria-describedby="delivery_date-error">

                                @error('delivery_date')
                                <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                                    <!-- Heroicon name: mini/exclamation-circle -->
                                    <svg class="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"/>
                                    </svg>
                                </div>
                                @enderror
                            </div>
                            @error('delivery_date')

                            <p class="m-0 mt-2 text-sm text-red-600" id="delivery_date-error">{{ $message }}</p>
                            @enderror

                            @if(today()->diffInDays(\Carbon\Carbon::parse($delivery_date)) <= 5)
                                <div class="mt-3 flex gap-3">
                                    <div class="flex h-6 shrink-0 items-center">
                                        <div class="group grid size-4 grid-cols-1">
                                            <input id="expedited_date_confirmation" wire:model="expedited_date_confirmation" aria-describedby="expedited_date_confirmation-description" name="expedited_date_confirmation" type="checkbox" class="!m-0 col-start-1 row-start-1 appearance-none rounded border border-gray-300 bg-white checked:border-keppel-600 checked:bg-keppel-600 indeterminate:border-keppel-600 indeterminate:bg-keppel-600 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-keppel-600 disabled:border-gray-300 disabled:bg-gray-100 disabled:checked:bg-gray-100 forced-colors:appearance-auto">
                                        </div>
                                    </div>
                                    <div class="text-sm/6">
                                        <label for="expedited_date_confirmation" class="font-medium text-gray-900">Expedite</label>
                                        <p id="expedited_date_confirmation-description" class="m-0 text-gray-500">I confirm the delivery date is within 5 days from today.</p>
                                    </div>
                                </div>
                                @error('expedited_date_confirmation')

                                <p class="m-0 mt-2 text-sm text-red-600" id="expedited_date_confirmation-error">{{ $message }}</p>
                                @enderror
                            @endif
                        </div>

                        <div x-show="action === 'product'" class="mt-4 text-left">
                            <div x-data="{ product_action: @entangle('product_action').live }">
                                <p class="m-0 text-sm text-gray-500">Remove or replace a product on {{ $subscription_count }} {{ str('subscription')->plural($subscription_count) }}.</p>

                                <label for="removed_product_id" class="mt-4 font-medium text-xs text-gray-900">Product</label>
                                <div x-on:product-selected="event => $wire.removed_product_id = event.detail.product_id" class="relative mt-1 rounded-md shadow-sm">
                                    <livewire:admin.product-select
                                            :product_types="[
                                            App\Support\Enums\ProductType::STANDARD,
                                            App\Support\Enums\ProductType::BUNDLE
                                        ]"
                                    />
                                    @error('removed_product_id')
                                    <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                                        <svg class="h-5 w-5 text-red-500" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"/>
                                        </svg>
                                    </div>
                                    @enderror
                                </div>
                                @error('removed_product_id')
                                <p class="mt-2 text-sm text-red-600" id="removed-product-id-error">{{ $message }}</p>
                                @enderror
                                <fieldset class="p-0 m-0 mt-4">
                                    <legend class="sr-only">Option</legend>
                                    <div class="space-y-2">
                                        <div class="relative flex items-start">
                                            <div class="flex h-6 items-center">
                                                <input id="remove" x-model="product_action" value="remove" aria-describedby="remove-description" name="product_action" type="radio" class="h-4 w-4 border-gray-300 text-keppel-600 focus:ring-keppel-600">
                                            </div>
                                            <div class="ml-3 text-sm leading-6">
                                                <label for="remove" class="font-medium text-gray-900">Remove Only</label>
                                            </div>
                                        </div>
                                        <div class="relative flex items-start">
                                            <div class="flex h-6 items-center">
                                                <input id="replace" x-model="product_action" value="replace" aria-describedby="replace-description" name="product_action" type="radio" class="h-4 w-4 border-gray-300 text-keppel-600 focus:ring-keppel-600">
                                            </div>
                                            <div class="ml-3 w-full text-sm leading-6">
                                                <label for="replace" class="font-medium text-gray-900">Replace</label>
                                                <div class="mt-2" :class="{'opacity-50 cursor-not-allowed pointer-events-none': product_action !== 'replace'}">
                                                    <label for="replacement_product_id" class="font-medium text-xs text-gray-900">Replacement product</label>
                                                    <div x-on:product-selected="event => $wire.replacement_product_id = event.detail.product_id" class="relative mt-1 rounded-md shadow-sm">
                                                        <livewire:admin.product-select
                                                                :product_types="[
                                                                    App\Support\Enums\ProductType::STANDARD,
                                                                    App\Support\Enums\ProductType::BUNDLE
                                                                ]"

                                                        />
                                                        @if($product_action === 'replace')
                                                            @error('replacement_product_id')
                                                            <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                                                                <svg class="h-5 w-5 text-red-500" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"/>
                                                                </svg>
                                                            </div>
                                                            @enderror
                                                        @endif
                                                    </div>
                                                    @if($product_action === 'replace')
                                                        @error('replacement_product_id')
                                                        <p class="mt-2 text-sm text-red-600" id="replacement-product-id-error">{{ $message }}</p>
                                                        @enderror
                                                    @endif
                                                </div>
                                            </div>

                                        </div>
                                    </div>
                                </fieldset>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
        <div class="rounded-b-lg bg-gray-50 px-4 py-3 sm:flex sm:flex-row-reverse sm:px-6">
            <button type="submit" wire:loading.attr="disabled" class="inline-flex w-full justify-center rounded-md bg-keppel-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-keppel-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-keppel-600 sm:ml-3 sm:w-auto">
                Save
            </button>
            <button type="button" wire:loading.attr="disabled" wire:click="close" class="mt-3 inline-flex w-full justify-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 sm:mt-0 sm:w-auto">
                Cancel
            </button>
        </div>
    </form>
</x-modal>
