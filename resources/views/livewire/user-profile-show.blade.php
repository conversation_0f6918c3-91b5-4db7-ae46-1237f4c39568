@php
    /** @var App\Models\User $user */
@endphp

<div>
    <div x-data="{ editing: $wire.entangle('editing') }">
        <div class="bg-white shadow rounded-md px-4 py-5 sm:px-6">
            <div>
                <h1 class="text-base font-semibold leading-6 text-gray-900">
                    Profile details
                </h1>

                <p class="m-0 mt-2 text-sm text-gray-700">
                    Manage the information shown on this
                    <a href="{{ route('blog.authors.show', [$slug]) }}" target="_blank" class="text-keppel-600 hover:text-keppel-500">author's
                        blog page</a>.
                </p>
                <div class="mt-6 ">
                    <dl class="divide-y divide-gray-100">

                        <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                            <dt class="text-sm font-medium leading-6 text-gray-900">Slug</dt>
                            <dd x-show="editing !== 'slug'"
                                class="mt-1 flex text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                                <span class="flex-grow">{{ $user->profile?->slug ?? $user->authorSlug() }}</span>
                                <span class="ml-4 flex-shrink-0">
                                    <button type="button" x-on:click="editing = 'slug'"
                                            class="rounded-md bg-white font-medium text-keppel-600 hover:text-keppel-500">
                                        Edit
                                    </button>
                                </span>
                            </dd>
                            <dd x-show="editing === 'slug'" x-cloak
                                class="mt-1 flex text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                                <span class="flex-grow">
                                    <label for="slug" class="sr-only">Slug</label>
                                    <div class="relative mt-1 rounded-md shadow-sm">
                                        <input type="text" wire:model="slug" name="slug"
                                               id="slug"
                                               class="block w-full rounded-md border border-gray-300 focus:outline-none focus:border-keppel-500 focus:ring-keppel-500 sm:text-sm @error('slug') border-red-300 pr-10 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500 @enderror"
                                               aria-invalid="true" aria-describedby="slug-error">
                                        @error('slug')
                                        <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                                            <!-- Heroicon name: mini/exclamation-circle -->
                                            <svg class="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg"
                                                 viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                                <path fill-rule="evenodd"
                                                      d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z"
                                                      clip-rule="evenodd" />
                                            </svg>
                                        </div>
                                        @enderror
                                    </div>
                                    @error('slug')
                                    <p class="m-0 mt-2 text-sm text-red-600" id="slug-error">{{ $message }}</p>
                                    @enderror
                                </span>
                                <span class="ml-4 flex-shrink-0">
                                    <button type="button" wire:click="saveSlug" wire:loading.attr="disabled"
                                            wire:target="saveSlug"
                                            class="rounded-md bg-white font-medium text-keppel-600 hover:text-keppel-500">
                                        <span wire:loading.remove wire:target="saveSlug">Save</span>
                                        <svg wire:loading.inline wire:target="saveSlug"
                                             class="animate-spin h-4 w-4"
                                             xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor"
                                                    stroke-width="4"></circle>
                                            <path class="opacity-75" fill="currentColor"
                                                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                        </svg>
                                    </button>
                                </span>
                            </dd>
                        </div>

                        <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                            <dt class="text-sm font-medium leading-6 text-gray-900">Position Title</dt>
                            <dd x-show="editing !== 'position_title'"
                                class="mt-1 flex text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                                <span class="flex-grow">{{ $user->profile?->position_title ?? 'N/A' }}</span>
                                <span class="ml-4 flex-shrink-0">
                                    <button type="button" x-on:click="editing = 'position_title'"
                                            class="rounded-md bg-white font-medium text-keppel-600 hover:text-keppel-500">
                                        Edit
                                    </button>
                                </span>
                            </dd>
                            <dd x-show="editing === 'position_title'" x-cloak
                                class="mt-1 flex text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                                <span class="flex-grow">
                                    <label for="position_title" class="sr-only">Position Title</label>
                                    <div class="relative mt-1 rounded-md shadow-sm">
                                        <input type="text" wire:model="position_title" name="position_title"
                                               id="position_title"
                                               class="block w-full rounded-md border border-gray-300 focus:outline-none focus:border-keppel-500 focus:ring-keppel-500 sm:text-sm @error('position_title') border-red-300 pr-10 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500 @enderror"
                                               aria-invalid="true" aria-describedby="position_title-error">
                                        @error('position_title')
                                        <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                                            <!-- Heroicon name: mini/exclamation-circle -->
                                            <svg class="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg"
                                                 viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                                <path fill-rule="evenodd"
                                                      d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z"
                                                      clip-rule="evenodd" />
                                            </svg>
                                        </div>
                                        @enderror
                                    </div>
                                    @error('position_title')
                                    <p class="m-0 mt-2 text-sm text-red-600" id="position_title-error">{{ $message }}</p>
                                    @enderror
                                </span>
                                <span class="ml-4 flex-shrink-0">
                                    <button type="button" wire:click="savePositionTitle" wire:loading.attr="disabled"
                                            wire:target="savePositionTitle"
                                            class="rounded-md bg-white font-medium text-keppel-600 hover:text-keppel-500">
                                        <span wire:loading.remove wire:target="savePositionTitle">Save</span>
                                        <svg wire:loading.inline wire:target="savePositionTitle"
                                             class="animate-spin h-4 w-4"
                                             xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor"
                                                    stroke-width="4"></circle>
                                            <path class="opacity-75" fill="currentColor"
                                                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                        </svg>
                                    </button>
                                </span>
                            </dd>
                        </div>

                        <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                            <dt class="text-sm font-medium leading-6 text-gray-900">Bio</dt>
                            <dd x-show="editing !== 'bio'"
                                class="mt-1 flex text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                                <span class="flex-grow">{{ $user->profile?->bio ?? 'N/A' }}</span>
                                <span class="ml-4 flex-shrink-0">
                                    <button type="button" x-on:click="editing = 'bio'"
                                            class="rounded-md bg-white font-medium text-keppel-600 hover:text-keppel-500">
                                        Edit
                                    </button>
                                </span>
                            </dd>
                            <dd x-show="editing === 'bio'" x-cloak
                                class="mt-1 flex text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                                <span class="flex-grow">
                                    <label for="bio" class="sr-only">Bio</label>
                                    <div class="relative mt-1 rounded-md shadow-sm">
                                        <textarea id="bio" wire:model="bio" rows="3"
                                                  class="block w-full rounded-md shadow-sm border border-gray-300 focus:outline-none focus:border-keppel-500 focus:ring-keppel-500 sm:text-sm @error('bio') border-red-300 pr-10 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500 @enderror"
                                                  aria-invalid="true" aria-describedby="bio-error"></textarea>

                                        @error('bio')
                                        <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                                            <!-- Heroicon name: mini/exclamation-circle -->
                                            <svg class="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg"
                                                 viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                                <path fill-rule="evenodd"
                                                      d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z"
                                                      clip-rule="evenodd" />
                                            </svg>
                                        </div>
                                        @enderror
                                    </div>
                                    @error('bio')
                                    <p class="m-0 mt-2 text-sm text-red-600" id="bio-error">{{ $message }}</p>
                                    @enderror
                                </span>
                                <span class="ml-4 flex-shrink-0">
                                    <button type="button" wire:click="saveBio" wire:loading.attr="disabled"
                                            wire:target="saveBio"
                                            class="rounded-md bg-white font-medium text-keppel-600 hover:text-keppel-500">
                                        <span wire:loading.remove wire:target="saveBio">Save</span>
                                        <svg wire:loading.inline wire:target="saveBio" class="animate-spin h-4 w-4"
                                             xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor"
                                                    stroke-width="4"></circle>
                                            <path class="opacity-75" fill="currentColor"
                                                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                        </svg>
                                    </button>
                                </span>
                            </dd>
                        </div>

                        <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                            <dt class="text-sm font-medium leading-6 text-gray-900">Meta (Facebook) Profile</dt>
                            <dd x-show="editing !== 'facebook'"
                                class="mt-1 flex text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                                <span class="flex-grow">{{ $user->profile?->facebook ?? 'N/A' }}</span>
                                <span class="ml-4 flex-shrink-0">
                                    <button type="button" x-on:click="editing = 'facebook'"
                                            class="rounded-md bg-white font-medium text-keppel-600 hover:text-keppel-500">
                                        Edit
                                    </button>
                                </span>
                            </dd>
                            <dd x-show="editing === 'facebook'" x-cloak
                                class="mt-1 flex text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                                <span class="flex-grow">
                                    <label for="facebook" class="sr-only">Meta (Facebook) Profile</label>
                                    <div class="relative mt-1 rounded-md shadow-sm">
                                        <input type="text" wire:model="facebook" name="facebook"
                                               id="facebook"
                                               class="block w-full rounded-md border border-gray-300 focus:outline-none focus:border-keppel-500 focus:ring-keppel-500 sm:text-sm @error('facebook') border-red-300 pr-10 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500 @enderror"
                                               aria-invalid="true" aria-describedby="facebook-error">
                                        @error('facebook')
                                        <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                                            <!-- Heroicon name: mini/exclamation-circle -->
                                            <svg class="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg"
                                                 viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                                <path fill-rule="evenodd"
                                                      d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z"
                                                      clip-rule="evenodd" />
                                            </svg>
                                        </div>
                                        @enderror
                                    </div>
                                    @error('facebook')
                                    <p class="m-0 mt-2 text-sm text-red-600" id="facebook-error">{{ $message }}</p>
                                    @enderror
                                </span>
                                <span class="ml-4 flex-shrink-0">
                                    <button type="button" wire:click="saveFacebook" wire:loading.attr="disabled"
                                            wire:target="saveFacebook"
                                            class="rounded-md bg-white font-medium text-keppel-600 hover:text-keppel-500">
                                        <span wire:loading.remove wire:target="saveFacebook">Save</span>
                                        <svg wire:loading.inline wire:target="saveFacebook"
                                             class="animate-spin h-4 w-4"
                                             xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor"
                                                    stroke-width="4"></circle>
                                            <path class="opacity-75" fill="currentColor"
                                                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                        </svg>
                                    </button>
                                </span>
                            </dd>
                        </div>

                        <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                            <dt class="text-sm font-medium leading-6 text-gray-900">X (Twitter) Profile</dt>
                            <dd x-show="editing !== 'twitter'"
                                class="mt-1 flex text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                                <span class="flex-grow">{{ $user->profile?->twitter ?? 'N/A' }}</span>
                                <span class="ml-4 flex-shrink-0">
                                    <button type="button" x-on:click="editing = 'twitter'"
                                            class="rounded-md bg-white font-medium text-keppel-600 hover:text-keppel-500">
                                        Edit
                                    </button>
                                </span>
                            </dd>
                            <dd x-show="editing === 'twitter'" x-cloak
                                class="mt-1 flex text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                                <span class="flex-grow">
                                    <label for="twitter" class="sr-only">X (Twitter) Profile</label>
                                    <div class="relative mt-1 rounded-md shadow-sm">
                                        <input type="text" wire:model="twitter" name="twitter"
                                               id="twitter"
                                               class="block w-full rounded-md border border-gray-300 focus:outline-none focus:border-keppel-500 focus:ring-keppel-500 sm:text-sm @error('twitter') border-red-300 pr-10 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500 @enderror"
                                               aria-invalid="true" aria-describedby="twitter-error">
                                        @error('twitter')
                                        <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                                            <!-- Heroicon name: mini/exclamation-circle -->
                                            <svg class="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg"
                                                 viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                                <path fill-rule="evenodd"
                                                      d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z"
                                                      clip-rule="evenodd" />
                                            </svg>
                                        </div>
                                        @enderror
                                    </div>
                                    @error('twitter')
                                    <p class="m-0 mt-2 text-sm text-red-600" id="twitter-error">{{ $message }}</p>
                                    @enderror
                                </span>
                                <span class="ml-4 flex-shrink-0">
                                    <button type="button" wire:click="saveTwitter" wire:loading.attr="disabled"
                                            wire:target="saveTwitter"
                                            class="rounded-md bg-white font-medium text-keppel-600 hover:text-keppel-500">
                                        <span wire:loading.remove wire:target="saveTwitter">Save</span>
                                        <svg wire:loading.inline wire:target="saveTwitter"
                                             class="animate-spin h-4 w-4"
                                             xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor"
                                                    stroke-width="4"></circle>
                                            <path class="opacity-75" fill="currentColor"
                                                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                        </svg>
                                    </button>
                                </span>
                            </dd>
                        </div>

                        <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                            <dt class="text-sm font-medium leading-6 text-gray-900">LinkedIn Profile</dt>
                            <dd x-show="editing !== 'linkedin'"
                                class="mt-1 flex text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                                <span class="flex-grow">{{ $user->profile?->linkedin ?? 'N/A' }}</span>
                                <span class="ml-4 flex-shrink-0">
                                    <button type="button" x-on:click="editing = 'linkedin'"
                                            class="rounded-md bg-white font-medium text-keppel-600 hover:text-keppel-500">
                                        Edit
                                    </button>
                                </span>
                            </dd>
                            <dd x-show="editing === 'linkedin'" x-cloak
                                class="mt-1 flex text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                                <span class="flex-grow">
                                    <label for="linkedin" class="sr-only">LinkedIn Profile</label>
                                    <div class="relative mt-1 rounded-md shadow-sm">
                                        <input type="text" wire:model="linkedin" name="linkedin"
                                               id="linkedin"
                                               class="block w-full rounded-md border border-gray-300 focus:outline-none focus:border-keppel-500 focus:ring-keppel-500 sm:text-sm @error('linkedin') border-red-300 pr-10 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500 @enderror"
                                               aria-invalid="true" aria-describedby="linkedin-error">
                                        @error('linkedin')
                                        <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                                            <!-- Heroicon name: mini/exclamation-circle -->
                                            <svg class="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg"
                                                 viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                                <path fill-rule="evenodd"
                                                      d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z"
                                                      clip-rule="evenodd" />
                                            </svg>
                                        </div>
                                        @enderror
                                    </div>
                                    @error('linkedin')
                                    <p class="m-0 mt-2 text-sm text-red-600" id="linkedin-error">{{ $message }}</p>
                                    @enderror
                                </span>
                                <span class="ml-4 flex-shrink-0">
                                    <button type="button" wire:click="saveLinkedin" wire:loading.attr="disabled"
                                            wire:target="saveLinkedin"
                                            class="rounded-md bg-white font-medium text-keppel-600 hover:text-keppel-500">
                                        <span wire:loading.remove wire:target="saveLinkedin">Save</span>
                                        <svg wire:loading.inline wire:target="saveLinkedin"
                                             class="animate-spin h-4 w-4"
                                             xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor"
                                                    stroke-width="4"></circle>
                                            <path class="opacity-75" fill="currentColor"
                                                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                        </svg>
                                    </button>
                                </span>
                            </dd>

                        </div>

                        <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                            <dt class="text-sm font-medium leading-6 text-gray-900">Photo URL</dt>
                            <dd x-show="editing !== 'photo_path'"
                                class="mt-1 flex text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                                <span class="flex-grow">
                                    @if(! is_null($user->profile?->photo_path))
                                        <img class="flex-none h-16 w-16 rounded-full object-cover"
                                             src="{{ $user->profile?->photo_path }}"
                                             alt="">
                                    @else
                                        Photo not set
                                    @endif
                                </span>
                                <span class="ml-4 flex-shrink-0">
                                    <button type="button" x-on:click="editing = 'photo_path'"
                                            class="rounded-md bg-white font-medium text-keppel-600 hover:text-keppel-500">
                                        Edit
                                    </button>
                                </span>
                            </dd>
                            <dd x-show="editing === 'photo_path'" x-cloak
                                class="mt-1 flex text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                                <span class="flex-grow">
                                    <label for="photo_path" class="sr-only">Photo URL</label>
                                    <div class="relative mt-1 rounded-md shadow-sm">
                                        <input type="text" wire:model="photo_path" name="photo_path" id="photo_path"
                                               class="block w-full rounded-md border border-gray-300 focus:outline-none focus:border-keppel-500 focus:ring-keppel-500 sm:text-sm @error('photo_path') border-red-300 pr-10 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500 @enderror"
                                               aria-invalid="true" aria-describedby="photo_path-error">
                                        @error('photo_path')
                                        <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                                            <!-- Heroicon name: mini/exclamation-circle -->
                                            <svg class="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg"
                                                 viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                                <path fill-rule="evenodd"
                                                      d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z"
                                                      clip-rule="evenodd" />
                                            </svg>
                                        </div>
                                        @enderror
                                    </div>
                                    @error('photo_path')
                                    <p class="m-0 mt-2 text-sm text-red-600" id="photo_path-error">{{ $message }}</p>
                                    @enderror
                                </span>
                                <span class="ml-4 flex-shrink-0">
                                    <button type="button" wire:click="savePhoto" wire:loading.attr="disabled"
                                            wire:target="savePhoto"
                                            class="rounded-md bg-white font-medium text-keppel-600 hover:text-keppel-500">
                                        <span wire:loading.remove wire:target="savePhoto">Save</span>
                                        <svg wire:loading.inline wire:target="savePhoto"
                                             class="animate-spin h-4 w-4"
                                             xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor"
                                                    stroke-width="4"></circle>
                                            <path class="opacity-75" fill="currentColor"
                                                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                        </svg>
                                    </button>
                                </span>
                            </dd>
                        </div>
                    </dl>
                </div>
            </div>
        </div>
    </div>
</div>

