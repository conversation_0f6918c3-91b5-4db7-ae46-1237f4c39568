<div>
    <div>
        <div class="space-y-6 sm:space-y-8">
            <div class="overflow-hidden rounded-lg bg-white shadow">
                @php

                    $available = $product->inventoryAvailableForPurchase(check_backorder: false);
                    $is_unavailable = ! is_infinite($available) && $available <= 0;
                    $inventory_is_low = ! $is_unavailable
                        && ! is_infinite($available)
                        && $available > 0
                        && $product->isBelowReorderThreshold();
                    $is_above_reorder_threshold = is_infinite($available) || (! $inventory_is_low && !$is_unavailable);
                @endphp
                <div class="@if($is_above_reorder_threshold) bg-green-50 @elseif($inventory_is_low) bg-yellow-50 @else bg-red-50 @endif px-4 py-5 sm:p-6">
                    <div class="-ml-4 -mt-4 flex justify-between">
                        <div class="ml-4 mt-4">
                            <h3 class="text-base font-semibold leading-6 @if($is_above_reorder_threshold) text-green-800 @elseif($inventory_is_low) text-yellow-800 @else text-red-800 @endif">
                                Store inventory</h3>
                            <p class="m-0 mt-1 text-sm @if($is_above_reorder_threshold) text-green-700 @elseif($inventory_is_low) text-yellow-700 @else text-red-700 @endif">
                                @if($is_above_reorder_threshold)
                                    Available for purchase
                                @elseif($inventory_is_low)
                                    Available for purchase, but on-site inventory is low
                                @else
                                    Unavailable for purchase
                                @endif
                            </p>
                        </div>
                        <div class="ml-4 mt-4 flex-shrink-0">
                            <p class="flex items-baseline gap-x-2">
                                <span class="text-4xl font-semibold tracking-tight @if($is_above_reorder_threshold) text-green-800 @elseif($inventory_is_low) text-yellow-800 @else text-red-800 @endif">
                                    {{ is_infinite($available) ? '&infin;' : $available }}
                                </span>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="overflow-hidden rounded-lg bg-white shadow">
                <div class="px-4 py-5 sm:p-6">
                    <h2 class="text-base font-semibold leading-7 text-gray-900">Inventory settings</h2>
                    <p class="mt-1 max-w-2xl text-sm leading-6 text-gray-600">Set how inventory and availability is
                        configured for this product.</p>

                    <div class="mt-10 space-y-10 sm:divide-y sm:divide-gray-900/10">
                        <div class="sm:grid sm:grid-cols-3 sm:items-start sm:gap-4">
                            <label for="about" class="block text-sm font-medium leading-6 text-gray-900 sm:pt-1.5">Inventory
                                tracking</label>
                            <div class="mt-2 sm:col-span-2 sm:mt-0">
                                <fieldset class="m-0 p-0" aria-label="Choose a inventory tracking">
                                    <div class="grid grid-cols-3 gap-3">
                                        <label wire:click.prevent="setInventoryTracking('no')"
                                               class="flex cursor-pointer items-center justify-center rounded-md px-3 py-3 text-sm font-semibold uppercase @if($track_inventory === 'no') bg-keppel-600 text-white hover:bg-keppel-500 @else ring-1 ring-inset ring-gray-300 bg-white text-gray-900 hover:bg-gray-50 @endif focus:outline-none sm:flex-1">
                                            <input type="radio"
                                                   @checked($track_inventory === 'no') name="track_inventory" value="no"
                                                   class="sr-only">
                                            <span>None</span>
                                        </label>
                                        <label wire:click.prevent="setInventoryTracking('yes')"
                                               class="flex cursor-pointer items-center justify-center rounded-md px-3 py-3 text-sm font-semibold uppercase @if($track_inventory === 'yes') bg-keppel-600 text-white hover:bg-keppel-500 @else ring-1 ring-inset ring-gray-300 bg-white text-gray-900 hover:bg-gray-50 @endif focus:outline-none sm:flex-1">
                                            <input type="radio"
                                                   @checked($track_inventory === 'yes')  name="track_inventory"
                                                   value="yes" class="sr-only">
                                            <span>Standard</span>
                                        </label>
                                        <label wire:click.prevent="setInventoryTracking('bundle')"
                                               class="flex cursor-pointer items-center justify-center rounded-md px-3 py-3 text-sm font-semibold uppercase @if($track_inventory === 'bundle') bg-keppel-600 text-white hover:bg-keppel-500 @else ring-1 ring-inset ring-gray-300 bg-white text-gray-900 hover:bg-gray-50 @endif focus:outline-none sm:flex-1">
                                            <input type="radio"
                                                   @checked($track_inventory === 'bundle')  name="track_inventory"
                                                   value="bundle" class="sr-only">
                                            <span>Bundle</span>
                                        </label>
                                    </div>
                                </fieldset>
                                @error('track_inventory')
                                <p class="m-0 mt-2 text-sm text-red-600" id="track_inventory-error">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>
                    </div>


                    @if($track_inventory === 'yes')
                        <div class="pt-10">
                            <h2 class="text-base font-semibold leading-7 text-gray-900">Standard inventory
                                tracking</h2>
                            <p class="mt-1 max-w-2xl text-sm leading-6 text-gray-600">The configured settings are
                                used to determine store
                                availability.</p>
                            <div class="mt-10 space-y-10">
                                <div class="sm:grid sm:grid-cols-3 sm:items-start sm:gap-4">
                                    <label for="on-site"
                                           class="block text-sm font-medium leading-6 text-gray-900 sm:pt-1.5">On-site
                                        inventory</label>
                                    <div class="mt-2 sm:col-span-2 sm:mt-0">
                                        <input type="number" wire:model="on_site_inventory" name="on-site"
                                               id="on-site"
                                               class="block rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300
                                                   placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-keppel-600 sm:max-w-xs sm:text-sm sm:leading-6
                                                   @error('on_site_inventory') border-red-300 pr-10 text-red-900 placeholder-red-300 focus:border-red-500
                                                   focus:ring-red-500 focus:ring-red-500 @enderror" aria-invalid="true"
                                               aria-describedby="on-site-error">
                                        @error('on_site_inventory')
                                        <p class="m-0 mt-2 text-sm text-red-600"
                                           id="on-site-error">{{ $message }}</p>
                                        @enderror
                                        <p class="mt-3 text-sm inline-flex text-gray-500">
                                            The amount of inventory on-site and available for purchase in the store.
                                            This value is automatically adjusted
                                            through one-time purchases, subscription order generation, and order
                                            cancellations.
                                        </p>

                                        <button type="button"
                                                wire:click="$dispatch('open-modal-product-inventory-history', { product_id: {{ $product->id }} })"
                                                class="text-sm text-keppel-600">
                                            See history &rarr;
                                        </button>
                                    </div>
                                </div>
                                <div class="sm:grid sm:grid-cols-3 sm:items-start sm:gap-4">
                                    <label for="off-site"
                                           class="block text-sm font-medium text-gray-900 leading-6 sm:pt-1.5"
                                    >
                                        Off-site inventory
                                    </label>
                                    <div class="mt-2 sm:col-span-2 sm:mt-0">
                                        <input type="number" wire:model="off_site_inventory" name="off-site"
                                               id="off-site"
                                               class="block rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300
                                                   placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-keppel-600 sm:max-w-xs sm:text-sm sm:leading-6
                                                   @error('off_site_inventory') border-red-300 pr-10 text-red-900 placeholder-red-300 focus:border-red-500
                                                   focus:ring-red-500 focus:ring-red-500 @enderror" aria-invalid="true"
                                               aria-describedby="off-site-error">
                                        @error('off_site_inventory')
                                        <p class="m-0 mt-2 text-sm text-red-600"
                                           id="off-site-error">{{ $message }}</p>
                                        @enderror
                                        <p class="mt-3 text-sm inline-flex text-gray-500">
                                            An addtional amount of inventory not on-site but still available for
                                            purchase. This value never changes unless
                                            manually updated.
                                        </p>
                                    </div>
                                </div>
                                <div class="sm:grid sm:grid-cols-3 sm:items-start sm:gap-4">
                                    <label for="subscription-reserve"
                                           class="block text-sm font-medium leading-6 text-gray-900 sm:pt-1.5">Subscription
                                        reserve</label>
                                    <div class="mt-2 sm:col-span-2 sm:mt-0">
                                        <input type="number" wire:model="subscription_reserve"
                                               name="subscription-reserve" id="subscription-reserve"
                                               class="block rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300
                                                   placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-keppel-600 sm:max-w-xs sm:text-sm sm:leading-6
                                                   @error('subscription_reserve') border-red-300 pr-10 text-red-900 placeholder-red-300 focus:border-red-500
                                                   focus:ring-red-500 focus:ring-red-500 @enderror" aria-invalid="true"
                                               aria-describedby="subscription-reserve-error">
                                        @error('subscription_reserve')
                                        <p class="m-0 mt-2 text-sm text-red-600"
                                           id="subscription-reserve-error">{{ $message }}</p>
                                        @enderror
                                        <p class="mt-3 text-sm inline-flex text-gray-500">
                                            The amount of inventory reserved for subscription orders. This product
                                            will appear as sold out in the
                                            store when on-site + off-site inventory falls below this value.
                                        </p>
                                    </div>
                                </div>
                            </div>
                            <div class="sm:grid sm:grid-cols-3 sm:items-start sm:gap-4">
                                <label for="reorder-threshold"
                                       class="block text-sm font-medium leading-6 text-gray-900 sm:pt-1.5">Re-order
                                    threshold</label>
                                <div class="mt-2 sm:col-span-2 sm:mt-0">
                                    <input type="number" wire:model="reorder_threshold" name="reorder-threshold"
                                           id="reorder-threshold"
                                           class="block rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300
                                                   placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-keppel-600 sm:max-w-xs sm:text-sm sm:leading-6
                                                   @error('reorder_threshold') border-red-300 pr-10 text-red-900 placeholder-red-300 focus:border-red-500
                                                   focus:ring-red-500 focus:ring-red-500 @enderror" aria-invalid="true"
                                           aria-describedby="reorder-threshold-error">
                                    @error('reorder_threshold')
                                    <p class="m-0 mt-2 text-sm text-red-600"
                                       id="reorder-threshold-error">{{ $message }}</p>
                                    @enderror
                                    <p class="mt-3 text-sm inline-flex text-gray-500">
                                        The minimum amount of inventory you want to keep on-site. This product will
                                        appear on the re-order report
                                        when on-site inventory is less-than or equal to this value.
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="-mx-4 bg-gray-100 -mb-5 px-4 py-3 sm:p-4 sm:-mx-6 sm:-mb-6 ">
                            <div class="flex justify-end">
                                <button type="button" wire:click="saveStandardInventorySettings"
                                        wire:target="saveStandardInventorySettings" wire:loading.attr="disabled"
                                        class="inline-flex justify-center rounded-md bg-keppel-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-keppel-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-keppel-600">
                                    <span wire:loading.remove wire:target="saveStandardInventorySettings">Save</span>
                                    <svg wire:loading.inline wire:target="saveStandardInventorySettings"
                                         class="animate-spin h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none"
                                         viewBox="0 0 24 24">
                                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor"
                                                stroke-width="4"></circle>
                                        <path class="opacity-75" fill="currentColor"
                                              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>

                    @elseif($track_inventory === 'bundle')
                        <div class="pt-10">
                            <div class="sm:flex sm:items-center">
                                <div class="sm:flex-auto">
                                    <h1 class="text-base font-semibold leading-6 text-gray-900">Bundle inventory
                                        tracking</h1>
                                    <p class="mt-2 text-sm text-gray-700">The products contained in the bundle are used to
                                        determine how many bundles
                                        are available for purchase.</p>
                                </div>
                                <div class="mt-4 sm:ml-16 sm:mt-0 sm:flex-none">
                                    <button type="button"
                                            wire:click="$dispatch('open-modal-add-product-to-bundle', { bundle_id: {{ $product->id }} })"
                                            class="block rounded-md border border-gray-300 px-3 py-2 text-center text-sm font-semibold text-gray-900 shadow-sm hover:bg-gray-50 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-gray-600">
                                        Add product
                                    </button>
                                </div>
                            </div>
                            <div class="mt-8 flow-root">
                                @if ($product->bundle->isEmpty())
                                    <button type="button"
                                            wire:click="$dispatch('open-modal-add-product-to-bundle', { bundle_id: {{ $product->id }} })"
                                            class="relative block w-full rounded-lg border-2 border-dashed border-gray-300 p-12 text-center hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-keppel-500 focus:ring-offset-2">
                                        <svg class="mx-auto h-12 w-12 text-gray-400" xmlns="http://www.w3.org/2000/svg"
                                             fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round"
                                                  d="m21 7.5-2.25-1.313M21 7.5v2.25m0-2.25-2.25 1.313M3 7.5l2.25-1.313M3 7.5l2.25 1.313M3 7.5v2.25m9 3 2.25-1.313M12 12.75l-2.25-1.313M12 12.75V15m0 6.75 2.25-1.313M12 21.75V19.5m0 2.25-2.25-1.313m0-16.875L12 2.25l2.25 1.313M21 14.25v2.25l-2.25 1.313m-13.5 0L3 16.5v-2.25"/>
                                        </svg>
                                        <span class="mt-2 block text-sm font-semibold text-gray-900">Add a new product</span>
                                    </button>

                                @else
                                    <div class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
                                        <div class="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
                                            <table class="min-w-full divide-y divide-gray-300">
                                                <thead>
                                                <tr>
                                                    <th scope="col"
                                                        class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-0">
                                                        Product
                                                    </th>
                                                    <th scope="col"
                                                        class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                                                        Qty
                                                    </th>
                                                    <th scope="col"
                                                        class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                                                        Available
                                                    </th>
                                                    <th scope="col" class="relative py-3.5 pl-3 pr-4 sm:pr-0">
                                                        <span class="sr-only">Edit</span>
                                                    </th>
                                                </tr>
                                                </thead>
                                                <tbody class="divide-y divide-gray-200">
                                                @foreach($product->bundle as $base_product)
                                                    @php
                                                        $available = $base_product->inventoryAvailableForPurchase(check_backorder: false);
                                                    @endphp
                                                    <tr>
                                                        <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-0">
                                                            <a href="{{ route('admin.products.edit', ['product' => $base_product->id, 'tab' => 'inventory']) }}">{{ $base_product->title }}</a>
                                                        </td>
                                                        <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">{{ $base_product->getRelationValue('pivot')->qty }}</td>
                                                        <td class="flex items-center whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                                                            <span class="inline-flex items-center">
                                                                @if(!is_infinite($available) && $available > 0 && $base_product->isBelowReorderThreshold())
                                                                    <svg xmlns="http://www.w3.org/2000/svg"
                                                                         fill="none" viewBox="0 0 24 24"
                                                                         stroke-width="1.5" stroke="currentColor"
                                                                         class="mr-1 text-yellow-400 size-6">
                                                                        <path stroke-linecap="round"
                                                                              stroke-linejoin="round"
                                                                              d="M12 9v3.75m9-.75a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 3.75h.008v.008H12v-.008Z"/>
                                                                    </svg>
                                                                @elseif(!is_infinite($available) && $available <= 0)
                                                                    <svg xmlns="http://www.w3.org/2000/svg"
                                                                         fill="none" viewBox="0 0 24 24"
                                                                         stroke-width="1.5" stroke="currentColor"
                                                                         class="mr-1 text-red-400 size-5">
                                                                        <path stroke-linecap="round"
                                                                              stroke-linejoin="round"
                                                                              d="m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"/>
                                                                    </svg>
                                                                @endif
                                                                {{ is_infinite($available) ? '&infin;' : $available }}
                                                            </span>
                                                        </td>
                                                        <td class="relative whitespace-nowrap py-4 pl-3 pr-4 text-sm font-medium sm:pr-0">
                                                            <span class="ml-4 flex flex-shrink-0 justify-end space-x-4">
                                                                <button type="button"
                                                                        x-on:click="$dispatch('open-modal-edit-bundle-product', { bundle_id: {{ $product->id }}, product_id: {{ $base_product->id }} })"
                                                                        class="rounded-md font-medium text-keppel-600 hover:text-keppel-500 focus:outline-none focus:ring-2 focus:ring-keppel-500 focus:ring-offset-2">
                                                                    Edit
                                                                </button>
                                                                <span class="text-gray-300"
                                                                      aria-hidden="true">|</span>
                                                                <button type="button"
                                                                        x-on:click="$dispatch('open-modal-delete-bundle-product-confirmation', { bundle_id: {{ $product->id }}, product_id: {{ $base_product->id }} })"
                                                                        class="rounded-md font-medium text-keppel-600 hover:text-keppel-500 focus:outline-none focus:ring-2 focus:ring-keppel-500 focus:ring-offset-2">
                                                                    Remove
                                                                </button>
                                                            </span>
                                                        </td>
                                                    </tr>
                                                @endforeach
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                @endif
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
        @if($track_inventory !== 'bundle')
            <div class="mt-10 overflow-hidden rounded-lg bg-white shadow">
                <div class="px-4 py-5 sm:p-6">

                    @if ($product->containingBundles->isEmpty())
                        <div class="text-center">
                            <svg class="mx-auto h-12 w-12 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none"
                                 viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round"
                                      d="M3.75 3.75v4.5m0-4.5h4.5m-4.5 0L9 9M3.75 20.25v-4.5m0 4.5h4.5m-4.5 0L9 15M20.25 3.75h-4.5m4.5 0v4.5m0-4.5L15 9m5.25 11.25h-4.5m4.5 0v-4.5m0 4.5L15 15"/>
                            </svg>
                            <h3 class="mt-2 text-sm font-semibold text-gray-900">No shared inventory</h3>
                            <p class="mt-1 text-sm text-gray-500">This product does not share inventory with any bundle
                                products.</p>
                        </div>
                    @else
                        <div class="flow-root">
                            <h2 class="text-base font-semibold leading-7 text-gray-900">Shared Inventory</h2>
                            <p class="mt-1 max-w-2xl text-sm leading-6 text-gray-600">A list of bundle products that share
                                this product's inventory.</p>
                            <div class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
                                <div class="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
                                    <table class="min-w-full divide-y divide-gray-300">
                                        <thead>
                                        <tr>
                                            <th scope="col"
                                                class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-0">
                                                Product
                                            </th>
                                            <th scope="col"
                                                class="px-3 py-3.5 text-right text-sm font-semibold text-gray-900">Qty
                                            </th>
                                            <th scope="col"
                                                class="py-3.5 pl-3 pr-4 text-right text-sm font-semibold text-gray-900  sm:pr-0">
                                                Available
                                            </th>
                                        </tr>
                                        </thead>
                                        <tbody class="divide-y divide-gray-200">
                                        @foreach($product->containingBundles as $bundle_product)
                                            @php
                                                $available = $bundle_product->inventoryAvailableForPurchase(check_backorder: false);
                                            @endphp
                                            <tr>
                                                <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-0">
                                                    <a href="{{ route('admin.products.edit', ['product' => $bundle_product->id, 'tab' => 'inventory']) }}">{{ $bundle_product->title }}</a>
                                                </td>
                                                <td class="whitespace-nowrap px-3 py-4 text-right text-sm text-gray-500">{{ $bundle_product->getRelationValue('pivot')->qty }}</td>
                                                <td class="relative whitespace-nowrap py-4 pl-3 pr-4 text-right  text-sm font-medium sm:pr-0">
                                                    <span class="inline-flex items-center">
                                                        @if(!is_infinite($available) && $available > 0 && $bundle_product->isBelowReorderThreshold())
                                                            <svg xmlns="http://www.w3.org/2000/svg" fill="none"
                                                                 viewBox="0 0 24 24" stroke-width="1.5"
                                                                 stroke="currentColor"
                                                                 class="mr-1 text-yellow-400 size-6">
                                                                <path stroke-linecap="round" stroke-linejoin="round"
                                                                      d="M12 9v3.75m9-.75a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 3.75h.008v.008H12v-.008Z"/>
                                                            </svg>
                                                        @elseif(!is_infinite($available) && $available <= 0)
                                                            <svg xmlns="http://www.w3.org/2000/svg" fill="none"
                                                                 viewBox="0 0 24 24" stroke-width="1.5"
                                                                 stroke="currentColor"
                                                                 class="mr-1 text-red-400 size-5">
                                                                <path stroke-linecap="round" stroke-linejoin="round"
                                                                      d="m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"/>
                                                            </svg>
                                                        @endif
                                                        {{ is_infinite($available) ? '&infin;' : $available }}
                                                    </span>
                                                </td>
                                            </tr>
                                        @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        @endif
    </div>
</div>
