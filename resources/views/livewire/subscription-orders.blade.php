@php
    /** @var App\Models\RecurringOrder $subscription */
    /** @var App\Models\Order|null $next_order */
    /** @var int $delivery_count */
    /** @var int $aov */
    /** @var Illuminate\Support\Collection $available_delivery_methods */
@endphp

<div>
    <div x-data="{ editing: @entangle('editing').live }">
        <div class="bg-white shadow rounded-md px-4 py-5 sm:px-6">
            <div>
                <h1 class="text-base font-semibold leading-6 text-gray-900">Generation</h1>
                <p class="mt-2 text-sm text-gray-700">Configure how order generation works for this subscription.</p>

                <div class="mt-6 ">
                    <dl class="divide-y divide-gray-100">
                        <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                            <dt class="text-sm font-medium leading-6 text-gray-900">Delivery method</dt>
                            <dd x-show="editing !== 'delivery-method'" class="mt-1 flex text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                                <span class="flex-grow">{{ $subscription->fulfillment->title }}</span>
                                <span class="ml-4 flex-shrink-0">
                                    @if( ! $subscription->trashed())
                                        <button type="button" x-on:click="editing = 'delivery-method'" class="rounded-md bg-white font-medium text-keppel-600 hover:text-keppel-500">
                                            Update
                                        </button>
                                    @endif
                                </span>
                            </dd>
                            @if( ! $subscription->trashed())
                                <dd x-show="editing === 'delivery-method'" style="display:none" class="mt-1 flex text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                                    <span class="flex-grow">
                                        <label for="delivery-method" class="sr-only">Delivery method</label>
                                        <select wire:model="fulfillment_id" id="delivery-method" name="delivery-method" aria-describedby="delivery-method-description" class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 @if($subscription->trashed()) bg-gray-50 cursor-not-allowed @endif focus:ring-2 focus:ring-inset focus:ring-keppel-600 sm:max-w-xs sm:text-sm sm:leading-6">
                                            @foreach($available_delivery_methods as $available_delivery_method)
                                                <option value="{{ $available_delivery_method->id }}" wire:key="{{ $available_delivery_method->id }}">
                                                    {{ $available_delivery_method->title }}
                                                </option>
                                            @endforeach
                                        </select>
                                    </span>
                                    <span class="ml-4 flex-shrink-0">
                                        <button type="button" wire:click="saveDeliveryMethod" class="rounded-md bg-white font-medium text-keppel-600 hover:text-keppel-500">
                                            Save
                                        </button>
                                    </span>
                                </dd>
                            @endif
                        </div>
                        <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                            <dt class="text-sm font-medium leading-6 text-gray-900">Frequency</dt>
                            <dd x-show="editing !== 'frequency'" class="mt-1 flex text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                                <span class="flex-grow">
                                    {{ $subscription->formattedReorderFrequency() }}
                                    @if ( ! in_array($subscription->reorder_frequency, $subscription->reorderOptions()))
                                        <div class="mt-4 rounded-md bg-yellow-50 p-4">
                                            <div class="flex">
                                                <div class="flex-shrink-0">
                                                    <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z" clip-rule="evenodd"/>
                                                    </svg>
                                                </div>
                                                <div class="ml-3 flex-1 md:flex md:justify-between">
                                                    <p class="m-0 text-sm text-yellow-700">The selected frequency is not available on the delivery method's
                                                        schedule.</p>
                                                </div>
                                            </div>
                                        </div>
                                    @endif
                                </span>
                                <span class="ml-4 flex-shrink-0">
                                    @if( ! $subscription->trashed())
                                        <button type="button" x-on:click="editing = 'frequency'" class="rounded-md bg-white font-medium text-keppel-600 hover:text-keppel-500">
                                            Update
                                        </button>
                                    @endif
                                </span>
                            </dd>
                            @if( ! $subscription->trashed())
                                <dd x-show="editing === 'frequency'" style="display:none" class="mt-1 flex text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                                    <span class="flex-grow">
                                        <label for="reorder_frequency" class="sr-only">Frequency</label>
                                        <div class="mt-2">
                                            <select wire:model="reorder_frequency" id="reorder_frequency" name="reorder_frequency" aria-describedby="frequency-description" class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 @if($subscription->trashed()) bg-gray-50 cursor-not-allowed @endif focus:ring-2 focus:ring-inset focus:ring-keppel-600 sm:max-w-xs sm:text-sm sm:leading-6">
                                                <option value="7">Every week @if( ! in_array(7, $subscription->reorderOptions()))
                                                        - Unavailable
                                                    @endif</option>
                                                <option value="14">Every 2 weeks @if( ! in_array(14, $subscription->reorderOptions()))
                                                        - Unavailable
                                                    @endif</option>
                                                <option value="28">Every 4 weeks @if( ! in_array(28, $subscription->reorderOptions()))
                                                        - Unavailable
                                                    @endif</option>
                                                <option value="28">Every 6 weeks @if( ! in_array(42, $subscription->reorderOptions()))
                                                        - Unavailable
                                                    @endif</option>
                                                <option value="56">Every 8 weeks @if( ! in_array(56, $subscription->reorderOptions()))
                                                        - Unavailable
                                                    @endif</option>
                                            </select>

                                        </div>
                                    </span>
                                    <span class="ml-4 flex-shrink-0">
                                        <button type="button" wire:click="saveFrequency" class="rounded-md bg-white font-medium text-keppel-600 hover:text-keppel-500">
                                            Save
                                        </button>
                                    </span>
                                </dd>
                            @endif
                        </div>
                        <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                            <dt class="text-sm font-medium leading-6 text-gray-900">Next order</dt>
                            <dd x-show="editing !== 'delivery-date'" class="mt-1 flex text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                                <span class="flex-grow">
                                    <dl>
                                        <div class="py-1 sm:grid sm:grid-cols-3 sm:gap-4">
                                            <dt class="text-gray-500">Delivery</dt>
                                            <dd class="mt-1 flex text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                                                <span class="flex-grow">@if(is_null($ready_at))
                                                        <div class="inline-block rounded-md py-1 px-2 text-xs font-medium ring-1 ring-inset text-orange-700 bg-orange-50 ring-orange-600/10">
                                                            Date not set
                                                        </div>
                                                    @else
                                                        <time datetime="{{ $ready_at?->format('Y-m-d') }}">{{ $ready_at?->format('D, M jS, Y') }}</time>
                                                    @endif</span>
                                            </dd>
                                        </div>
                                        @if( ! is_null($ready_at))

                                            <div class="py-1 sm:grid sm:grid-cols-3 sm:gap-4">
                                                <dt class="text-gray-500">Deadline</dt>
                                                <dd class="mt-1 flex text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                                                    <span class="flex-grow">
                                                        <time datetime="{{ $deadline_at->format('Y-m-d H:i:s') }}">{{ $deadline_at->format('D, M jS, Y') }}
                                                            | {{ $deadline_at->format('g:iA') }}</time>
                                                    </span>
                                                </dd>
                                            </div>
                                            <div class="py-1 sm:grid sm:grid-cols-3 sm:gap-4">
                                                <dt class="text-gray-500">Generates</dt>
                                                <dd class="mt-1 flex text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                                                    <span class="flex-grow">
                                                        <time datetime="{{ $generates_at->format('Y-m-d H:i:s') }}">{{ $generates_at->format('D, M jS, Y') }}
                                                            | {{ $generates_at->format('g:iA') }}</time>
                                                    </span>
                                                </dd>
                                            </div>
                                        @endif
                                    </dl>
                                </span>
                                <span class="ml-4 flex-shrink-0">
                                    @if( ! $subscription->trashed())
                                        <button type="button" x-on:click="editing = 'delivery-date'" class="rounded-md bg-white font-medium text-keppel-600 hover:text-keppel-500">
                                            Update
                                        </button>
                                    @endif
                                </span>
                            </dd>
                            @if( ! $subscription->trashed())
                                <dd x-show="editing === 'delivery-date'" style="display:none" class="mt-1 flex text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                                    <span class="flex-grow">
                                        <label for="ready_at" class="block text-sm font-medium leading-6 text-gray-900">Delivery on</label>
                                        <div class="mt-2">
                                            <select wire:model="selected_ready_at" id="ready_at" name="ready_at" aria-describedby="delivery-date-description" class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 @if($subscription->trashed()) bg-gray-50 cursor-not-allowed @endif focus:ring-2 focus:ring-inset focus:ring-keppel-600 sm:max-w-xs sm:text-sm sm:leading-6">
                                                @foreach($available_order_windows as $order_window)
                                                    @php /** @var \App\OrderWindow $order_window */ @endphp
                                                    <option value="{{ $order_window->originalDate()->pickup_date->format('Y-m-d H:i:s') }}">
                                                        {{ $order_window->readyAtDatetime()->format('D, M j, Y') }}
                                                    </option>
                                                @endforeach
                                            </select>
                                        </div>
                                    </span>
                                    <span class="ml-4 flex-shrink-0">
                                        <button type="button" wire:click="saveDeliveryDate" class="rounded-md bg-white font-medium text-keppel-600 hover:text-keppel-500">
                                            Save
                                        </button>
                                    </span>
                                </dd>
                            @endif
                        </div>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <div>
        <div class="mt-8 bg-white shadow rounded-md px-4 py-5 sm:px-6">
            <div class="sm:flex sm:items-center">
                <div class="sm:flex-auto">
                    <h1 class="text-base font-semibold leading-6 text-gray-900">Orders</h1>
                    <p class="mt-2 text-sm text-gray-700">A list of orders generated and confirmed on this subscription.</p>
                </div>
            </div>
            <div class="mt-8 flow-root">
                <div class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
                    <div class="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
                        <table class="min-w-full divide-y divide-gray-300">
                            <thead>
                            <tr>
                                <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-0">Order</th>
                                <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Delivery date</th>
                                <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Total</th>
                                <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Status</th>
                                <th scope="col" class="relative py-3.5 pl-3 pr-4 sm:pr-0">
                                    <span class="sr-only">View</span>
                                </th>
                            </tr>
                            </thead>


                            <tbody class="divide-y divide-gray-200">
                            @foreach($orders as $order)
                                @php
                                    /** @var \App\Models\Order $order */
                                @endphp
                                <tr>
                                    <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-0">#{{ $order->id }}</td>
                                    <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">{{ $order->pickup_date->format('M j, Y') }}</td>
                                    <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">${{ money($order->total) }}</td>
                                    <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                                        @if($order->isCanceled())
                                            <span class="inline-flex rounded-full bg-gray-100 px-2 text-xs font-semibold leading-5 text-gray-800">
                                                Canceled @if( ! is_null($order->skipped_at))
                                                    / Skipped
                                                @endif</span>
                                        @elseif($order->isUnconfirmed())
                                            <span class="inline-flex rounded-full bg-yellow-100 px-2 text-xs font-semibold leading-5 text-yellow-800">
                                                Unconfirmed
                                            </span>
                                        @else
                                            <span class="inline-flex rounded-full bg-green-100 px-2 text-xs font-semibold leading-5 text-green-800">{{ \App\Support\Enums\OrderStatus::get($order->status_id) }}</span>
                                        @endif
                                    </td>
                                    <td class="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-0">
                                        <a href="{{ route('admin.orders.edit', compact('order')) }}" class="text-keppel-600 hover:text-keppel-900">View
                                            <span class="sr-only">, #{{ $order->id }}</span>
                                        </a>
                                    </td>
                                </tr>
                            @endforeach
                            </tbody>
                        </table>

                        <div class="mt-8">
                            {!! $orders->links('pagination.livewire-tailwind') !!}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

