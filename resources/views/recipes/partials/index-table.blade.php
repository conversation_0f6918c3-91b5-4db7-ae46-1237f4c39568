<div class="panel ">
    <div class="panel-body pa-0">
        <div class="table-responsive">
            <table class="table table-striped table-full table-list">
                <thead>
                <tr>
                    <th>{!! sortTable('Title', 'title') !!}</th>
                    <th>{!! sortTable('Published', 'published_at') !!}</th>
                    <th>{!! sortTable('Author', 'user_id') !!}</th>
                </tr>
                </thead>
                <tbody>
                @foreach($recipes as $recipe)
                    <tr>
                        <td>
                            <a href="{{ route('admin.recipes.edit', $recipe->slug) }}">{{ $recipe->title }}</a>
                            @if(!$recipe->published)
                                &nbsp;<span class="label label-danger">Draft</span>
                            @endif
                        </td>
                        <td>{{ $recipe->published_at->format('m/d/Y @ g:i A') }}</td>
                        <td>{{ $recipe->author ? $recipe->author->full_name : '' }}</td>
                    </tr>
                @endforeach
                </tbody>
                @if(!$recipes->count())<tr><td colspan="100%">No recipes found.</td></tr>@endif
            </table>
        </div>
    </div>
</div>