<div class="panel ">
    <div class="panel-body pa-0">
        <div class="table-responsive">
            <table class="table table-striped table-list table-full">
                <thead>
                <tr>
                    <th>{!! sortTable('Name', 'title') !!}</th>
                    <th>Sort Order</th>
                    <th>Products</th>
                </tr>
                </thead>
                <tbody>
                @foreach($collections as $collection)
                    <tr>
                        <td data-label="Name">
                            <a href="{{ route('admin.collections.edit', [$collection->id]) }}">{{ $collection->title }}</a>
                        </td>
                        <td data-label="Sort Order">{{ $collection->present()->sortOrder }}</td>
                        <td data-label="Products">{{ $collection->products->count() }}</td>
                    </tr>
                @endforeach
                @if(!$collections->count())
                    <tr>
                        <td colspan="100%"><h4>No collections found.</h4></td>
                    </tr>
                @endif
                </tbody>
            </table>
        </div>
    </div>
</div>
