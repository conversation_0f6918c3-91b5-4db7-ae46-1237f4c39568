<?php

namespace Tests\Unit\Actions;

use App\Actions\CreateRepeatingDates;
use App\Models\Schedule;
use Illuminate\Support\Carbon;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class CreateRepeatingDatesTest extends TenantTestCase
{
    #[Test]
    public function it_throws_an_exception_when_the_end_date_is_less_than_the_start_date(): void
    {
        Carbon::setTestNow(now());

        $schedule = Schedule::factory()->create();

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('The start date must come before the end date.');


        (new CreateRepeatingDates)->execute(
            $start = now(),
            $end = now()->subSecond(),
            $delivery = now(),
            $schedule->id,
            $stop = now(),
        );

        Carbon::setTestNow();
    }

    #[Test]
    public function it_throws_an_exception_when_the_delivery_date_is_less_than_the_end_date(): void
    {
        Carbon::setTestNow(now());

        $schedule = Schedule::factory()->create();

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('The delivery date must come after the end date.');

        (new CreateRepeatingDates)->execute(
            $start = now(),
            $end = now(),
            $delivery = now()->subSecond(),
            $schedule->id,
            $stop = now()
        );

        Carbon::setTestNow();
    }

    #[Test]
    public function it_generates_a_set_of_repeating_weekly_dates_between_the_given_range(): void
    {
        $schedule = Schedule::factory()->create();

        $expected_increments = 7;

        $dates_array = (new CreateRepeatingDates)->execute(
            $start = Carbon::parse('2022-01-11'),
            $end = Carbon::parse('2022-01-13'),
            $delivery = Carbon::parse('2022-01-14'),
            $schedule->id,
            $stop = Carbon::parse('2022-02-04')
        );

        $this->assertIsArray($dates_array);
        $this->assertCount(4, $dates_array);

        $expected_format = 'Y-m-d';

        foreach ($dates_array as $date_array) {
            $this->assertIsArray($date_array);
            $this->assertCount(4, $date_array);

            $this->assertTrue(isset($date_array['pickup_date']));
            $this->assertTrue(isset($date_array['order_start_date']));
            $this->assertTrue(isset($date_array['order_end_date']));
            $this->assertTrue(isset($date_array['schedule_id']));
            $this->assertEquals($schedule->id, $date_array['schedule_id']);
        }

        $this->assertEquals(Carbon::parse('2022-01-11')->format($expected_format), $dates_array[0]['order_start_date']);
        $this->assertEquals(Carbon::parse('2022-01-13')->format($expected_format), $dates_array[0]['order_end_date']);
        $this->assertEquals(Carbon::parse('2022-01-14')->format($expected_format), $dates_array[0]['pickup_date']);

        $this->assertEquals(Carbon::parse('2022-01-13')->format($expected_format), $dates_array[1]['order_start_date']);
        $this->assertEquals(Carbon::parse('2022-01-20')->format($expected_format), $dates_array[1]['order_end_date']);
        $this->assertEquals(Carbon::parse('2022-01-21')->format($expected_format), $dates_array[1]['pickup_date']);

        $this->assertEquals(Carbon::parse('2022-01-20')->format($expected_format), $dates_array[2]['order_start_date']);
        $this->assertEquals(Carbon::parse('2022-01-27')->format($expected_format), $dates_array[2]['order_end_date']);
        $this->assertEquals(Carbon::parse('2022-01-28')->format($expected_format), $dates_array[2]['pickup_date']);

        $this->assertEquals(Carbon::parse('2022-01-27')->format($expected_format), $dates_array[3]['order_start_date']);
        $this->assertEquals(Carbon::parse('2022-02-03')->format($expected_format), $dates_array[3]['order_end_date']);
        $this->assertEquals(Carbon::parse('2022-02-04')->format($expected_format), $dates_array[3]['pickup_date']);
    }

    #[Test]
    public function it_generates_a_set_of_repeating_using_a_custom_increment(): void
    {
        $schedule = Schedule::factory()->create();

        $expected_increments = 14;

        $dates_array = (new CreateRepeatingDates)->execute(
            $start = Carbon::parse('2022-01-11'),
            $end = Carbon::parse('2022-01-13'),
            $delivery = Carbon::parse('2022-01-14'),
            $schedule->id,
            $stop = Carbon::parse('2022-02-04'),
            $expected_increments
        );

        $this->assertIsArray($dates_array);
        $this->assertCount(2, $dates_array);

        $expected_format = 'Y-m-d';

        foreach ($dates_array as $date_array) {
            $this->assertIsArray($date_array);
            $this->assertCount(4, $date_array);

            $this->assertTrue(isset($date_array['pickup_date']));
            $this->assertTrue(isset($date_array['order_start_date']));
            $this->assertTrue(isset($date_array['order_end_date']));
            $this->assertTrue(isset($date_array['schedule_id']));
            $this->assertEquals($schedule->id, $date_array['schedule_id']);
        }

        $this->assertEquals(Carbon::parse('2022-01-11')->format($expected_format), $dates_array[0]['order_start_date']);
        $this->assertEquals(Carbon::parse('2022-01-13')->format($expected_format), $dates_array[0]['order_end_date']);
        $this->assertEquals(Carbon::parse('2022-01-14')->format($expected_format), $dates_array[0]['pickup_date']);

        $this->assertEquals(Carbon::parse('2022-01-13')->format($expected_format), $dates_array[1]['order_start_date']);
        $this->assertEquals(Carbon::parse('2022-01-27')->format($expected_format), $dates_array[1]['order_end_date']);
        $this->assertEquals(Carbon::parse('2022-01-28')->format($expected_format), $dates_array[1]['pickup_date']);
    }

    #[Test]
    public function it_generates_a_set_of_repeating_dates_using_a_weekly_increment_when_passed_increment_is_less_than_seven(): void
    {
        $schedule = Schedule::factory()->create();

        $dates_array = (new CreateRepeatingDates)->execute(
            $start = Carbon::parse('2022-01-11'),
            $end = Carbon::parse('2022-01-13'),
            $delivery = Carbon::parse('2022-01-14'),
            $schedule->id,
            $stop = Carbon::parse('2022-02-04'),
            6
        );

        $this->assertIsArray($dates_array);
        $this->assertCount(4, $dates_array);

        $expected_format = 'Y-m-d';

        foreach ($dates_array as $date_array) {
            $this->assertIsArray($date_array);
            $this->assertCount(4, $date_array);

            $this->assertTrue(isset($date_array['pickup_date']));
            $this->assertTrue(isset($date_array['order_start_date']));
            $this->assertTrue(isset($date_array['order_end_date']));
            $this->assertTrue(isset($date_array['schedule_id']));
            $this->assertEquals($schedule->id, $date_array['schedule_id']);
        }

        $this->assertEquals(Carbon::parse('2022-01-11')->format($expected_format), $dates_array[0]['order_start_date']);
        $this->assertEquals(Carbon::parse('2022-01-13')->format($expected_format), $dates_array[0]['order_end_date']);
        $this->assertEquals(Carbon::parse('2022-01-14')->format($expected_format), $dates_array[0]['pickup_date']);

        $this->assertEquals(Carbon::parse('2022-01-13')->format($expected_format), $dates_array[1]['order_start_date']);
        $this->assertEquals(Carbon::parse('2022-01-20')->format($expected_format), $dates_array[1]['order_end_date']);
        $this->assertEquals(Carbon::parse('2022-01-21')->format($expected_format), $dates_array[1]['pickup_date']);

        $this->assertEquals(Carbon::parse('2022-01-20')->format($expected_format), $dates_array[2]['order_start_date']);
        $this->assertEquals(Carbon::parse('2022-01-27')->format($expected_format), $dates_array[2]['order_end_date']);
        $this->assertEquals(Carbon::parse('2022-01-28')->format($expected_format), $dates_array[2]['pickup_date']);

        $this->assertEquals(Carbon::parse('2022-01-27')->format($expected_format), $dates_array[3]['order_start_date']);
        $this->assertEquals(Carbon::parse('2022-02-03')->format($expected_format), $dates_array[3]['order_end_date']);
        $this->assertEquals(Carbon::parse('2022-02-04')->format($expected_format), $dates_array[3]['pickup_date']);
    }

    #[Test]
    public function it_generates_a_set_of_repeating_dates_when_a_stop_date_is_null(): void
    {
        $schedule = Schedule::factory()->create();

        $dates_array = (new CreateRepeatingDates)->execute(
            $start = Carbon::parse('2022-01-11'),
            $end = Carbon::parse('2022-01-13'),
            $delivery = Carbon::parse('2022-01-14'),
            $schedule->id,
            $stop = null,
        );

        $this->assertIsArray($dates_array);
        $this->assertCount(26, $dates_array);
    }

    #[Test]
    public function it_generates_a_set_of_repeating_dates_when_a_stop_date_and_custom_increments_are_used(): void
    {
        $schedule = Schedule::factory()->create();

        $dates_array = (new CreateRepeatingDates)->execute(
            $start = Carbon::parse('2022-01-11'),
            $end = Carbon::parse('2022-01-13'),
            $delivery = Carbon::parse('2022-01-14'),
            $schedule->id,
            $stop = null,
            14
        );

        $this->assertIsArray($dates_array);
        $this->assertCount(13, $dates_array);
    }

    #[Test]
    public function it_generates_a_set_of_anchored_repeating_weekly_dates_between_the_given_range(): void
    {
        $schedule = Schedule::factory()->create();

        $expected_increments = 7;
        $expected_format = 'Y-m-d';

        $dates_array = (new CreateRepeatingDates)->execute(
            $start = Carbon::parse('2022-01-11'),
            $end = Carbon::parse('2022-01-13'),
            $delivery = Carbon::parse('2022-01-14'),
            $schedule->id,
            $stop = Carbon::parse('2022-02-04'),
            $expected_increments,
            $expected_format,
            $anchor = true
        );

        $this->assertIsArray($dates_array);
        $this->assertCount(4, $dates_array);

        foreach ($dates_array as $date_array) {
            $this->assertIsArray($date_array);
            $this->assertCount(4, $date_array);

            $this->assertTrue(isset($date_array['pickup_date']));
            $this->assertTrue(isset($date_array['order_start_date']));
            $this->assertTrue(isset($date_array['order_end_date']));
            $this->assertTrue(isset($date_array['schedule_id']));
            $this->assertEquals($schedule->id, $date_array['schedule_id']);
        }

        $this->assertEquals(Carbon::parse('2022-01-11')->format($expected_format), $dates_array[0]['order_start_date']);
        $this->assertEquals(Carbon::parse('2022-01-13')->format($expected_format), $dates_array[0]['order_end_date']);
        $this->assertEquals(Carbon::parse('2022-01-14')->format($expected_format), $dates_array[0]['pickup_date']);

        $this->assertEquals(Carbon::parse('2022-01-11')->format($expected_format), $dates_array[1]['order_start_date']);
        $this->assertEquals(Carbon::parse('2022-01-20')->format($expected_format), $dates_array[1]['order_end_date']);
        $this->assertEquals(Carbon::parse('2022-01-21')->format($expected_format), $dates_array[1]['pickup_date']);

        $this->assertEquals(Carbon::parse('2022-01-11')->format($expected_format), $dates_array[2]['order_start_date']);
        $this->assertEquals(Carbon::parse('2022-01-27')->format($expected_format), $dates_array[2]['order_end_date']);
        $this->assertEquals(Carbon::parse('2022-01-28')->format($expected_format), $dates_array[2]['pickup_date']);

        $this->assertEquals(Carbon::parse('2022-01-11')->format($expected_format), $dates_array[3]['order_start_date']);
        $this->assertEquals(Carbon::parse('2022-02-03')->format($expected_format), $dates_array[3]['order_end_date']);
        $this->assertEquals(Carbon::parse('2022-02-04')->format($expected_format), $dates_array[3]['pickup_date']);
    }

    #[Test]
    public function it_generates_a_set_of_repeating_weekly_dates_between_the_given_range_with_a_custom_format(): void
    {
        $schedule = Schedule::factory()->create();

        $expected_increments = 7;
        $expected_format = 'Y-m-d H:i:s';

        $dates_array = (new CreateRepeatingDates)->execute(
            $start = Carbon::parse('2022-01-11'),
            $end = Carbon::parse('2022-01-13'),
            $delivery = Carbon::parse('2022-01-14'),
            $schedule->id,
            $stop = Carbon::parse('2022-02-04'),
            $expected_increments,
            $expected_format
        );

        $this->assertIsArray($dates_array);
        $this->assertCount(4, $dates_array);

        foreach ($dates_array as $date_array) {
            $this->assertIsArray($date_array);
            $this->assertCount(4, $date_array);

            $this->assertTrue(isset($date_array['pickup_date']));
            $this->assertTrue(isset($date_array['order_start_date']));
            $this->assertTrue(isset($date_array['order_end_date']));
            $this->assertTrue(isset($date_array['schedule_id']));
            $this->assertEquals($schedule->id, $date_array['schedule_id']);
        }

        $this->assertEquals(Carbon::parse('2022-01-11')->format($expected_format), $dates_array[0]['order_start_date']);
        $this->assertEquals(Carbon::parse('2022-01-13')->format($expected_format), $dates_array[0]['order_end_date']);
        $this->assertEquals(Carbon::parse('2022-01-14')->format($expected_format), $dates_array[0]['pickup_date']);

        $this->assertEquals(Carbon::parse('2022-01-13')->format($expected_format), $dates_array[1]['order_start_date']);
        $this->assertEquals(Carbon::parse('2022-01-20')->format($expected_format), $dates_array[1]['order_end_date']);
        $this->assertEquals(Carbon::parse('2022-01-21')->format($expected_format), $dates_array[1]['pickup_date']);

        $this->assertEquals(Carbon::parse('2022-01-20')->format($expected_format), $dates_array[2]['order_start_date']);
        $this->assertEquals(Carbon::parse('2022-01-27')->format($expected_format), $dates_array[2]['order_end_date']);
        $this->assertEquals(Carbon::parse('2022-01-28')->format($expected_format), $dates_array[2]['pickup_date']);

        $this->assertEquals(Carbon::parse('2022-01-27')->format($expected_format), $dates_array[3]['order_start_date']);
        $this->assertEquals(Carbon::parse('2022-02-03')->format($expected_format), $dates_array[3]['order_end_date']);
        $this->assertEquals(Carbon::parse('2022-02-04')->format($expected_format), $dates_array[3]['pickup_date']);
    }
}
