<?php

namespace Tests\Unit\Actions\Billing;

use App\Actions\Billing\ProcessOrderPayment;
use App\Billing\Gateway\Transaction;
use App\Contracts\Billing;
use App\Events\Billing\CardWasDeclined;
use App\Exceptions\OrderChargeException;
use App\Models\Card;
use App\Models\Order;
use App\Models\OrderPayment;
use App\Models\Setting;
use App\Models\User;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Event;
use Mockery\MockInterface;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class ProcessOrderPaymentTest extends TenantTestCase
{
    #[Test]
    public function it_throws_exception_when_attempting_to_charge_an_order_for_a_customer_that_has_no_credit_card(): void
    {
        $customer = User::factory()->create();
        $order = Order::factory()->create(['customer_id' => $customer->id]);

        $this->mock(Billing::class, function (MockInterface $mock) {
            $mock->shouldNotReceive('chargeOrderWithCard');
        });

        $this->expectException(OrderChargeException::class);
        $this->expectExceptionMessage('There is no card on file for the customer.');

        (new ProcessOrderPayment)->handle($order);
    }

    #[Test]
    public function it_throws_exception_when_attempting_to_charge_an_order_for_a_total_due_lower_than_50_cents(): void
    {
        $card = Card::factory()->create();
        $order = Order::factory()->create(['customer_id' => $card->user_id, 'total' => 49]);

        $this->mock(Billing::class, function (MockInterface $mock) {
            $mock->shouldNotReceive('chargeOrderWithCard');
        });

        $this->expectException(OrderChargeException::class);
        $this->expectExceptionMessage('Payment cannot be processed for less than 50 cents.');

        (new ProcessOrderPayment)->handle($order);
    }

    #[Test]
    public function it_throws_exception_when_attempting_to_charge_an_order_with_a_custom_amount_lower_than_50_cents(): void
    {
        $card = Card::factory()->create();
        $order = Order::factory()->create(['customer_id' => $card->user_id, 'total' => 500]);

        $this->mock(Billing::class, function (MockInterface $mock) {
            $mock->shouldNotReceive('chargeOrderWithCard');
        });

        $this->expectException(OrderChargeException::class);
        $this->expectExceptionMessage('Payment cannot be processed for less than 50 cents.');

        (new ProcessOrderPayment)->handle($order, null, '0.49');
    }

    #[Test]
    public function it_handles_thrown_exception_when_charging_order(): void
    {
        Event::fake([CardWasDeclined::class]);

        Setting::updateOrCreate(['key' => 'stripe_user_id'],['value'  => 'some account']);

        $card = Card::factory()->create();
        $order = Order::factory()->create(['customer_id' => $card->user_id, 'total' => 500]);

        $expected_exception = new OrderChargeException('The customer\'s card was declined. An email has been sent to inform them.');

        $this->mock(Billing::class, function (MockInterface $mock) use ($expected_exception, $order, $card) {

            $mock->shouldReceive('chargeOrderWithCard')
                ->once()
                ->with(
                    \Mockery::on(fn(Order $arg) => $arg->id === $order->id),
                    \Mockery::on(fn(Card $arg) => $arg->id === $card->id),
                    500,
                    'Order #' . $order->id
                )
                ->andThrow($expected_exception);
        });

        $this->expectException(OrderChargeException::class);
        $this->expectExceptionMessage('The customer\'s card was declined. An email has been sent to inform them.');

        (new ProcessOrderPayment)->handle($order, $card);

        Event::assertDispatched(CardWasDeclined::class);
    }

    #[Test]
    public function it_can_charge_an_order_without_card(): void
    {
        Carbon::setTestNow(now());

        $card = Card::factory()->create();
        $order = Order::factory()->create(['customer_id' => $card->user_id, 'total' => 500]);

        $this->mock(Billing::class, function (MockInterface $mock) use ($order, $card) {
            $mock->shouldNotReceive('retrieveDefaultSourceForUser');

            $mock->shouldReceive('chargeOrderWithCard')
                ->once()
                ->with(
                    \Mockery::on(fn(Order $arg) => $arg->id === $order->id),
                    \Mockery::on(fn(Card $arg) => $arg->id === $card->id),
                    500,
                    'Order #' . $order->id
                )
                ->andReturn(new Transaction(
                    type: Transaction::TYPE_CHARGE,
                    id: 'charge_id',
                    amount: 500,
                    success: true,
                    source_id: 'some_source_id',
                    payment_type: 'some_source_brand',
                    description: 'Order #' . $order->id
                ));
        });

        $payment = (new ProcessOrderPayment)->handle($order);

        $this->assertInstanceOf(OrderPayment::class, $payment);

        $this->assertDatabaseHas(OrderPayment::class, [
            'order_id' => $order->id,
            'payment_type_id' => 2, // Credit card
            'payment_type' =>'some_source_brand',
            'customer_id' => $order->customer_id,
            'admin_id' => auth()->id() ?? 0,
            'amount' => 500,
            'payment_id' => 'charge_id',
            'source_id' => 'some_source_id',
            'description' => 'Order #' . $order->id
        ]);

        Carbon::setTestNow();
    }

    #[Test]
    public function it_can_charge_an_order_with_a_card(): void
    {
        Carbon::setTestNow(now());

        $card = Card::factory()->create();
        $order = Order::factory()->create(['customer_id' => $card->user_id, 'total' => 500]);

        $this->mock(Billing::class, function (MockInterface $mock) use ($order, $card) {
            $mock->shouldNotReceive('retrieveDefaultSourceForUser');

            $mock->shouldReceive('chargeOrderWithCard')
                ->once()
                ->with(
                    \Mockery::on(fn(Order $arg) => $arg->id === $order->id),
                    \Mockery::on(fn(Card $arg) => $arg->id === $card->id),
                    500,
                    'Order #' . $order->id
                )
                ->andReturn(new Transaction(
                    type: Transaction::TYPE_CHARGE,
                    id: 'charge_id',
                    amount: 500,
                    success: true,
                    source_id: 'some_source_id',
                    payment_type: 'some_source_brand',
                    description: 'Order #' . $order->id
                ));
        });

        $payment = (new ProcessOrderPayment)->handle($order, $card);

        $this->assertInstanceOf(OrderPayment::class, $payment);

        $this->assertDatabaseHas(OrderPayment::class, [
            'order_id' => $order->id,
            'payment_type_id' => 2, // Credit card
            'payment_type' =>'some_source_brand',
            'customer_id' => $order->customer_id,
            'admin_id' => auth()->id() ?? 0,
            'amount' => 500,
            'payment_id' => 'charge_id',
            'source_id' => 'some_source_id',
            'description' => 'Order #' . $order->id
        ]);

        Carbon::setTestNow();
    }

    #[Test]
    public function it_can_charge_an_order_with_an_amount(): void
    {
        Carbon::setTestNow(now());

        $card = Card::factory()->create();
        $order = Order::factory()->create(['customer_id' => $card->user_id, 'total' => 500]);

        $this->mock(Billing::class, function (MockInterface $mock) use ($order, $card) {
            $mock->shouldReceive('chargeOrderWithCard')
                ->once()
                ->with(
                    \Mockery::on(fn(Order $arg) => $arg->id === $order->id),
                    \Mockery::on(fn(Card $arg) => $arg->id === $card->id),
                    10000,
                    'Order #' . $order->id
                )
                ->andReturn(new Transaction(
                    type: Transaction::TYPE_CHARGE,
                    id: 'charge_id',
                    amount: 10000,
                    success: true,
                    source_id: 'some_source_id',
                    payment_type: 'some_source_brand',
                    description: 'Order #' . $order->id
                ));
        });

        $payment = (new ProcessOrderPayment)->handle($order, $card, 10000);

        $this->assertInstanceOf(OrderPayment::class, $payment);

        $this->assertDatabaseHas(OrderPayment::class, [
            'order_id' => $order->id,
            'payment_type_id' => 2, // Credit card
            'payment_type' =>'some_source_brand',
            'customer_id' => $order->customer_id,
            'admin_id' => auth()->id() ?? 0,
            'amount' => 10000,
            'payment_id' => 'charge_id',
            'source_id' => 'some_source_id',
            'description' => 'Order #' . $order->id
        ]);

        Carbon::setTestNow();
    }

    #[Test]
    public function it_can_charge_an_order_with_a_description_suffix(): void
    {
        Carbon::setTestNow(now());

        $card = Card::factory()->create();
        $order = Order::factory()->create(['customer_id' => $card->user_id, 'total' => 500]);

        $this->mock(Billing::class, function (MockInterface $mock) use ($order, $card) {
            $mock->shouldReceive('chargeOrderWithCard')
                ->once()
                ->with(
                    \Mockery::on(fn(Order $arg) => $arg->id === $order->id),
                    \Mockery::on(fn(Card $arg) => $arg->id === $card->id),
                    10000,
                    'Order #' . $order->id . ' (appended)'
                )
                ->andReturn(new Transaction(
                    type: Transaction::TYPE_CHARGE,
                    id: 'charge_id',
                    amount: 10000,
                    success: true,
                    source_id: 'some_source_id',
                    payment_type: 'some_source_brand',
                    description: 'Order #' . $order->id . ' (appended)'
                ));
        });

        $payment = (new ProcessOrderPayment)->handle($order, $card, 10000, 'appended');

        $this->assertInstanceOf(OrderPayment::class, $payment);

        $this->assertDatabaseHas(OrderPayment::class, [
            'order_id' => $order->id,
            'payment_type_id' => 2, // Credit card
            'payment_type' =>'some_source_brand',
            'customer_id' => $order->customer_id,
            'admin_id' => auth()->id() ?? 0,
            'amount' => 10000,
            'payment_id' => 'charge_id',
            'source_id' => 'some_source_id',
            'description' => 'Order #' . $order->id . ' (appended)'
        ]);

        Carbon::setTestNow();
    }
}
