<?php

namespace Tests\Unit\Actions;

use App\Actions\ActivateSchedule;
use App\Actions\CreateRepeatingDates;
use App\Models\Schedule;
use Carbon\Carbon;
use Mockery\MockInterface;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class ActivateScheduleTest extends TenantTestCase
{
    #[Test]
    public function it_can_create_repeating_dates(): void
    {
        Carbon::setTestNow(Carbon::parse('2022-06-27'));

        $schedule = Schedule::factory()->create([
            'first_delivery_date' => null,
            'first_delivery_deadline' => null,
            'active' => false,
            'ordering_in_advance' => 2
        ]);

        $this->mock(CreateRepeatingDates::class, function (MockInterface $mock) use ($schedule) {
                $mock->shouldReceive('execute')->andReturn([]);
        });

        app(ActivateSchedule::class)->execute([
            'first_delivery_date' => Carbon::parse('2022-06-30'),
            'order_cutoff' => 2,
            'ordering_in_advance' => 2
        ], $schedule);

        $this->assertDatabaseHas(Schedule::class, [
            'id' => $schedule->id,
            'first_delivery_date' => Carbon::parse('2022-06-30')->format('Y-m-d H:i:s'),
            'first_delivery_deadline' => Carbon::parse('2022-06-28')->format('Y-m-d H:i:s'),
            'active' => 1,
            'ordering_in_advance' => 2,
        ]);

        $this->assertTrue($schedule->extra_attributes['days_of_week'] === [4]);

        Carbon::setTestNow();
    }

    #[Test]
    public function it_throws_an_exception_when_the_first_delivery_date_is_in_the_past(): void {
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('The first delivery date must be in the future.');

        $schedule = Schedule::factory()->create();

        app(ActivateSchedule::class)->execute([
            'first_delivery_date' => today()->subDay(),
            'order_cutoff' => 2,
        ], $schedule);
    }

    #[Test]
    public function it_throws_an_exception_when_the_first_delivery_date_is_earlier_than_the_delivery_deadline_date(): void {
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('The delivery deadline date must come before the first delivery date.');

        $schedule = Schedule::factory()->create();

        app(ActivateSchedule::class)->execute([
            'first_delivery_date' => today()->addDay(),
            'order_cutoff' => -2,
        ], $schedule);
    }
}
