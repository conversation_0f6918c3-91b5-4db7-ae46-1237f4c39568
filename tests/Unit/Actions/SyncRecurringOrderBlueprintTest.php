<?php

namespace Tests\Unit\Actions;

use App\Actions\SyncRecurringOrderBlueprint;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Pickup;
use App\Models\RecurringOrder;
use App\Models\RecurringOrderItem;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class SyncRecurringOrderBlueprintTest extends TenantTestCase
{
    #[Test]
    public function it_syncs_current_order_items_to_recurring_blueprint(): void
    {
        $recurring_order = RecurringOrder::factory()->create();

        $order = Order::factory()->create(['blueprint_id' => $recurring_order->id, 'pickup_id' => Pickup::factory()]);
        $order_items = OrderItem::factory()->count(3)
            ->create(['order_id' => $order->id, 'type' => 'standard']);

        $existing_recurring_items = RecurringOrderItem::factory()->count(2)
            ->create(['order_id' => $recurring_order->id]);

        (new SyncRecurringOrderBlueprint)->execute($order, $recurring_order);

        $existing_recurring_items->each(function (RecurringOrderItem $recurring_item) {
            $this->assertDatabaseMissing(RecurringOrderItem::class, ['id' => $recurring_item]);
        });

        $this->assertDatabaseCount(RecurringOrderItem::class, 3);

        $order_items->each(function (OrderItem $recurring_item) use ($recurring_order) {
            $this->assertDatabaseHas(RecurringOrderItem::class, [
                'order_id' => $recurring_order->id,
                'product_id' => $recurring_item->product_id,
                'qty' => $recurring_item->qty,
                'type' => 'recurring'
            ]);
        });
    }

    #[Test]
    public function it_does_not_sync_current_order_addon_items_to_recurring_blueprint(): void
    {
        $recurring_order = RecurringOrder::factory()->create();
        $order = Order::factory()->create(['blueprint_id' => $recurring_order->id, 'pickup_id' => Pickup::factory()]);
        OrderItem::factory()->count(3)
            ->create(['order_id' => $order->id, 'type' => 'addon']);

        $existing_recurring_items = RecurringOrderItem::factory()->count(2)
            ->create(['order_id' => $recurring_order->id]);

        (new SyncRecurringOrderBlueprint)->execute($order, $recurring_order);

        $existing_recurring_items->each(function (RecurringOrderItem $recurring_item) {
            $this->assertDatabaseMissing(RecurringOrderItem::class, ['id' => $recurring_item]);
        });

        $this->assertDatabaseCount(RecurringOrderItem::class, 0);
    }
}