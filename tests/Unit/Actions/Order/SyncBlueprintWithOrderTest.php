<?php

namespace Tests\Unit\Actions\Order;

use App\Actions\Order\SyncBlueprintWithOrder;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\RecurringOrder;
use App\Models\RecurringOrderItem;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class SyncBlueprintWithOrderTest extends TenantTestCase
{
    #[Test]
    public function it_syncs_recurring_order_items_from_an_order(): void
    {
        $blueprint = RecurringOrder::factory()->create();
        $blueprint_items = RecurringOrderItem::factory()->count(2)
            ->create(['order_id' => $blueprint->id]);

        $blueprint_promo_item = RecurringOrderItem::factory()->create(['order_id' => $blueprint->id, 'type' => 'promo']);

        $order = Order::factory()->create([
            'blueprint_id' => $blueprint->id,
            'pickup_date' => today()->addDay(),
            'deadline_date' => today(),
        ]);

        $item = OrderItem::factory()->create([
            'order_id' => $order->id,
            'type' => 'standard',
            'qty' => 2,
            'fulfilled_qty' => 3
        ]);

        $promo_item = OrderItem::factory()->create([
            'order_id' => $order->id,
            'type' => 'promo',
            'qty' => 3,
            'fulfilled_qty' => 4
        ]);

        $addon_item = OrderItem::factory()->create([
            'order_id' => $order->id,
            'type' => 'addon',
            'qty' => 3,
            'fulfilled_qty' => 4
        ]);

        (new SyncBlueprintWithOrder)->handle($blueprint, $order);

        // all existing items are removed
        $blueprint_items->each(function (RecurringOrderItem $item) {
            $this->assertDatabaseMissing(RecurringOrderItem::class, ['id' => $item->id]);
        });

        // standard items are added
        $this->assertDatabaseHas(RecurringOrderItem::class, [
            'order_id' => $blueprint->id,
            'product_id' => $item->product_id,
            'qty' => 2,
            'type' => 'recurring'
        ]);

        // promo item is not changed
        $this->assertDatabaseMissing(RecurringOrderItem::class, [
            'order_id' => $blueprint->id,
            'product_id' => $promo_item->product_id,
            'type' => 'promo'
        ]);

        $this->assertDatabaseHas(RecurringOrderItem::class, [
            'id' => $blueprint_promo_item->id,
            'order_id' => $blueprint->id,
            'product_id' => $blueprint_promo_item->product_id,
            'qty' => $blueprint_promo_item->qty,
            'type' => 'promo'
        ]);

        // addon items are not added
        $this->assertDatabaseMissing(RecurringOrderItem::class, [
            'order_id' => $blueprint->id,
            'product_id' => $addon_item->product_id,
        ]);
    }
}