<?php

namespace Tests\Unit\Actions\Order;

use App\Actions\Order\AddRecurringItem;
use App\Events\Order\ItemWasAddedToOrder;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Product;
use App\Models\RecurringOrderItem;
use App\Models\Tag;
use App\Services\SubscriptionSettingsService;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Event;
use Mockery\MockInterface;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class AddRecurringItemTest extends TenantTestCase
{
    #[Test]
    public function it_can_add_a_standard_item(): void
    {
        Carbon::setTestNow(now());

        $order = Order::factory()->create();

        $item = RecurringOrderItem::factory()->create(['type' => 'recurring', 'qty' => 2]);

        $result = (new AddRecurringItem)->handle($order, $item);

        $this->assertDatabaseHas(OrderItem::class, [
            'id' => $result->id,
            'order_id' => $order->id,
            'product_id' => $item->product->id,
            'user_id' => $order->customer_id,
            'title' => $item->product->title,
            'type' => 'standard',
            'unit_price' => $item->product->getUnitPrice(),
            'original_unit_price' => $item->product->getUnitPrice(),
            'qty' => 2,
            'original_qty' => 2,
            'weight' => $item->product->weight * 2,
            'original_weight' => $item->product->weight * 2,
            'store_price' => $item->product->getPrice(),
            'unit_of_issue' => $item->product->unit_of_issue,
            'taxable' => $item->product->taxable,
            'created_year' => today()->year,
            'created_month' => today()->month,
            'created_day' => today()->day,
        ]);

        Carbon::setTestNow();
    }

    #[Test]
    public function it_can_add_a_promo_item(): void
    {
        Carbon::setTestNow(now());

        $order = Order::factory()->create();
        $item = RecurringOrderItem::factory()->create(['type' => 'promo', 'qty' => 2]);

        $result = (new AddRecurringItem)->handle($order, $item);

        $this->assertDatabaseHas(OrderItem::class, [
            'id' => $result->id,
            'order_id' => $order->id,
            'product_id' => $item->product->id,
            'user_id' => $order->customer_id,
            'title' => $item->product->title,
            'type' => 'promo',
            'unit_price' => $item->product->getUnitPrice(),
            'original_unit_price' => $item->product->getUnitPrice(),
            'qty' => 2,
            'original_qty' => 2,
            'weight' => $item->product->weight * 2,
            'original_weight' => $item->product->weight * 2,
            'store_price' => $item->product->getPrice(),
            'unit_of_issue' => $item->product->unit_of_issue,
            'taxable' => $item->product->taxable,
            'created_year' => today()->year,
            'created_month' => today()->month,
            'created_day' => today()->day,
        ]);

        Carbon::setTestNow();
    }

    #[Test]
    public function it_adds_an_addon_item_when_adding_an_excluded_product_to_a_recurring_order(): void
    {
        $order = Order::factory()->create();
        $item = RecurringOrderItem::factory()->create(['type' => 'recurring', 'qty' => 2]);

        $this->mock(SubscriptionSettingsService::class, function (MockInterface $mock) use ($item) {
            $mock->shouldReceive('excludedProductIds')->once()->andReturn(collect([$item->product_id]));
        });

        $result = (new AddRecurringItem)->handle($order, $item);

        $this->assertEquals('addon', $result->type);

        $this->assertDatabaseHas(OrderItem::class, [
            'id' => $result->id,
            'type' => 'addon'
        ]);
    }

    #[Test]
    public function it_can_add_an_item_with_a_unit_price_override(): void
    {
        Carbon::setTestNow(now());

        $order = Order::factory()->create();

        $item = RecurringOrderItem::factory()->create(['type' => 'recurring', 'qty' => 2, 'unit_price_override' => 9876]);

        $result = (new AddRecurringItem)->handle($order, $item);

        $this->assertDatabaseHas(OrderItem::class, [
            'id' => $result->id,
            'order_id' => $order->id,
            'product_id' => $item->product->id,
            'user_id' => $order->customer_id,
            'title' => $item->product->title,
            'type' => 'standard',
            'unit_price' => 9876,
            'original_unit_price' => 9876,
            'qty' => 2,
            'original_qty' => 2,
            'weight' => $item->product->weight * 2,
            'original_weight' => $item->product->weight * 2,
            'store_price' => $item->product->getPrice(),
            'unit_of_issue' => $item->product->unit_of_issue,
            'taxable' => $item->product->taxable,
            'created_year' => today()->year,
            'created_month' => today()->month,
            'created_day' => today()->day,
        ]);

        $this->assertDatabaseHas(RecurringOrderItem::class, [
            'id' => $item->id,
            'unit_price_override' => null
        ]);

        Carbon::setTestNow();
    }

    #[Test]
    public function it_does_not_decrement_inventory_when_item_has_been_added_to_an_unconfirmed_order(): void
    {
        $order = Order::factory()->create(['confirmed' => false]);
        $product = Product::factory()->create(['track_inventory' => 'yes', 'inventory' => 10]);
        $item = RecurringOrderItem::factory()->create(['type' => 'recurring', 'qty' => 2, 'product_id' => $product->id]);

        $result = (new AddRecurringItem)->handle($order, $item);

        $this->assertInstanceOf(OrderItem::class, $result);

        $this->assertDatabaseHas(Product::class, [
            'id' => $product->id,
            'inventory' => 10
        ]);
    }

    #[Test]
    public function it_does_not_decrement_inventory_when_item_has_been_added_to_a_confirmed_order(): void
    {
        // TODO: check on if this is actually the behavior we want
        $order = Order::factory()->create(['confirmed' => true]);
        $product = Product::factory()->create(['track_inventory' => 'yes', 'inventory' => 10]);
        $item = RecurringOrderItem::factory()->create(['type' => 'recurring', 'qty' => 2, 'product_id' => $product->id]);

        $this->actingAsAdmin();

        $result = (new AddRecurringItem)->handle($order, $item);

        $this->assertInstanceOf(OrderItem::class, $result);

        $this->assertDatabaseHas(Product::class, [
            'id' => $item->product->id,
            'inventory' => 10
        ]);
    }

    #[Test]
    public function it_does_not_record_event_when_item_has_been_added_to_a_confirmed_order(): void
    {
        // TODO: check on if this is actually the behavior we want
        $order = Order::factory()->create(['confirmed' => true]);
        $item = RecurringOrderItem::factory()->create(['type' => 'recurring', 'qty' => 2]);

        $this->actingAsAdmin();

        $result = (new AddRecurringItem)->handle($order, $item);

        $this->assertInstanceOf(OrderItem::class, $result);

        $this->assertDatabaseMissing(\App\Models\Event::class, [
            'model_type' => \App\Models\Order::class,
            'model_id' => $order->id,
        ]);
    }

    #[Test]
    public function it_does_not_dispatch_an_event_when_adding_an_item(): void
    {
        Event::fake([ItemWasAddedToOrder::class]);

        $order = Order::factory()->create();
        $item = RecurringOrderItem::factory()->create(['type' => 'recurring', 'qty' => 2]);

        $result = (new AddRecurringItem)->handle($order, $item);

        $this->assertInstanceOf(OrderItem::class, $result);

        Event::assertNotDispatched(ItemWasAddedToOrder::class);
    }

    #[Test]
    public function it_tags_the_order_when_attempting_to_add_an_excluded_item(): void
    {
        $order = Order::factory()->create();
        $item = RecurringOrderItem::factory()->create(['type' => 'recurring', 'qty' => 2]);

        $order->pickup->products()->attach($item->product->id);

        $result = (new AddRecurringItem)->handle($order, $item);

        $this->assertInstanceOf(OrderItem::class, $result);

        $this->assertTrue($order->tags()->where('title', 'Product Error')->exists());
    }

    #[Test]
    public function it_tags_the_order_when_attempting_to_add_a_quantity_greater_than_the_product_limit(): void
    {
        $order = Order::factory()->create();
        $product = Product::factory()->create(['settings' => ['quantity_limit' => 2]]);
        $item = RecurringOrderItem::factory()->create(['type' => 'recurring', 'qty' => 3, 'product_id' => $product->id]);

        $result = (new AddRecurringItem)->handle($order, $item);

        $this->assertInstanceOf(OrderItem::class, $result);

        $this->assertTrue($order->tags()->where('title', Tag::INVENTORY_ERROR)->exists());
    }

    #[Test]
    public function it_does_not_tag_the_order_when_attempting_to_add_an_item_without_available_quantity(): void
    {
        $order = Order::factory()->create();
        $product = Product::factory()->create(['track_inventory' => 'yes', 'inventory' => 1]);
        $item = RecurringOrderItem::factory()->create(['type' => 'recurring', 'qty' => 2, 'product_id' => $product->id]);

        $result = (new AddRecurringItem)->handle($order, $item);

        $this->assertInstanceOf(OrderItem::class, $result);

        $this->assertFalse($order->tags()->exists());
    }

    #[Test]
    public function it_does_not_tag_the_order_when_attempting_to_update_an_existing_item_without_available_quantity(): void
    {
        $order = Order::factory()->create();
        $product = Product::factory()->create(['track_inventory' => 'yes', 'inventory' => 4]);
        $item = RecurringOrderItem::factory()->create(['type' => 'recurring', 'qty' => 2, 'product_id' => $product->id]);

        OrderItem::factory()->create(['order_id' => $order->id, 'product_id' => $item->product->id, 'qty' => 2]);

        $result = (new AddRecurringItem)->handle($order, $item);

        $this->assertInstanceOf(OrderItem::class, $result);

        $this->assertFalse($order->tags()->exists());
    }

    #[Test]
    public function it_does_not_update_an_existing_item_when_adding_an_already_added_product(): void
    {
        Event::fake([ItemWasAddedToOrder::class]);

        $order = Order::factory()->create();
        $recurring_item = RecurringOrderItem::factory()->create(['type' => 'recurring', 'qty' => 3]);

        $item = OrderItem::factory()->create(['order_id' => $order->id, 'product_id' => $recurring_item->product->id, 'qty' => 2]);

        $result = (new AddRecurringItem)->handle($order, $recurring_item);

        $this->assertNotEquals($recurring_item->id, $result->id);
        $this->assertEquals(3, $result->qty);

        $this->assertDatabaseHas(OrderItem::class, [
            'id' => $item->id,
            'qty' => 2
        ]);

        $this->assertDatabaseHas(OrderItem::class, [
            'id' => $result->id,
            'qty' => 3
        ]);
    }
}
