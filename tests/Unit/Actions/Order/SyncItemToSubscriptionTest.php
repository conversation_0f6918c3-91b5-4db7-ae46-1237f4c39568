<?php

namespace Tests\Unit\Actions\Order;

use App\Actions\Order\SyncItemToSubscription;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Product;
use App\Models\RecurringOrder;
use App\Models\RecurringOrderItem;
use Illuminate\Support\Carbon;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class SyncItemToSubscriptionTest extends TenantTestCase
{
    #[Test]
    public function it_does_not_sync_item_when_there_is_not_a_subscription(): void
    {
        Carbon::setTestNow(now());

        $order = Order::factory()->create(['blueprint_id' => null, 'pickup_date' => today()->addDay()]);
        $order_item = OrderItem::factory()->create(['order_id' => $order->id]);

        (new SyncItemToSubscription)->handle($order_item);

        $this->assertDatabaseMissing(RecurringOrderItem::class, [
            'product_id' => $order_item->product_id
        ]);

        Carbon::setTestNow();
    }

    #[Test]
    public function it_adds_new_addon_subscription_item_when_not_already_on_subscription(): void
    {
        Carbon::setTestNow(now());

        $subscription = RecurringOrder::factory()->create();
        $order = Order::factory()->create(['blueprint_id' => $subscription->id, 'pickup_date' => today()->addDay()]);

        $product = Product::factory()->create(['unit_price' => '10.00']);
        $order_item = OrderItem::factory()->create([
            'order_id' => $order->id,
            'product_id' => $product->id,
            'type' => 'addon',
            'qty' => 2
        ]);

        (new SyncItemToSubscription)->handle($order_item);

        $this->assertDatabaseHas(RecurringOrderItem::class, [
            'order_id' => $subscription->id,
            'product_id' => $order_item->product_id,
            'type' => 'addon',
            'qty' => 2,
        ]);

        Carbon::setTestNow();
    }

    #[Test]
    public function it_updates_existing_addon_subscription_item_when_product_already_on_subscription(): void
    {
        Carbon::setTestNow(now());

        $subscription = RecurringOrder::factory()->create();
        $order = Order::factory()->create(['blueprint_id' => $subscription->id, 'pickup_date' => today()->addDay()]);
        $product = Product::factory()->create(['unit_price' => '10.00']);
        $order_item = OrderItem::factory()->create([
            'order_id' => $order->id,
            'product_id' => $product->id,
            'type' => 'addon',
            'qty' => 3,
            'unit_price' => 100
        ]);

        $subscription_item = RecurringOrderItem::factory()->create([
            'order_id' => $subscription->id,
            'product_id' => $product->id,
            'type' => 'addon',
            'qty' => 2
        ]);

        (new SyncItemToSubscription)->handle($order_item);

        $this->assertDatabaseHas(RecurringOrderItem::class, [
            'order_id' => $subscription->id,
            'product_id' => $subscription_item->product_id,
            'type' => 'addon',
            'qty' => 3,
        ]);

        $this->assertDatabaseMissing(RecurringOrderItem::class, [
            'id' => $subscription_item->id,
            'product_id' => $subscription_item->product_id,
            'type' => 'addon',
            'qty' => 2,
        ]);

        Carbon::setTestNow();
    }

    #[Test]
    public function it_adds_new_addon_subscription_item_when_product_already_on_subscription_but_type_doesnt_match(): void
    {
        Carbon::setTestNow(now());

        $subscription = RecurringOrder::factory()->create();
        $order = Order::factory()->create(['blueprint_id' => $subscription->id, 'pickup_date' => today()->addDay()]);
        $product = Product::factory()->create(['unit_price' => '10.00']);
        $subscription_item = RecurringOrderItem::factory()->create([
            'order_id' => $subscription->id,
            'product_id' => $product->id,
            'type' => 'recurring',
            'qty' => 3,
        ]);

        $order_item = OrderItem::factory()->create([
            'order_id' => $order->id,
            'product_id' => $product->id,
            'type' => 'addon',
            'qty' => 2,
            'unit_price' => 100
        ]);

        (new SyncItemToSubscription)->handle($order_item);

        $this->assertDatabaseHas(RecurringOrderItem::class, [
            'order_id' => $subscription->id,
            'product_id' => $subscription_item->product_id,
            'type' => 'addon',
            'qty' => 2,
        ]);

        $this->assertDatabaseHas(RecurringOrderItem::class, [
            'order_id' => $subscription->id,
            'product_id' => $subscription_item->product_id,
            'type' => 'recurring',
            'qty' => 3,
        ]);

        Carbon::setTestNow();
    }

    #[Test]
    public function it_adds_new_recurring_order_item_when_not_already_on_subscription(): void
    {
        Carbon::setTestNow(now());

        $subscription = RecurringOrder::factory()->create();
        $order = Order::factory()->create(['blueprint_id' => $subscription->id, 'pickup_date' => today()->addDay()]);

        $product = Product::factory()->create(['unit_price' => '10.00']);
        $order_item = OrderItem::factory()->create([
            'order_id' => $order->id,
            'product_id' => $product->id,
            'type' => 'recurring',
            'qty' => 2
        ]);

        (new SyncItemToSubscription)->handle($order_item);

        $this->assertDatabaseHas(RecurringOrderItem::class, [
            'order_id' => $subscription->id,
            'product_id' => $order_item->product_id,
            'type' => 'recurring',
            'qty' => 2,
        ]);

        Carbon::setTestNow();
    }

    #[Test]
    public function it_updates_existing_recurring_order_item_when_product_already_on_subscription(): void
    {
        Carbon::setTestNow(now());

        $subscription = RecurringOrder::factory()->create();
        $order = Order::factory()->create(['blueprint_id' => $subscription->id, 'pickup_date' => today()->addDay()]);
        $product = Product::factory()->create(['unit_price' => '10.00']);
        $order_item = OrderItem::factory()->create([
            'order_id' => $order->id,
            'product_id' => $product->id,
            'type' => 'standard',
            'qty' => 3,
            'unit_price' => 100
        ]);

        $subscription_item = RecurringOrderItem::factory()->create([
            'order_id' => $subscription->id,
            'product_id' => $product->id,
            'type' => 'recurring',
            'qty' => 2
        ]);

        (new SyncItemToSubscription)->handle($order_item);

        $this->assertDatabaseHas(RecurringOrderItem::class, [
            'order_id' => $subscription->id,
            'product_id' => $subscription_item->product_id,
            'type' => 'recurring',
            'qty' => 3,
        ]);

        $this->assertDatabaseMissing(RecurringOrderItem::class, [
            'id' => $subscription_item->id,
            'product_id' => $subscription_item->product_id,
            'type' => 'recurring',
            'qty' => 2,
        ]);

        Carbon::setTestNow();
    }

    #[Test]
    public function it_adds_new_recurring_order_item_when_product_already_on_subscription_but_type_doesnt_match(): void
    {
        Carbon::setTestNow(now());

        $subscription = RecurringOrder::factory()->create();
        $order = Order::factory()->create(['blueprint_id' => $subscription->id, 'pickup_date' => today()->addDay()]);
        $product = Product::factory()->create(['unit_price' => '10.00']);
        $subscription_item = RecurringOrderItem::factory()->create([
            'order_id' => $subscription->id,
            'product_id' => $product->id,
            'type' => 'addon',
            'qty' => 3,
        ]);

        $order_item = OrderItem::factory()->create([
            'order_id' => $order->id,
            'product_id' => $product->id,
            'type' => 'standard',
            'qty' => 2,
            'unit_price' => 100

        ]);

        (new SyncItemToSubscription)->handle($order_item);

        $this->assertDatabaseHas(RecurringOrderItem::class, [
            'order_id' => $subscription->id,
            'product_id' => $subscription_item->product_id,
            'type' => 'recurring',
            'qty' => 2,
        ]);

        $this->assertDatabaseHas(RecurringOrderItem::class, [
            'id' => $subscription_item->id,
            'product_id' => $subscription_item->product_id,
            'type' => 'addon',
            'qty' => 3,
        ]);

        Carbon::setTestNow();
    }

    #[Test]
    public function it_adds_new_promo_order_item_when_not_already_on_subscription(): void
    {
        Carbon::setTestNow(now());

        $subscription = RecurringOrder::factory()->create();
        $order = Order::factory()->create(['blueprint_id' => $subscription->id, 'pickup_date' => today()->addDay()]);

        $product = Product::factory()->create(['unit_price' => '10.00']);
        $order_item = OrderItem::factory()->create([
            'order_id' => $order->id,
            'product_id' => $product->id,
            'type' => 'promo',
            'qty' => 2
        ]);

        (new SyncItemToSubscription)->handle($order_item);

        $this->assertDatabaseHas(RecurringOrderItem::class, [
            'order_id' => $subscription->id,
            'product_id' => $order_item->product_id,
            'type' => 'promo',
            'qty' => 1,
        ]);

        Carbon::setTestNow();
    }

    #[Test]
    public function it_does_not_add_zero_quantity_promo_order_item(): void
    {
        Carbon::setTestNow(now());

        $subscription = RecurringOrder::factory()->create();
        $order = Order::factory()->create(['blueprint_id' => $subscription->id, 'pickup_date' => today()->addDay()]);

        $product = Product::factory()->create(['unit_price' => '10.00']);
        $order_item = OrderItem::factory()->make([
            'order_id' => $order->id,
            'product_id' => $product->id,
            'type' => 'promo',
            'qty' => 0
        ]);

        (new SyncItemToSubscription)->handle($order_item);

        $this->assertDatabaseMissing(RecurringOrderItem::class, [
            'order_id' => $subscription->id,
            'product_id' => $order_item->product_id,
            'type' => 'promo',
        ]);

        Carbon::setTestNow();
    }

    #[Test]
    public function it_replaces_existing_promo_order_item_when_product_already_on_subscription(): void
    {
        Carbon::setTestNow(now());

        $subscription = RecurringOrder::factory()->create();
        $order = Order::factory()->create(['blueprint_id' => $subscription->id, 'pickup_date' => today()->addDay()]);
        $product = Product::factory()->create(['unit_price' => '10.00']);
        $order_item = OrderItem::factory()->create([
            'order_id' => $order->id,
            'product_id' => $product->id,
            'type' => 'promo',
            'qty' => 3,
            'unit_price' => 100
        ]);

        $subscription_item = RecurringOrderItem::factory()->create([
            'order_id' => $subscription->id,
            'product_id' => $product->id,
            'type' => 'promo',
            'qty' => 2
        ]);

        (new SyncItemToSubscription)->handle($order_item);

        $this->assertDatabaseHas(RecurringOrderItem::class, [
            'order_id' => $subscription->id,
            'product_id' => $subscription_item->product_id,
            'type' => 'promo',
            'qty' => 1,
        ]);

        $this->assertDatabaseMissing(RecurringOrderItem::class, [
            'id' => $subscription_item->id,
        ]);

        Carbon::setTestNow();
    }

    #[Test]
    public function it_adds_new_promo_order_item_when_product_already_on_subscription_but_type_doesnt_match(): void
    {
        Carbon::setTestNow(now());

        $subscription = RecurringOrder::factory()->create();
        $order = Order::factory()->create(['blueprint_id' => $subscription->id, 'pickup_date' => today()->addDay()]);
        $product = Product::factory()->create(['unit_price' => '10.00']);
        $subscription_item = RecurringOrderItem::factory()->create([
            'order_id' => $subscription->id,
            'product_id' => $product->id,
            'type' => 'addon',
            'qty' => 3,
        ]);

        $order_item = OrderItem::factory()->create([
            'order_id' => $order->id,
            'product_id' => $product->id,
            'type' => 'promo',
            'qty' => 2,
            'unit_price' => 100
        ]);

        (new SyncItemToSubscription)->handle($order_item);

        $this->assertDatabaseHas(RecurringOrderItem::class, [
            'order_id' => $subscription->id,
            'product_id' => $subscription_item->product_id,
            'type' => 'promo',
            'qty' => 1,
        ]);

        $this->assertDatabaseHas(RecurringOrderItem::class, [
            'id' => $subscription_item->id,
            'product_id' => $subscription_item->product_id,
            'type' => 'addon',
            'qty' => 3,
        ]);

        Carbon::setTestNow();
    }
}