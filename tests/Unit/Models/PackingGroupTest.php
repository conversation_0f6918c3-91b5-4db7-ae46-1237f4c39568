<?php

namespace Tests\Unit\Models;

use App\Models\PackingGroup;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class PackingGroupTest extends TestCase
{
    use RefreshDatabase;

    public function test_dry_returns_correct_id(): void
    {
        $this->assertEquals(2, PackingGroup::dry());
    }

    public function test_frozen_returns_correct_id(): void
    {
        $this->assertEquals(1, PackingGroup::frozen());
    }

    public function test_is_fresh_returns_correct_id(): void
    {
        $this->assertEquals(3, PackingGroup::isFresh());
    }

    public function test_seasonal_returns_correct_id(): void
    {
        $this->assertEquals(4, PackingGroup::seasonal());
    }

    public function test_merchandise_returns_correct_id(): void
    {
        $this->assertEquals(5, PackingGroup::merchandise());
    }

    public function test_digital_returns_correct_id(): void
    {
        $this->assertEquals(6, PackingGroup::digital());
    }

    public function test_it_can_be_created_with_title(): void
    {
        PackingGroup::create([
            'title' => 'Test Group'
        ]);

        $this->assertDatabaseHas('packing_groups', [
            'title' => 'Test Group'
        ]);
    }
} 