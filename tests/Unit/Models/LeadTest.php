<?php

namespace Tests\Unit\Models;

use App\Models\Lead;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class LeadTest extends TestCase
{
    #[Test]
    public function it_casts_lat_to_decimal_with_precision(): void
    {
        $lead = Lead::factory()->make(['lat' => '11.23456789']);
        $this->assertEquals(11.234568, $lead->lat);
    }

    #[Test]
    public function it_casts_lng_to_decimal_with_precision(): void
    {
        $lead = Lead::factory()->make(['lng' => '12.23456789']);
        $this->assertEquals(12.234568, $lead->lng);
    }

    #[Test]
    public function it_can_use_custom_fields_as_settings(): void
    {
        $lead = Lead::factory()->make(['custom_fields' => ['foo' => 'bar']]);
        $this->assertEquals('bar', $lead->custom_fields->foo);

        $lead->custom_fields = ['foo' => 'baz'];
        $this->assertEquals('baz', $lead->custom_fields->foo);

        $lead->custom_fields = ['bar' => 'fizz'];
        $this->assertEquals('baz', $lead->custom_fields->foo);
        $this->assertEquals('fizz', $lead->custom_fields->bar);
    }
}