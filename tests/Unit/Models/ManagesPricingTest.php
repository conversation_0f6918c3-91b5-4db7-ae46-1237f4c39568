<?php

namespace Tests\Unit\Models;

use App\Models\Price;
use App\Models\Product;
use App\Models\ProductPrice;
use App\Models\ProductPriceGroup;
use App\Support\Enums\ProductType;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class ManagesPricingTest extends TenantTestCase
{
    #[Test]
    public function it_gets_price_from_prices_table_when_available()
    {
        $product = Product::factory()->create([
            'unit_price' => 1000,
            'sale_unit_price' => 800,
            'sale' => false
        ]);

        Price::factory()->create([
            'product_id' => $product->id,
            'quantity' => 2,
            'unit_price' => 1500,
            'sale_unit_price' => 1200
        ]);

        $this->assertEquals(1500, $product->getUnitPrice(2));

        $product->sale = true;
        $product->save();

        $this->assertEquals(1200, $product->getUnitPrice(2));
    }

    #[Test]
    public function it_falls_back_to_product_price_when_no_price_record_exists()
    {
        $product = Product::factory()->create([
            'unit_price' => 1000,
            'sale_unit_price' => 800,
            'sale' => false
        ]);

        $this->assertEquals(100000, $product->getUnitPrice(2));

        $product->sale = true;
        $product->save();

        $this->assertEquals(80000, $product->getUnitPrice(2));
    }

    #[Test]
    public function it_gets_price_based_on_quantity_tiers()
    {
        $product = Product::factory()->create([
            'unit_price' => 1000,
            'sale_unit_price' => 800,
            'sale' => false
        ]);

        Price::factory()->create([
            'product_id' => $product->id,
            'quantity' => 2,
            'unit_price' => 1500,
            'sale_unit_price' => 1200
        ]);

        Price::factory()->create([
            'product_id' => $product->id,
            'quantity' => 5,
            'unit_price' => 1400,
            'sale_unit_price' => 1100
        ]);

        Price::factory()->create([
            'product_id' => $product->id,
            'quantity' => 10,
            'unit_price' => 1300,
            'sale_unit_price' => 1000
        ]);

        // Test quantity 1 (exact match)
        $this->assertEquals(1500, $product->getUnitPrice(2));

        // Test quantity 3 (should use tier 1)
        $this->assertEquals(1500, $product->getUnitPrice(3));

        // Test quantity 5 (exact match)
        $this->assertEquals(1400, $product->getUnitPrice(5));

        // Test quantity 7 (should use tier 5)
        $this->assertEquals(1400, $product->getUnitPrice(7));

        // Test quantity 10 (exact match)
        $this->assertEquals(1300, $product->getUnitPrice(10));

        // Test quantity 15 (should use tier 10)
        $this->assertEquals(1300, $product->getUnitPrice(15));

        $product->sale = true;
        $product->save();

        $this->assertEquals(1200, $product->getUnitPrice(2));
        $this->assertEquals(1200, $product->getUnitPrice(3));
        $this->assertEquals(1100, $product->getUnitPrice(5));
        $this->assertEquals(1100, $product->getUnitPrice(7));
        $this->assertEquals(1000, $product->getUnitPrice(10));
        $this->assertEquals(1000, $product->getUnitPrice(15));
    }

    #[Test]
    public function it_calculates_regular_price_correctly()
    {
        $product = Product::factory()->create([
            'unit_price' => 1000,
            'sale_unit_price' => 800,
            'sale' => true,
            'unit_of_issue' => 'package'
        ]);

        Price::factory()->create([
            'product_id' => $product->id,
            'quantity' => 2,
            'unit_price' => 1500,
            'sale_unit_price' => 1200
        ]);

        Price::factory()->create([
            'product_id' => $product->id,
            'quantity' => 5,
            'unit_price' => 1400,
            'sale_unit_price' => 1100
        ]);

        $this->assertEquals(1500, $product->getRegularUnitPrice(2));
        $this->assertEquals(1500, $product->getRegularUnitPrice(3));
        $this->assertEquals(1400, $product->getRegularUnitPrice(5));
        $this->assertEquals(1400, $product->getRegularUnitPrice(7));

        $this->assertEquals(1500, $product->getRegularPrice(2));
        $this->assertEquals(1500, $product->getRegularPrice(3));
        $this->assertEquals(1400, $product->getRegularPrice(5));
        $this->assertEquals(1400, $product->getRegularPrice(7));

        $product->unit_of_issue = 'weight';
        $product->weight = 2.5;
        $product->save();

        $this->assertEquals(1500 * 2.5, $product->getRegularPrice(2));
        $this->assertEquals(1500 * 2.5, $product->getRegularPrice(3));
        $this->assertEquals(1400 * 2.5, $product->getRegularPrice(5));
        $this->assertEquals(1400 * 2.5, $product->getRegularPrice(7));
    }

    #[Test]
    public function it_calculates_sale_price_correctly()
    {
        $product = Product::factory()->create([
            'unit_price' => 1000,
            'sale_unit_price' => 800,
            'sale' => true,
            'unit_of_issue' => 'package'
        ]);

        Price::factory()->create([
            'product_id' => $product->id,
            'quantity' => 2,
            'unit_price' => 1500,
            'sale_unit_price' => 1200
        ]);

        Price::factory()->create([
            'product_id' => $product->id,
            'quantity' => 5,
            'unit_price' => 1400,
            'sale_unit_price' => 1100
        ]);

        $this->assertEquals(1200, $product->getPrice(2));
        $this->assertEquals(1200, $product->getPrice(3));
        $this->assertEquals(1100, $product->getPrice(5));
        $this->assertEquals(1100, $product->getPrice(7));

        $product->unit_of_issue = 'weight';
        $product->weight = 2.5;
        $product->save();

        $this->assertEquals(1200 * 2.5, $product->getPrice(2));
        $this->assertEquals(1200 * 2.5, $product->getPrice(3));
        $this->assertEquals(1100 * 2.5, $product->getPrice(5));
        $this->assertEquals(1100 * 2.5, $product->getPrice(7));
    }

    #[Test]
    public function it_calculates_savings_correctly()
    {
        $product = Product::factory()->create([
            'unit_price' => 1000,
            'sale_unit_price' => 800,
            'sale' => true,
            'unit_of_issue' => 'package'
        ]);

        Price::factory()->create([
            'product_id' => $product->id,
            'quantity' => 2,
            'unit_price' => 1500,
            'sale_unit_price' => 1200
        ]);

        $this->assertEquals(300, $product->getUnitSavings(2));

        $this->assertEquals(300, $product->getSavings(2));

        $product->unit_of_issue = 'weight';
        $product->weight = 2.5;
        $product->save();

        $this->assertEquals(300 * 2.5, $product->getSavings(2));
    }

    #[Test]
    public function it_handles_gift_card_pricing_correctly()
    {
        $product = Product::factory()->create([
            'unit_price' => 1000,
            'sale_unit_price' => 800,
            'sale' => false,
            'type_id' => ProductType::GIFT_CARD->value
        ]);

        $this->assertEquals(100000, $product->getUnitPrice());

        $product->sale = true;
        $product->save();

        $this->assertEquals(80000, $product->getUnitPrice());
    }

    #[Test]
    public function it_applies_percentage_increase_to_price()
    {
        $product = Product::factory()->create([
            'unit_price' => 1000,
            'sale_unit_price' => 800,
            'sale' => false
        ]);

        $priceGroup = ProductPriceGroup::factory()->create([
            'type' => ProductPriceGroup::PERCENTAGE_INCREASE,
            'amount' => 10 // 10% increase
        ]);

        ProductPrice::factory()->create([
            'product_id' => $product->id,
            'group_id' => $priceGroup->id,
            'unit_price' => 1000,
            'sale_unit_price' => 800
        ]);

        $this->assertEquals(110000, $product->getUnitPrice()); // 1000 + 10%

        $product->sale = true;
        $product->save();

        $this->assertEquals(88000, $product->getUnitPrice()); // 800 + 10%
    }

    #[Test]
    public function it_applies_percentage_decrease_to_price()
    {
        $product = Product::factory()->create([
            'unit_price' => 1000,
            'sale_unit_price' => 800,
            'sale' => false
        ]);

        $priceGroup = ProductPriceGroup::factory()->create([
            'type' => ProductPriceGroup::PERCENTAGE_DECREASE,
            'amount' => 10 // 10% decrease
        ]);

        ProductPrice::factory()->create([
            'product_id' => $product->id,
            'group_id' => $priceGroup->id,
            'unit_price' => 1000,
            'sale_unit_price' => 800
        ]);

        $this->assertEquals(90000, $product->getUnitPrice()); // 1000 - 10%

        $product->sale = true;
        $product->save();

        $this->assertEquals(72000, $product->getUnitPrice()); // 800 - 10%
    }
}