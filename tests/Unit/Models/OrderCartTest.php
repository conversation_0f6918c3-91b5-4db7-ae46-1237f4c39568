<?php

namespace Tests\Unit\Models;

use App\Cart\Item;
use App\Cart\Subscription;
use App\Contracts\Cartable;
use App\Models\Card;
use App\Models\Cart;
use App\Models\Coupon;
use App\Models\Date;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Payment;
use App\Models\Pickup;
use App\Models\PickupFee;
use App\Models\Product;
use App\Models\Schedule;
use App\Models\User;
use App\Services\SubscriptionSettingsService;
use Illuminate\Support\Collection;
use Mockery\MockInterface;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class OrderCartTest extends TenantTestCase
{
    #[Test]
    public function it_can_get_its_cart_id(): void
    {
        $order = Order::factory()->create();
        $this->assertEquals($order->id, $order->cartId());
    }

    #[Test]
    public function it_can_determine_its_purchase_type(): void
    {
        $order = Order::factory()->make(['is_recurring' => null]);
        $this->assertEquals(Cartable::ONE_TIME_PURCHASE, $order->purchaseType());

        $order = Order::factory()->make(['is_recurring' => true]);
        $this->assertEquals(Cartable::SUBSCRIPTION_PURCHASE, $order->purchaseType());
    }

    #[Test]
    public function it_can_determine_if_cart_is_empty(): void
    {
        $order = Order::factory()->create();
        $this->assertTrue($order->cartIsEmpty());

        OrderItem::factory()->for($order)->create(['type' => 'promo']);

        $this->assertTrue($order->cartIsEmpty());

        OrderItem::factory()->for($order)->create(['type' => 'standard']);

        $this->assertFalse($order->cartIsEmpty());
    }

    #[Test]
    public function it_can_get_its_cart_customer(): void
    {
        $user = User::factory()->create();
        $order = Order::factory()->make(['customer_id' => $user->id]);

        $this->assertEquals($user->id, $order->cartCustomer()->id);
    }

    #[Test]
    public function it_can_get_its_cart_location(): void
    {
        $order = Order::factory()->make(['pickup_id' => 123123123123]);
        $this->assertNull($order->cartLocation());

        $pickup = Pickup::factory()->create();
        $order = Order::factory()->make(['pickup_id' => $pickup->id]);
        $this->assertEquals($pickup->id, $order->cartLocation()->id);
    }

    #[Test]
    public function it_can_get_its_cart_date(): void
    {
        $order = Order::factory()->make(['date_id' => 12312312312]);
        $this->assertNull($order->cartDate());

        $schedule = Schedule::factory()->create();
        $date = Date::factory()->create(['schedule_id' => $schedule->id]);
        $order = Order::factory()->make(['date_id' => $date->id]);
        $this->assertEquals($date->id, $order->cartDate()->id);
    }

    #[Test]
    public function it_can_get_its_items_in_the_cart(): void
    {
        $order = Order::factory()->create();
        $this->assertEquals(0, $order->itemsInCart()->count());

        OrderItem::factory()->for($order)->create(['type' => 'promo']);

        $this->assertEquals(0, $order->itemsInCart()->count());

        OrderItem::factory()->for($order)->create(['type' => 'standard']);
        OrderItem::factory()->for($order)->create(['type' => 'standard']);

        $this->assertEquals(2, $order->itemsInCart()->count());

        foreach ($order->itemsInCart() as $cart_item) {
            $this->assertInstanceOf(Item::class, $cart_item);
        }
    }

    #[Test]
    public function it_can_get_its_cart_subscription(): void
    {
        $this->mock(SubscriptionSettingsService::class, function (MockInterface $mock) {
            $mock->shouldNotReceive('defaultProductIncentiveId');
        });

        $order = Order::factory()->make(['is_recurring' => null]);
        $this->assertNull($order->cartSubscription());

        $order = Order::factory()->create(['is_recurring' => true]);
        $item = OrderItem::factory()->for($order)->create(['type' => 'promo']);
        session(['subscription_frequency' => 7]);

        $subscription = $order->cartSubscription();
        $this->assertInstanceOf(Subscription::class, $subscription);
        $this->assertEquals(7, $subscription->frequency);
        $this->assertEquals($item->product_id, $subscription->product_incentive_id);

        $this->mock(SubscriptionSettingsService::class, function (MockInterface $mock) {
            $mock->shouldReceive('defaultProductIncentiveId')->andReturn(1234);
        });

        $schedule = Schedule::factory()->create(['type_id' => 2, 'reorder_frequency' => ['14']]);
        $pickup = Pickup::factory()->create(['schedule_id' => $schedule->id]);
        $order = Order::factory()->create(['is_recurring' => true, 'pickup_id' => $pickup->id]);
        session(['subscription_frequency' => null]);

        $subscription = $order->cartSubscription();
        $this->assertInstanceOf(Subscription::class, $subscription);
        $this->assertEquals(14, $subscription->frequency);
        $this->assertEquals(1234, $subscription->product_incentive_id);
    }

    #[Test]
    public function it_can_add_a_cart_item_to_cart(): void
    {
        $order = Order::factory()->create();
        $product = Product::factory()->create(['unit_price' => '22.33']); // mutator sets to cents

        $order->addItemToCart(new Item('some-id', $product, 2));

        $this->assertDatabaseHas(OrderItem::class, [
            'order_id' => $order->id,
            'product_id' => $product->id,
            'unit_price' => 2233,
            'qty' => 2
        ]);
    }

    #[Test]
    public function it_can_update_a_cart_items_quantity(): void
    {
        $order = Order::factory()->create();
        $item = OrderItem::factory()->for($order)->create(['qty' => 2]);

        $order->updateCartItemQuantity($item->id , 3);

        $this->assertDatabaseHas(OrderItem::class, [
            'id' => $item->id,
            'qty' => 3
        ]);
    }

    #[Test]
    public function it_handles_attempting_to_update_quantity_for_an_item_that_does_not_exist(): void
    {
        $order = Order::factory()->create();

        $result = $order->updateCartItemQuantity(123123123312, 3);

        $this->assertInstanceOf(Order::class, $result);

    }

    #[Test]
    public function it_can_remove_a_cart_item(): void
    {
        $order = Order::factory()->create();
        $item = OrderItem::factory()->for($order)->create(['qty' => 2]);

        $order->removeCartItem($item->id);

        $this->assertDatabaseMissing(OrderItem::class, [
            'id' => $item->id,
        ]);
    }

    #[Test]
    public function it_handles_attempting_to_remove_an_item_that_does_not_exist(): void
    {
        $order = Order::factory()->create();

        $result = $order->removeCartItem(312312312);

        $this->assertInstanceOf(Order::class, $result);
    }

    #[Test]
    public function it_can_update_the_cart_location(): void
    {
        $old_pickup = Pickup::factory()->create();
        $order = Order::factory()->create(['pickup_id' => $old_pickup->id]);

        $new_pickup = Pickup::factory()->create();

        $order->updateCartLocation($new_pickup);

        $this->assertDatabaseHas(Order::class, [
            'id' => $order->id,
            'pickup_id' => $new_pickup->id
        ]);
    }

    #[Test]
    public function it_can_update_the_cart_date_with_a_valid_date(): void
    {
        $old_date = Date::factory()->create(['schedule_id' => Schedule::factory()]);
        $order = Order::factory()->create(['date_id' => $old_date->id, 'deadline_date' => today()->subDays(2), 'pickup_date' => today()->subDays(2)]);

        $new_date = Date::factory()->create(['schedule_id' => Schedule::factory(), 'order_end_date' => today()->addDays(2), 'pickup_date' => today()->addDays(3)]);

        $order->updateCartDate($new_date);

        $this->assertDatabaseHas(Order::class, [
            'id' => $order->id,
            'date_id' => $new_date->id,
            'deadline_date' => $new_date->order_end_date->format('Y-m-d'),
            'pickup_date' => $new_date->pickup_date->format('Y-m-d'),
            'original_pickup_date' => $new_date->pickup_date->format('Y-m-d'),
        ]);
    }

    #[Test]
    public function it_can_update_the_cart_date_with_null(): void
    {
        $old_date = Date::factory()->create(['schedule_id' => Schedule::factory()]);
        $order = Order::factory()->create(['date_id' => $old_date->id, 'deadline_date' => today()->subDays(2), 'pickup_date' => today()->subDays(2)]);

        $order->updateCartDate(null);

        $this->assertDatabaseHas(Order::class, [
            'id' => $order->id,
            'date_id' => null,
            'deadline_date' => null,
            'pickup_date' => null,
            'original_pickup_date' => null,
        ]);
    }

    #[Test]
    public function it_gets_the_cart_as_a_one_time_purchase(): void
    {
        $order = Order::factory()->create(['is_recurring' => true]);
        $item = OrderItem::factory()->for($order)->create(['type' => 'promo']);

        session(['subscription_frequency' => 1]);

        $order->setCartAsOneTimePurchase();

        $this->assertDatabaseHas(Order::class, [
            'id' => $order->id,
            'is_recurring' => null
        ]);

        $this->assertDatabaseMissing(OrderItem::class, [
            'id' => $item->id,
        ]);

        $this->assertNull(session('subscription_frequency'));
    }

    #[Test]
    public function it_gets_the_cart_as_a_subscription_purchase(): void
    {
        $schedule = Schedule::factory()->create(['type_id' => Schedule::TYPE_REPEATING, 'reorder_frequency' => [7,14,30]]);
        $pickup = Pickup::factory()->create(['schedule_id' => $schedule->id]);
        $order = Order::factory()->create(['is_recurring' => false, 'pickup_id' => $pickup->id]);
        $product = Product::factory()->create();

        session(['subscription_frequency' => null]);

        $this->mock(SubscriptionSettingsService::class, function (MockInterface $mock) {
            $mock->shouldReceive('discountIncentive')->andReturn(5);
            $mock->shouldReceive('excludedProductIds')->andReturn(collect());
        });

        $order->setCartAsSubscriptionPurchase(7, $product->id);

        $this->assertDatabaseHas(Order::class, [
            'id' => $order->id,
            'is_recurring' => true
        ]);

        $this->assertDatabaseHas(OrderItem::class, [
            'order_id' => $order->id,
            'type' => 'promo',
        ]);

        $this->assertEquals(7, session('subscription_frequency'));
    }

    #[Test]
    public function it_determine_if_its_recurring(): void
    {
        $order = Order::factory()->make(['is_recurring' => null]);
        $this->assertFalse($order->isRecurring());

        $order = Order::factory()->make(['is_recurring' => true]);
        $this->assertTrue($order->isRecurring());
    }

    #[Test]
    public function it_can_apply_a_coupon_to_the_cart(): void
    {
        $coupon = Coupon::factory()->create();

        $order = Order::factory()->create();

        $order->applyCouponToCart($coupon);

        $this->assertTrue($order->discounts()->where('coupons.id', $coupon->id)->exists());
    }

    #[Test]
    public function it_gets_the_cart_subtotal(): void
    {
        $order = Order::factory()->create();

        OrderItem::factory()->for($order)->create(['type' => 'promo', 'subtotal' => 1111]);
        OrderItem::factory()->for($order)->create(['type' => 'promo', 'subtotal' => 2222]);

        $this->assertEquals(3333, $order->cartSubtotal());
    }

    #[Test]
    public function it_gets_the_cart_weight(): void
    {
        $order = Order::factory()->create();

        OrderItem::factory()->for($order)->create(['weight' => 1.111]);
        OrderItem::factory()->for($order)->create(['weight' => 2.222]);

        $this->assertEquals(3.333, $order->cartWeight());
    }

    #[Test]
    public function it_gets_the_cart_coupon_total(): void
    {
        $order = Order::factory()->create();

        $coupon = Coupon::factory()->create();
        $order->discounts()->attach($coupon->id, ['user_id' => $order->customer_id, 'savings' => 1234]);
        $order->discounts()->attach($coupon->id, ['user_id' => $order->customer_id, 'savings' => 2234]);

        $this->assertEquals(3468, $order->cartCouponTotal());
    }

    #[Test]
    public function it_gets_the_cart_store_credit_total(): void
    {
        $user = User::factory()->create(['credit' => 1234]);
        $order = Order::factory()->create(['customer_id' => $user->id]);

        $this->assertEquals(1234, $order->cartStoreCreditTotal());
    }

    #[Test]
    public function it_gets_the_cart_location_fee_total_for_non_fee_exempt_customers(): void
    {
        $user = User::factory()->create(['exempt_from_fees' => false]);

        $pickup = Pickup::factory()->create();
        PickupFee::factory()->for($pickup)->create(['amount' => '11.22']);
        PickupFee::factory()->for($pickup)->create(['amount' => '22.44']);
        $order = Order::factory()->create(['customer_id' => $user->id, 'pickup_id' => $pickup->id]);

        $this->assertEquals(3366, $order->cartLocationFeeTotal());
    }

    #[Test]
    public function it_gets_the_cart_location_fee_total_for_fee_exempt_customers(): void
    {
        $user = User::factory()->create(['exempt_from_fees' => true]);

        $pickup = Pickup::factory()->create();
        PickupFee::factory()->for($pickup)->create(['amount' => '11.22']);
        PickupFee::factory()->for($pickup)->create(['amount' => '22.44']);
        $order = Order::factory()->create(['customer_id' => $user->id, 'pickup_id' => $pickup->id]);

        $this->assertEquals(0, $order->cartLocationFeeTotal());
    }

    #[Test]
    public function it_gets_the_cart_subscription_savings_total(): void
    {
        $order = Order::factory()->create(['is_recurring' => true]);

        OrderItem::factory()->for($order)->create(['subtotal' => 1111]);
        OrderItem::factory()->for($order)->create(['subtotal' => 2222]);

        $this->mock(SubscriptionSettingsService::class, function (MockInterface $mock) {
            $mock->shouldReceive('discountIncentive')->andReturn(10);
        });

        $this->assertEquals(333, $order->cartSubscriptionSavingsTotal());
    }

    #[Test]
    public function it_gets_the_cart_tax_total_when_customer_is_not_exempt_from_taxes(): void
    {
        $pickup = Pickup::factory()->create(['tax_rate' => 0.10, 'tax_delivery_fee' => true, 'delivery_rate' => '14.55', 'settings' => ['delivery_fee_type' => Pickup::DELIVERY_FEE_TYPE_FLAT]]);
        PickupFee::factory()->for($pickup)->create(['amount' => '11.22', 'taxable' => true]);
        PickupFee::factory()->for($pickup)->create(['amount' => '22.44', 'taxable' => false]);

        $user = User::factory()->create(['settings' => ['exempt_from_tax' => false]]);
        $order = Order::factory()->create(['customer_id' => $user->id, 'pickup_id' => $pickup->id]);

        $product_one = Product::factory()->create(['taxable' => true]);
        OrderItem::factory()->for($order)->create(['product_id' => $product_one->id, 'subtotal' => 1111]);
        $product_two = Product::factory()->create(['taxable' => false]);
        OrderItem::factory()->for($order)->create(['product_id' => $product_two->id, 'subtotal' => 2222]);

        $product_tax = 111;
        $fee_tax = 112;
        $delivery_tax = 146;

        $this->assertEquals($delivery_tax + $product_tax + $fee_tax, $order->cartTaxTotal());
    }

    #[Test]
    public function it_gets_the_cart_tax_total_when_customer_is_exempt_from_taxes(): void
    {
        $pickup = Pickup::factory()->create(['tax_rate' => 0.10, 'tax_delivery_fee' => true, 'delivery_rate' => '14.55', 'settings' => ['delivery_fee_type' => Pickup::DELIVERY_FEE_TYPE_FLAT]]);
        PickupFee::factory()->for($pickup)->create(['amount' => '11.22', 'taxable' => true]);
        PickupFee::factory()->for($pickup)->create(['amount' => '22.44', 'taxable' => false]);

        $user = User::factory()->create(['settings' => ['exempt_from_tax' => true]]);
        $order = Order::factory()->create(['customer_id' => $user->id, 'pickup_id' => $pickup->id]);

        $product_one = Product::factory()->create(['taxable' => true]);
        OrderItem::factory()->for($order)->create(['product_id' => $product_one->id, 'subtotal' => 1111]);
        $product_two = Product::factory()->create(['taxable' => false]);
        OrderItem::factory()->for($order)->create(['product_id' => $product_two->id, 'subtotal' => 2222]);

        $this->assertEquals(0, $order->cartTaxTotal());
    }

    #[Test]
    public function it_gets_the_flat_cart_delivery_total_when_customer_is_not_exempt_from_fees(): void
    {
        $pickup = Pickup::factory()->create(['delivery_rate' => '14.55', 'settings' => ['delivery_fee_type' => Pickup::DELIVERY_FEE_TYPE_FLAT]]);

        $user = User::factory()->create(['exempt_from_fees' => false]);
        $order = Order::factory()->create(['customer_id' => $user->id, 'pickup_id' => $pickup->id, 'weight' => 123]);

        $this->assertEquals(1455, $order->cartDeliveryTotal());
    }

    #[Test]
    public function it_gets_the_weighted_cart_delivery_total_when_customer_is_not_exempt_from_fees(): void
    {
        $pickup = Pickup::factory()->create(['delivery_rate' => '2.00', 'settings' => ['delivery_fee_type' => Pickup::DELIVERY_FEE_TYPE_BY_WEIGHT]]);

        $user = User::factory()->create(['exempt_from_fees' => false]);
        $order = Order::factory()->create(['customer_id' => $user->id, 'pickup_id' => $pickup->id]);
        OrderItem::factory()->for($order)->create(['weight' => 1.111]);
        OrderItem::factory()->for($order)->create(['weight' => 2.222]);

        // 3.333 * 2.00
        $this->assertEquals(667, $order->cartDeliveryTotal());
    }

    #[Test]
    public function it_gets_the_capped_cart_delivery_total(): void
    {
        $pickup = Pickup::factory()->create([
            'delivery_rate' => '2.00',
            'apply_limit' => true,
            'delivery_fee_cap' => '40.00',
            'delivery_total_threshold' => 0,
            'settings' => ['delivery_fee_type' => Pickup::DELIVERY_FEE_TYPE_BY_WEIGHT]
        ]);

        $user = User::factory()->create(['exempt_from_fees' => false]);
        $order = Order::factory()->create(['customer_id' => $user->id, 'pickup_id' => $pickup->id, 'weight' => 21.22]);

        $this->assertEquals(4000, $order->cartDeliveryTotal());
    }

    #[Test]
    public function it_gets_the_uncapped_cart_delivery_total(): void
    {
        $pickup = Pickup::factory()->create([
            'delivery_rate' => '2.00',
            'apply_limit' => true,
            'delivery_fee_cap' => '43.45',
            'delivery_total_threshold' => 123,
            'settings' => ['delivery_fee_type' => Pickup::DELIVERY_FEE_TYPE_BY_WEIGHT]
        ]);

        $user = User::factory()->create(['exempt_from_fees' => false]);
        $order = Order::factory()->create(['customer_id' => $user->id, 'pickup_id' => $pickup->id]);
        OrderItem::factory()->for($order)->create(['weight' => 1.111, 'subtotal' => 5555]);

        $this->assertEquals(222, $order->cartDeliveryTotal());
    }

    #[Test]
    public function it_can_get_the_total_before_store_credit_for_a_one_time_purchase(): void
    {
        $pickup = Pickup::factory()->create(['tax_rate' => 0.10, 'tax_delivery_fee' => true, 'delivery_rate' => '14.55', 'settings' => ['delivery_fee_type' => Pickup::DELIVERY_FEE_TYPE_FLAT]]);
        PickupFee::factory()->for($pickup)->create(['amount' => '11.22', 'taxable' => true]);
        PickupFee::factory()->for($pickup)->create(['amount' => '22.44', 'taxable' => false]);

        $user = User::factory()->create(['credit' => 444]);
        $order = Order::factory()->create(['customer_id' => $user->id, 'pickup_id' => $pickup->id, 'is_recurring' => null]);

        $coupon = Coupon::factory()->create();
        $order->discounts()->attach($coupon->id, ['user_id' => $order->customer_id, 'savings' => 123]);

        $product_one = Product::factory()->create(['taxable' => true]);
        OrderItem::factory()->for($order)->create(['product_id' => $product_one->id, 'subtotal' => 1111]);
        $product_two = Product::factory()->create(['taxable' => false]);
        OrderItem::factory()->for($order)->create(['product_id' => $product_two->id, 'subtotal' => 2222]);

        $item_subtotal = 3333;
        $cart_location_total = 3366;
        $cart_delivery_total = 1455;

        $product_tax = 111;
        $fee_tax = 112;
        $delivery_tax = 146;
        $cart_tax_total = $delivery_tax + $product_tax + $fee_tax;

        $subscription_savings_total = 0;
        $cart_coupon_total = 123;

        $this->assertEquals(
            $item_subtotal + $cart_location_total + $cart_delivery_total + $cart_tax_total - $subscription_savings_total - $cart_coupon_total,
            $order->cartTotalBeforeStoreCredit()
        );
    }

    #[Test]
    public function it_can_get_the_total_before_store_credit_for_a_recurring_purchase(): void
    {
        $this->mock(SubscriptionSettingsService::class, function (MockInterface $mock) {
            $mock->shouldReceive('discountIncentive')->andReturn(10);
        });

        $pickup = Pickup::factory()->create(['tax_rate' => 0.10, 'tax_delivery_fee' => true, 'delivery_rate' => '14.55', 'settings' => ['delivery_fee_type' => Pickup::DELIVERY_FEE_TYPE_FLAT]]);
        PickupFee::factory()->for($pickup)->create(['amount' => '11.22', 'taxable' => true]);
        PickupFee::factory()->for($pickup)->create(['amount' => '22.44', 'taxable' => false]);

        $user = User::factory()->create(['credit' => 444]);
        $order = Order::factory()->create(['customer_id' => $user->id, 'pickup_id' => $pickup->id, 'is_recurring' => true]);

        $coupon = Coupon::factory()->create();
        $order->discounts()->attach($coupon->id, ['user_id' => $order->customer_id, 'savings' => 123]);

        $product_one = Product::factory()->create(['taxable' => true]);
        OrderItem::factory()->for($order)->create(['product_id' => $product_one->id, 'subtotal' => 1111]);
        $product_two = Product::factory()->create(['taxable' => false]);
        OrderItem::factory()->for($order)->create(['product_id' => $product_two->id, 'subtotal' => 2222]);

        $item_subtotal = 3333;
        $cart_location_total = 3366;
        $cart_delivery_total = 1455;

        $product_tax = 111;
        $fee_tax = 112;
        $delivery_tax = 146;
        $cart_tax_total = $delivery_tax + $product_tax + $fee_tax;

        $subscription_savings_total = 333;
        $cart_coupon_total = 123;

        $this->assertEquals(
            $item_subtotal + $cart_location_total + $cart_delivery_total + $cart_tax_total - $subscription_savings_total - $cart_coupon_total,
            $order->cartTotalBeforeStoreCredit()
        );
    }

    #[Test]
    public function it_can_get_the_total_after_store_credit(): void
    {
        $pickup = Pickup::factory()->create(['tax_rate' => 0.10, 'tax_delivery_fee' => true, 'delivery_rate' => '14.55', 'settings' => ['delivery_fee_type' => Pickup::DELIVERY_FEE_TYPE_FLAT]]);
        PickupFee::factory()->for($pickup)->create(['amount' => '11.22', 'taxable' => true]);
        PickupFee::factory()->for($pickup)->create(['amount' => '22.44', 'taxable' => false]);

        $user = User::factory()->create(['credit' => 444]);
        $order = Order::factory()->create(['customer_id' => $user->id, 'pickup_id' => $pickup->id, 'is_recurring' => null]);

        $coupon = Coupon::factory()->create();
        $order->discounts()->attach($coupon->id, ['user_id' => $order->customer_id, 'savings' => 123]);

        $product_one = Product::factory()->create(['taxable' => true]);
        OrderItem::factory()->for($order)->create(['product_id' => $product_one->id, 'subtotal' => 1111]);
        $product_two = Product::factory()->create(['taxable' => false]);
        OrderItem::factory()->for($order)->create(['product_id' => $product_two->id, 'subtotal' => 2222]);

        $item_subtotal = 3333;
        $cart_location_total = 3366;
        $cart_delivery_total = 1455;

        $product_tax = 111;
        $fee_tax = 112;
        $delivery_tax = 146;
        $cart_tax_total = $delivery_tax + $product_tax + $fee_tax;

        $subscription_savings_total = 0;
        $cart_coupon_total = 123;

        $this->assertEquals(
            $item_subtotal + $cart_location_total + $cart_delivery_total + $cart_tax_total - $subscription_savings_total - $cart_coupon_total - $user->credit,
            $order->cartTotal()
        );
    }

    #[Test]
    public function it_can_get_a_zeroed_out_total_when_store_credit_exceeds_total(): void
    {
        $pickup = Pickup::factory()->create(['tax_rate' => 0.10, 'tax_delivery_fee' => true, 'delivery_rate' => '14.55', 'settings' => ['delivery_fee_type' => Pickup::DELIVERY_FEE_TYPE_FLAT]]);
        PickupFee::factory()->for($pickup)->create(['amount' => '11.22', 'taxable' => true]);
        PickupFee::factory()->for($pickup)->create(['amount' => '22.44', 'taxable' => false]);

        $user = User::factory()->create(['credit' => 9999999]);
        $order = Order::factory()->create(['customer_id' => $user->id, 'pickup_id' => $pickup->id, 'is_recurring' => null]);

        $coupon = Coupon::factory()->create();
        $order->discounts()->attach($coupon->id, ['user_id' => $order->customer_id, 'savings' => 123]);

        $product_one = Product::factory()->create(['taxable' => true]);
        OrderItem::factory()->for($order)->create(['product_id' => $product_one->id, 'subtotal' => 1111]);
        $product_two = Product::factory()->create(['taxable' => false]);
        OrderItem::factory()->for($order)->create(['product_id' => $product_two->id, 'subtotal' => 2222]);

        $this->assertEquals(0, $order->cartTotal());
    }


    #[Test]
    public function it_can_add_a_product(): void
    {
        /** @var Order $order */
        $order = Order::factory()->create();

        $product = Product::factory()->create();

        $this->actingAs($order->customer);

        $order->addProduct($product, 2);

        $this->assertDatabaseHas(OrderItem::class, [
            'order_id' => $order->id,
            'product_id' => $product->id,
            'qty' => 2
        ]);
    }

    #[Test]
    public function it_can_set_its_contact_info(): void
    {
        /** @var Order $cart */
        $order = Order::factory()->create([
            'customer_first_name' => 'existing first',
            'customer_last_name' => 'existing last',
            'customer_email' => 'existing email',
            'customer_phone' => 'existing phone',
        ]);

        $order->setContactInfo([
            'first_name' => 'new first',
            'last_name' => 'new last',
            'email' => 'new email',
            'phone' => 'new phone',
            'save_for_later' => true,
            'opt_in_to_sms' => true,
            'subscribed_to_sms' => true
        ]);

        $this->assertEquals([
            'customer_first_name' => 'new first',
            'customer_last_name' => 'new last',
            'customer_email' => 'existing email',
            'customer_phone' => 'new phone',
        ], $order->only([
            'customer_first_name',
            'customer_last_name',
            'customer_email',
            'customer_phone',
        ]));

        $this->assertDatabaseHas(Order::class, [
            'customer_first_name' => 'new first',
            'customer_last_name' => 'new last',
            'customer_email' => 'existing email',
            'customer_phone' => 'new phone',
        ]);

        $order->setContactInfo([
            'first_name' => 'new again first',
        ]);

        $this->assertEquals([
            'customer_first_name' => 'new again first',
            'customer_last_name' => 'new last',
            'customer_email' => 'existing email',
            'customer_phone' => 'new phone',
        ], $order->only([
            'customer_first_name',
            'customer_last_name',
            'customer_email',
            'customer_phone',
        ]));

        $this->assertDatabaseHas(Order::class, [
            'customer_first_name' => 'new again first',
            'customer_last_name' => 'new last',
            'customer_email' => 'existing email',
            'customer_phone' => 'new phone',
        ]);
    }

    #[Test]
    public function it_can_set_its_shipping_info(): void
    {
        /** @var Order $cart */
        $order = Order::factory()->create([
            'shipping_street' => 'existing street',
            'shipping_street_2' => 'existing street 2',
            'shipping_city' => 'existing city',
            'shipping_state' => 'existing state',
            'shipping_zip' => 'existing zip',
        ]);

        $order->setShippingInfo([
            'street' => 'new street',
            'street_2' => 'new street 2',
            'city' => 'new city',
            'state' => 'new state',
            'zip' => 'new zip',
            'country' => true,
            'save_for_later' => true,
        ]);

        $this->assertEquals([
            'shipping_street' => 'new street',
            'shipping_street_2' => 'new street 2',
            'shipping_city' => 'new city',
            'shipping_state' => 'new state',
            'shipping_zip' => 'new zip',
        ], $order->only([
            'shipping_street',
            'shipping_street_2',
            'shipping_city',
            'shipping_state',
            'shipping_zip',
        ]));

        $this->assertDatabaseHas(Order::class, [
            'shipping_street' => 'new street',
            'shipping_street_2' => 'new street 2',
            'shipping_city' => 'new city',
            'shipping_state' => 'new state',
            'shipping_zip' => 'new zip',
        ]);

        $order->setShippingInfo([
            'street' => 'new again street',
        ]);

        $this->assertEquals([
            'shipping_street' => 'new again street',
            'shipping_street_2' => 'new street 2',
            'shipping_city' => 'new city',
            'shipping_state' => 'new state',
            'shipping_zip' => 'new zip',
        ], $order->only([
            'shipping_street',
            'shipping_street_2',
            'shipping_city',
            'shipping_state',
            'shipping_zip',
        ]));

        $this->assertDatabaseHas(Order::class, [
            'shipping_street' => 'new again street',
            'shipping_street_2' => 'new street 2',
            'shipping_city' => 'new city',
            'shipping_state' => 'new state',
            'shipping_zip' => 'new zip',
        ]);
    }

    #[Test]
    public function it_can_set_its_billing_info(): void
    {
        $existing_payment = Payment::factory()->create();
        $existing_card = Card::factory()->create();

        /** @var Order $cart */
        $order = Order::factory()->create([
            'payment_id' => $existing_payment->id,
            'payment_source_id' => $existing_card->id,
        ]);

        $new_payment = Payment::factory()->create();
        $new_card = Card::factory()->create();

        $order->setBillingInfo([
            'method' => $new_payment->key,
            'source_id' => $new_card->id,
            'save_for_later' => true,
        ]);

        $this->assertEquals([
            'payment_id' => $new_payment->id,
            'payment_source_id' => $new_card->id,
        ], $order->only([
            'payment_id',
            'payment_source_id',
        ]));

        $this->assertDatabaseHas(Order::class, [
            'payment_id' => $new_payment->id,
            'payment_source_id' => $new_card->id,
        ]);

        $new_again_payment = Payment::factory()->create();

        $order->setBillingInfo([
            'method' => $new_again_payment->key,
        ]);

        $this->assertEquals([
            'payment_id' => $new_again_payment->id,
            'payment_source_id' => $new_card->id,
        ], $order->only([
            'payment_id',
            'payment_source_id',
        ]));

        $this->assertDatabaseHas(Order::class, [
            'payment_id' => $new_again_payment->id,
            'payment_source_id' => $new_card->id,
        ]);
    }

    #[Test]
    public function it_can_get_its_current_quantities_by_product_id(): void
    {
        /** @var Order $cart */
        $cart = Order::factory()->create();

        $this->assertEmpty($cart->itemsInCart());

        $product_one = Product::factory()->create();

        $cart = $cart->addItemToCart(new Item(
            id: 'abc-123',
            product: $product_one,
            quantity: 1,
        ));

        $product_two = Product::factory()->create();

        $cart = $cart->addItemToCart(new Item(
            id: 'abc-456',
            product: $product_two,
            quantity: 3,
        ));

        $result = $cart->quantitiesByProductId();

        $this->assertInstanceOf(Collection::class, $result);

        $this->assertEquals(1, $result->get($product_one->id));
        $this->assertEquals(3, $result->get($product_two->id));
    }

    #[Test]
    public function it_can_get_its_current_quantities_with_bundles_by_product_id(): void
    {
        /** @var Cart $cart */
        $cart = Cart::factory()->make();

        $this->assertEmpty($cart->itemsInCart());

        $product_one = Product::factory()->create();

        // item 1
        $cart = $cart->addItemToCart(new Item(
            id: 'abc-123',
            product: $product_one,
            quantity: 2,
        ));

        $product_two = Product::factory()->create();
        $product_three = Product::factory()->create();

        $bundle_one = Product::factory()->create(['is_bundle' => true, 'track_inventory' => 'bundle']);
        $bundle_one->bundle()->attach($product_one->id, ['qty' => 2]);
        $bundle_one->bundle()->attach($product_two->id, ['qty' => 1]);
        $bundle_one->bundle()->attach($product_three->id, ['qty' => 4]);

        // item 2
        $cart = $cart->addItemToCart(new Item(
            id: 'abc-456',
            product: $bundle_one,
            quantity: 3,
        ));

        $bundle_two = Product::factory()->create(['is_bundle' => true, 'track_inventory' => 'yes']);
        $bundle_two->bundle()->attach($product_three->id, ['qty' => 2]);

        // item 3
        $cart = $cart->addItemToCart(new Item(
            id: 'abc-789',
            product: $bundle_two,
            quantity: 2,
        ));

        // item 4
        $cart = $cart->addItemToCart(new Item(
            id: 'abc-012',
            product: $product_three,
            quantity: 5,
        ));

        $result = $cart->quantitiesByProductId();

        $this->assertInstanceOf(Collection::class, $result);

        // 2 + 6 = 8 (item 1: 2, item 2: 6)
        $this->assertEquals(8, $result->get($product_one->id));

        // tracks on bundled items
        $this->assertEquals(0, $result->get($bundle_one->id));

        // 3 (item 2: 3)
        $this->assertEquals(3, $result->get($product_two->id));

        // tracks on bundled items
        $this->assertEquals(2, $result->get($bundle_two->id));

        // 12 + 0 + 5 = 17 (item 2: 12, item 3: 0, item 4: 5)
        $this->assertEquals(17, $result->get($product_three->id));
    }
}
