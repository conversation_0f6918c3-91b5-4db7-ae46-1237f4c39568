<?php

namespace Tests\Unit\Tasks;

use App\Actions\Cart\CreateCartFromOrder;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Pickup;
use App\Models\Product;
use Illuminate\Support\Str;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class ReorderTest extends TenantTestCase
{
    #[Test]
    public function it_moves_items_from_one_order_to_another(): void
    {
        /** @var Order $from */
        $from = Order::factory()->create();
        $items = OrderItem::factory()->count(2)->create(['order_id' => $from->id]);

        /** @var Order $to */
        $to = Order::factory()->create();

        (new CreateCartFromOrder)->handle($from, $to);

        $items->each(function (OrderItem $item) use ($to) {
            $this->assertDatabaseHas(OrderItem::class, [
                'order_id' => $to->id,
                'product_id' => $item->product_id,
                'qty' => $item->qty
            ]);
        });
    }

    #[Test]
    public function its_message_indicates_the_items_added(): void
    {
        /** @var Order $from */
        $from = Order::factory()->create();
        $items = OrderItem::factory()->count(2)->create(['order_id' => $from->id]);

        /** @var Order $to */
        $to = Order::factory()->create();

        $result = (new CreateCartFromOrder)->handle($from, $to);

        $message = $result->getMessage();

        $this->assertTrue(Str::contains($message, 'Added to your cart:'));

        $items->each(function (OrderItem $item) use ($message) {
            $this->assertTrue(Str::contains($message, $item->title));
        });
    }

    #[Test]
    public function it_does_not_move_trashed_products_from_one_order_to_another(): void
    {
        /** @var Order $from */
        $from = Order::factory()->create();
        /** @var Product $product */
        $product = Product::factory()->create(['deleted_at' => now()]);
        $items = OrderItem::factory()->count(2)->create(['order_id' => $from->id, 'product_id' => $product->id]);

        /** @var Order $to */
        $to = Order::factory()->create();

        (new CreateCartFromOrder)->handle($from, $to);

        $items->each(function (OrderItem $item) use ($to) {
            $this->assertDatabaseMissing(OrderItem::class, [
                'order_id' => $to->id,
                'product_id' => $item->product_id,
            ]);
        });
    }

    #[Test]
    public function its_message_indicates_trashed_products_are_not_available(): void
    {
        /** @var Order $from */
        $from = Order::factory()->create();
        $product = Product::factory()->create(['deleted_at' => now()]);
        $items = OrderItem::factory()->count(2)->create(['order_id' => $from->id, 'product_id' => $product->id]);

        /** @var Order $to */
        $to = Order::factory()->create();

        $result = (new CreateCartFromOrder)->handle($from, $to);

        $message = $result->getMessage();

        $this->assertTrue(Str::contains($message, 'No items were added to your cart.'));

        $items->each(function (OrderItem $item) use ($message) {
            $this->assertTrue(Str::contains($message,  "{$item->title} is no longer available."));
        });
    }

    #[Test]
    public function it_does_not_move_location_exclusive_products_from_one_order_to_another(): void
    {
        /** @var Order $from */
        $from = Order::factory()->create();
        /** @var Product $product */
        $product = Product::factory()->create();
        /** @var Pickup $pickup */
        $pickup = Pickup::factory()->create();
        $pickup->products()->attach($product->id);

        $items = OrderItem::factory()->count(2)->create(['order_id' => $from->id, 'product_id' => $product->id]);

        /** @var Order $to */
        $to = Order::factory()->create(['pickup_id' => $pickup->id]);

        (new CreateCartFromOrder)->handle($from, $to);

        $items->each(function (OrderItem $item) use ($to) {
            $this->assertDatabaseMissing(OrderItem::class, [
                'order_id' => $to->id,
                'product_id' => $item->product_id,
            ]);
        });
    }

    #[Test]
    public function its_message_indicates_location_exclusive_products_are_not_available(): void
    {
        /** @var Order $from */
        $from = Order::factory()->create();
        /** @var Product $product */
        $product = Product::factory()->create();
        /** @var Pickup $pickup */
        $pickup = Pickup::factory()->create();
        $pickup->products()->attach($product->id);

        $items = OrderItem::factory()->count(2)->create(['order_id' => $from->id, 'product_id' => $product->id]);

        /** @var Order $to */
        $to = Order::factory()->create(['pickup_id' => $pickup->id]);

        $result = (new CreateCartFromOrder)->handle($from, $to);

        $message = $result->getMessage();

        $this->assertTrue(Str::contains($message, 'No items were added to your cart.'));

        $items->each(function (OrderItem $item) use ($message) {
            $this->assertTrue(Str::contains($message,  "{$item->title} is not available for your selected location."));
        });
    }

    #[Test]
    public function it_does_not_move_low_stock_products_from_one_order_to_another(): void
    {
        /** @var Order $from */
        $from = Order::factory()->create();
        /** @var Product $product */
        $product = Product::factory()->create(['track_inventory' => 'yes', 'inventory' => 1]);
        $items = OrderItem::factory()->count(2)->create(['order_id' => $from->id, 'product_id' => $product->id, 'qty' => 2]);

        /** @var Order $to */
        $to = Order::factory()->create();

        (new CreateCartFromOrder)->handle($from, $to);

        $items->each(function (OrderItem $item) use ($to) {
            $this->assertDatabaseMissing(OrderItem::class, [
                'order_id' => $to->id,
                'product_id' => $item->product_id
            ]);
        });
    }

    #[Test]
    public function its_message_indicates_low_stock_products_are_not_available(): void
    {
        /** @var Order $from */
        $from = Order::factory()->create();
        /** @var Product $product */
        $product = Product::factory()->create(['track_inventory' => 'yes', 'inventory' => 1]);
        $items = OrderItem::factory()->count(2)->create(['order_id' => $from->id, 'product_id' => $product->id, 'qty' => 2]);

        /** @var Order $to */
        $to = Order::factory()->create();

        $result = (new CreateCartFromOrder)->handle($from, $to);

        $message = $result->getMessage();

        $this->assertTrue(Str::contains($message, 'No items were added to your cart.'));

        $items->each(function (OrderItem $item) use ($message) {
            $this->assertTrue(Str::contains($message,  "{$item->title} does not have enough stock for the requested quantity."));
        });
    }

    #[Test]
    public function its_error_message_is_null_when_there_are_no_errors(): void
    {
        /** @var Order $from */
        $from = Order::factory()->create();
        $items = OrderItem::factory()->count(2)->create(['order_id' => $from->id]);

        /** @var Order $to */
        $to = Order::factory()->create();

        $result = (new CreateCartFromOrder)->handle($from, $to);

        $this->assertNull($result->getErrorMessage());
    }

    #[Test]
    public function it_can_fetch_the_error_message(): void
    {
        /** @var Order $from */
        $from = Order::factory()->create();
        /** @var Product $product */
        $product = Product::factory()->create(['track_inventory' => 'yes', 'inventory' => 1]);
        $items = OrderItem::factory()->count(2)->create(['order_id' => $from->id, 'product_id' => $product->id, 'qty' => 2]);

        /** @var Order $to */
        $to = Order::factory()->create();

        $result = (new CreateCartFromOrder)->handle($from, $to);

        $message = $result->getErrorMessage();

        $this->assertFalse(Str::contains($message, 'No items were added to your cart.'));

        $items->each(function (OrderItem $item) use ($message) {
            $this->assertTrue(Str::contains($message,  "{$item->title} does not have enough stock for the requested quantity."));
        });
    }
}