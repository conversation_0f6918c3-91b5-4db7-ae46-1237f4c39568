<?php

namespace Tests\Unit\Listeners\Order\OrderWasConfirmed;

use App\Events\Order\OrderWasConfirmed;
use App\Listeners\Order\OrderWasConfirmed\SendGiftRecipientNotification;
use App\Mail\GiftWasPurchased;
use App\Models\Order;
use App\Models\Setting;
use App\Models\Template;
use App\Models\User;
use Illuminate\Mail\Mailable;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Mail;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class SendGiftRecipientListenerTest extends TenantTestCase
{
    #[Test]
    public function it_listens_to_expected_event(): void
    {
        Event::fake([OrderWasConfirmed::class]);

        Event::assertListening(OrderWasConfirmed::class, SendGiftRecipientNotification::class);
    }

    #[Test]
    public function it_sends_expected_mail_to_gift_recipient_email(): void
    {
        Mail::fake();

        $template = Template::factory()->create();

        Setting::updateOrCreate(
            ['key' => 'email_gift_purchased_template'],
            ['value' => $template->id]
        );

        $customer = User::factory()->create(['email' => '<EMAIL>']);
        $order = Order::factory()->create([
            'customer_id' => $customer->id,
            'customer_email' => '<EMAIL>',
            'blueprint_id' => null,
            'confirmed' => true,
            'recipient_email' => '<EMAIL>'
        ]);

        (new SendGiftRecipientNotification)->handle(new OrderWasConfirmed($order));

        Mail::assertQueued(GiftWasPurchased::class, function (Mailable $mail) {
            return $mail->hasTo('<EMAIL>');
        });
    }

    #[Test]
    public function it_does_not_send_gift_purchased_email_to_non_gift_orders(): void
    {
        Mail::fake();

        $template = Template::factory()->create();

        Setting::updateOrCreate(
            ['key' => 'email_gift_purchased_template'],
            ['value' => $template->id]
        );

        $customer = User::factory()->create(['email' => '<EMAIL>']);
        $order = Order::factory()->create([
            'customer_id' => $customer->id,
            'customer_email' => '<EMAIL>',
            'blueprint_id' => null,
            'confirmed' => true,
            'recipient_email' => null
        ]);

        (new SendGiftRecipientNotification)->handle(new OrderWasConfirmed($order));

        Mail::assertNothingQueued();
        Mail::assertNotSent(GiftWasPurchased::class);
    }
}
