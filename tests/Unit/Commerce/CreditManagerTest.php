<?php

namespace Tests\Unit\Commerce;

use App\Commerce\CreditManager;
use App\Models\Order;
use App\Models\User;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class CreditManagerTest extends TenantTestCase
{
    #[Test]
    public function it_can_apply_a_credit_to_an_order_when_order_total_is_greater_than_customer_credit_total(): void
    {
        $user = User::factory()->create(['credit' => 500]);
        $order = Order::factory()->create(['customer_id' => $user->id, 'total' => 1000, 'credit_applied' => 0]);

        (new CreditManager)->ApplyCreditToOrder($order);

        $this->assertDatabaseHas('users', [
            'id' => $user->id,
            'credit' => 0
        ]);

        $this->assertDatabaseHas('orders', [
            'id' => $order->id,
            'credit_applied' => 500,
            'total' => 1000
        ]);

        $this->assertDatabaseCount('events', 2);

        $this->assertDatabaseHas('events', [
            'model_type' => \App\Models\User::class,
            'model_id' => $user->id,
            'description' => "Order #{$order->id} adjusted.",
            'event_id' => 'credit_removed',
            'user_id' => $user->id,
            'metadata' => json_encode(['amount' => 500])
        ]);

        $this->assertDatabaseHas('events', [
            'model_type' => \App\Models\Order::class,
            'model_id' => $order->id,
            'description' => "5.00 in credit applied.",
            'event_id' => 'credit_applied',
            'user_id' => $user->id,
            'metadata' => json_encode(['amount' => 500])
        ]);
    }

    #[Test]
    public function it_can_apply_a_credit_to_an_order_when_order_total_is_less_than_customer_credit_total(): void
    {
        $user = User::factory()->create(['credit' => 1000]);
        $order = Order::factory()->create(['customer_id' => $user->id, 'total' => 500, 'credit_applied' => 0]);

        (new CreditManager)->ApplyCreditToOrder($order);

        $this->assertDatabaseHas('users', [
            'id' => $user->id,
            'credit' => 500
        ]);

        $this->assertDatabaseHas('orders', [
            'id' => $order->id,
            'credit_applied' => 500,
            'total' => 500
        ]);

        $this->assertDatabaseCount('events', 2);

        $this->assertDatabaseHas('events', [
            'model_type' => \App\Models\User::class,
            'model_id' => $user->id,
            'description' => "Order #{$order->id} adjusted.",
            'event_id' => 'credit_removed',
            'user_id' => $user->id,
            'metadata' => json_encode(['amount' => 500])
        ]);

        $this->assertDatabaseHas('events', [
            'model_type' => \App\Models\Order::class,
            'model_id' => $order->id,
            'description' => "5.00 in credit applied.",
            'event_id' => 'credit_applied',
            'user_id' => $user->id,
            'metadata' => json_encode(['amount' => 500])
        ]);
    }

    #[Test]
    public function it_does_not_apply_credit_when_customer_has_no_available_credit(): void
    {
        $user = User::factory()->create(['credit' => 0]);
        $order = Order::factory()->create(['customer_id' => $user->id, 'total' => 1000, 'credit_applied' => 0]);

        (new CreditManager)->ApplyCreditToOrder($order);

        $this->assertDatabaseHas('users', [
            'id' => $user->id,
            'credit' => 0
        ]);

        $this->assertDatabaseHas('orders', [
            'id' => $order->id,
            'credit_applied' => 0,
            'total' => 1000
        ]);

        $this->assertDatabaseCount('events', 0);
    }

    #[Test]
    public function it_can_remove_a_credit_from_an_order_when_order_total_is_less_than_applied_credit(): void
    {
        $user = User::factory()->create(['credit' => 500]);
        $order = Order::factory()->create(['customer_id' => $user->id, 'total' => 500, 'credit_applied' => 1000]);

        (new CreditManager)->ApplyCreditToOrder($order);

        $this->assertDatabaseHas('users', [
            'id' => $user->id,
            'credit' => 1000
        ]);

        $this->assertDatabaseHas('orders', [
            'id' => $order->id,
            'credit_applied' => 500,
            'total' => 500
        ]);

        $this->assertDatabaseCount('events', 2);

        $this->assertDatabaseHas('events', [
            'model_type' => \App\Models\User::class,
            'model_id' => $user->id,
            'description' => "Order #{$order->id} adjusted.",
            'event_id' => 'credit_applied',
            'user_id' => $user->id,
            'metadata' => json_encode(['amount' => 500])
        ]);

        $this->assertDatabaseHas('events', [
            'model_type' => \App\Models\Order::class,
            'model_id' => $order->id,
            'description' => "5.00 in credit removed.",
            'event_id' => 'credit_removed',
            'user_id' => $user->id,
            'metadata' => json_encode(['amount' => 500])
        ]);
    }
}
