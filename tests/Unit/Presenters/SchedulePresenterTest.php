<?php

namespace Tests\Unit\Presenters;

use App\Models\Date;
use App\Models\Schedule;
use App\Presenters\SchedulePresenter;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class SchedulePresenterTest extends TenantTestCase
{
    #[Test]
    public function it_can_present_the_upcoming_pickup_date_when_it_has_no_dates(): void
    {
        $schedule = Schedule::factory()->create();
        $this->assertEquals('No upcoming pickup scheduled', (new SchedulePresenter($schedule))->upcomingPickupDate());
    }

    #[Test]
    public function it_can_present_the_upcoming_pickup_date_when_it_has_a_past_active_date(): void
    {
        $schedule = Schedule::factory()->create();
        Date::factory()->create(['schedule_id' => $schedule->id, 'pickup_date' => today()->subDays(2), 'active' => true]);
        $this->assertEquals('No upcoming pickup scheduled', (new SchedulePresenter($schedule))->upcomingPickupDate());
    }

    #[Test]
    public function it_can_present_the_upcoming_pickup_date_when_it_has_an_upcoming_inactive_date(): void
    {
        $schedule = Schedule::factory()->create();
        Date::factory()->create(['schedule_id' => $schedule->id, 'pickup_date' => today()->addDays(2), 'active' => false]);
        $this->assertEquals('No upcoming pickup scheduled', (new SchedulePresenter($schedule))->upcomingPickupDate());
    }

    #[Test]
    public function it_can_present_the_upcoming_pickup_date_when_it_has_an_upcoming_active_date(): void
    {
        $schedule = Schedule::factory()->create();
        $date = Date::factory()->create(['schedule_id' => $schedule->id, 'pickup_date' => today()->addDays(2), 'active' => true]);
        $this->assertEquals($date->pickup_date->format('D, M jS'), (new SchedulePresenter($schedule))->upcomingPickupDate());
    }
}