<?php

namespace Tests\Unit\Jobs;

use App\Jobs\MigrateCustomerFromClosedDeliveryMethod;
use App\Jobs\MigrateCustomersFromClosedDeliveryMethod;
use App\Models\Pickup;
use App\Models\User;
use Illuminate\Support\Facades\Bus;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class MigrateCustomersFromClosedDeliveryMethodTest extends TenantTestCase
{
    #[Test]
    public function it_queues_jobs_for_users_that_need_to_be_migrated(): void
    {
        Bus::fake([MigrateCustomerFromClosedDeliveryMethod::class]);

        $old_delivery_method = Pickup::factory()->create();
        $users = User::factory(2)->create(['pickup_point' => $old_delivery_method->id]);
        $other_users = User::factory(2)->create(['pickup_point' => null]);

        (new MigrateCustomersFromClosedDeliveryMethod($old_delivery_method->id))->handle();

        Bus::assertDispatched(MigrateCustomerFromClosedDeliveryMethod::class, 2);

        foreach ($users as $user) {
            Bus::assertDispatched(
                MigrateCustomerFromClosedDeliveryMethod::class,
                function (MigrateCustomerFromClosedDeliveryMethod $job) use ($old_delivery_method, $user) {
                    return $job->delivery_method_id === $old_delivery_method->id
                        && $job->user_id === $user->id;
                });
        }

        foreach ($other_users as $user) {
            Bus::assertNotDispatched(
                MigrateCustomerFromClosedDeliveryMethod::class,
                fn($job) => $job->user_id === $user->id
            );
        }
    }
}
