<?php

namespace Tests\Unit\Integrations\Drip\Jobs;

use App\Integrations\Drip\Drip;
use App\Integrations\Drip\DripShopperActivity;
use App\Integrations\Drip\Jobs\RecordCanceledOrder;
use App\Models\Integration;
use App\Models\Order;
use App\Models\User;
use Mockery\MockInterface;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class RecordCanceledOrderTest extends TenantTestCase
{
    #[Test]
    public function it_records_expected_event_in_drip(): void
    {
        Integration::factory()->create([
            'name' => 'drip',
            'settings' => [
                'api_key' => 'abc123',
                'account_id' => 'def456'
            ]
        ]);

        $order = Order::factory()->create([
            'total' => 12345,
            'schedule_id' => 123,
            'pickup_id' => 456,
            'pickup_date' => '2023-01-01'
        ]);

        $mock_shopper_activity = \Mockery::mock(DripShopperActivity::class);
        $mock_shopper_activity->shouldReceive('cancelOrder')->once()
            ->with(\Mockery::on(fn (Order $arg) => $arg->id === $order->id))
            ->andReturn([]);

        $this->mock(Drip::class, function (MockInterface $mock) use ($order, $mock_shopper_activity) {
            $mock->shouldReceive('configure')->once()
                ->with('abc123', 'def456')->andReturnSelf();

                $mock->shouldReceive('recordEvent')->once()->with(
                    \Mockery::on(fn ($arg) => $arg === $order->customer->email),
                    'Order canceled',
                    [
                        'order_id' => $order->id,
                        'order_total' => 12345,
                        'schedule_id' => 123,
                        'fulfillment_id' => 456,
                        'pickup_date' => '01/01/23'
                    ]
                )->andReturn(true);

                $mock->shouldReceive('shopperActivity')->once()->withNoArgs()->andReturn($mock_shopper_activity);
        });

        (new RecordCanceledOrder($order->id))->handle();
    }

    #[Test]
    public function it_does_not_record_event_in_drip_when_order_cannot_be_found(): void
    {
        Integration::factory()->create([
            'name' => 'drip',
            'settings' => [
                'api_key' => 'abc123',
                'account_id' => 'def456'
            ]
        ]);

        $this->mock(Drip::class, function (MockInterface $mock) {
            $mock->shouldReceive('configure')->once()
                ->with('abc123', 'def456')->andReturnSelf();

            $mock->shouldNotReceive('recordEvent');
        });

        (new RecordCanceledOrder(*********))->handle();
    }
}
