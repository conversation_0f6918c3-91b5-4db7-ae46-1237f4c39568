<?php

namespace Tests\Unit\Integrations\Drip\Jobs;

use App\Integrations\Drip\Drip;
use App\Integrations\Drip\DripShopperActivity;
use App\Integrations\Drip\Jobs\RecordPurchase;
use App\Models\Integration;
use App\Models\Order;
use App\Models\User;
use Mockery\MockInterface;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class RecordPurchaseTest extends TenantTestCase
{
    #[Test]
    public function it_records_the_purchase_in_drip_for_a_gift_order(): void
    {
        config(['app.url' => 'http://localhost/test']);

        Integration::factory()->create([
            'name' => 'drip',
            'settings' => [
                'api_key' => 'abc123',
                'account_id' => 'def456'
            ]
        ]);

        $order = Order::factory()->create([
            'total' => 12345,
            'schedule_id' => 123,
            'pickup_id' => 456,
            'pickup_date' => '2023-01-01',
            'recipient_email' => '<EMAIL>'
        ]);

        $mock_shopper_activity = \Mockery::mock(DripShopperActivity::class);
        $mock_shopper_activity->shouldReceive('placeOrder')->once()
            ->with(
                \Mockery::on(fn (Order $arg) => $arg->id === $order->id),
                'http://localhost/test'
            )
            ->andReturn([]);

        $this->mock(Drip::class, function (MockInterface $mock) use ($order, $mock_shopper_activity) {
            $mock->shouldReceive('configure')->once()
                ->with('abc123', 'def456')->andReturnSelf();

            $mock->shouldReceive('recordPurchase')->once()->with(
                    \Mockery::on(fn (User $arg) => $arg->id === $order->customer_id),
                    \Mockery::on(fn (Order $arg) => $arg->id === $order->id)
                )->andReturn(true);

            $mock->shouldReceive('updateSubscriber')->once()->with(
                    \Mockery::on(fn (User $arg) => $arg->id === $order->customer_id),
                    ['Buyer']
                )->andReturn(true);

            $mock->shouldReceive('shopperActivity')->once()->withNoArgs()
                ->andReturn($mock_shopper_activity);


                $mock->shouldReceive('recordEvent')->once()->with(
                    \Mockery::on(fn ($arg) => $arg === $order->customer->email),
                    'New Gift Order',
                    [
                        'order_id' => $order->id,
                        'recipient_email' => '<EMAIL>',
                    ]
                )->andReturn(true);
        });

        (new RecordPurchase($order->customer_id, $order->id))->handle();
    }

    #[Test]
    public function it_records_the_purchase_in_drip_for_a_non_gift_order(): void
    {
        config(['app.url' => 'http://localhost/test']);

        Integration::factory()->create([
            'name' => 'drip',
            'settings' => [
                'api_key' => 'abc123',
                'account_id' => 'def456'
            ]
        ]);

        $order = Order::factory()->create([
            'total' => 12345,
            'schedule_id' => 123,
            'pickup_id' => 456,
            'pickup_date' => '2023-01-01',
            'recipient_email' => ''
        ]);

        $mock_shopper_activity = \Mockery::mock(DripShopperActivity::class);
        $mock_shopper_activity->shouldReceive('placeOrder')->once()
            ->with(
                \Mockery::on(fn (Order $arg) => $arg->id === $order->id),
                'http://localhost/test'
            )
            ->andReturn([]);

        $this->mock(Drip::class, function (MockInterface $mock) use ($order, $mock_shopper_activity) {
            $mock->shouldReceive('configure')->once()
                ->with('abc123', 'def456')->andReturnSelf();

            $mock->shouldReceive('recordPurchase')->once()->with(
                \Mockery::on(fn (User $arg) => $arg->id === $order->customer_id),
                \Mockery::on(fn (Order $arg) => $arg->id === $order->id)
            )->andReturn(true);

            $mock->shouldReceive('updateSubscriber')->once()->with(
                \Mockery::on(fn (User $arg) => $arg->id === $order->customer_id),
                ['Buyer']
            )->andReturn(true);

            $mock->shouldReceive('shopperActivity')->once()->withNoArgs()
                ->andReturn($mock_shopper_activity);


            $mock->shouldNotReceive('recordEvent');
        });

        (new RecordPurchase($order->customer_id, $order->id))->handle();
    }

    #[Test]
    public function it_does_not_record_the_purchase_when_drip_is_disabled(): void
    {
        config(['app.url' => 'http://localhost/test']);

        Integration::factory()->create([
            'name' => 'drip',
            'settings' => [
                'api_key' => 'abc123',
                'account_id' => 'def456'
            ],
            'enabled' => false
        ]);

        $order = Order::factory()->create([
            'total' => 12345,
            'schedule_id' => 123,
            'pickup_id' => 456,
            'pickup_date' => '2023-01-01',
            'recipient_email' => ''
        ]);

        $this->mock(Drip::class, function (MockInterface $mock) {
            $mock->shouldNotReceive('configure');
            $mock->shouldNotReceive('recordPurchase');
            $mock->shouldNotReceive('updateSubscriber');
            $mock->shouldNotReceive('shopperActivity');
            $mock->shouldNotReceive('recordEvent');
        });

        (new RecordPurchase($order->customer_id, $order->id))->handle();
    }
}
