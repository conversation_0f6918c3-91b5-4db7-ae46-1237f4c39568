<?php

namespace Tests\Unit\Services\FilterService;

use App\Services\FilterService\Filters\Orders;
use App\Services\FilterService\Filters\OrderStatus;
use Illuminate\Support\Collection;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class FilterServiceTest extends TenantTestCase
{
    #[Test]
    public function it_runs_only_the_filters_present_in_the_request(): void
    {
        $this->actingAsAdmin()
            ->get(route('admin.orders.index', ['orders' => '123', 'order_status' => [1]]))
            ->assertOk()
            ->assertViewHas('appliedFilters', function (Collection $arg) {
                $order_filter = $arg->get('orders');
                $status_filter = $arg->get('order_status');

                return $order_filter instanceof Orders
                    && $order_filter->label() === 'Search:'
                    && $order_filter->value() === '123'
                    && $order_filter->removeFilterUrl() === route('admin.orders.index') . '?order_status%5B0%5D=1&confirmed=1&orderBy=orders.confirmed_date&sort=desc'
                    && $status_filter instanceof OrderStatus
                    && $status_filter->label() === 'Status:'
                    && $status_filter->value() === 'New'
                    && $status_filter->removeFilterUrl() === route('admin.orders.index') . '?orders=123&confirmed=1&orderBy=orders.confirmed_date&sort=desc';
            });
    }
}