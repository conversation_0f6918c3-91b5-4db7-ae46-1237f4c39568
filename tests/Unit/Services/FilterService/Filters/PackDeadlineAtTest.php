<?php

namespace Tests\Unit\Services\FilterService\Filters;

use App\Services\FilterService\Filters\PackDeadlineAt;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class PackDeadlineAtTest extends TestCase
{
    use CreatesFakeRequest;

    #[Test]
    public function it_can_return_its_properties_for_one_date(): void
    {
        $collection = (new PackDeadlineAt)->handle([$this->createRequest(['pack_deadline_at' => '2020-12-20']), collect()], function ($passable) {
            list($request, $collection) = $passable;
            return $collection;
        });

        $this->assertTrue($collection->has('pack_deadline_at'));

        /** @var PackDeadlineAt $filter */
        $filter = $collection->get('pack_deadline_at');

        $this->assertEquals('Pack Deadline:', $filter->label());
        $this->assertEquals('2020-12-20', $filter->value());
        $this->assertEquals('http://test.test?foo=bar', $filter->removeFilterUrl());

    }

    #[Test]
    public function it_can_return_its_properties_for_an_array_of_dates(): void
    {
        $collection = (new PackDeadlineAt)->handle([$this->createRequest(['pack_deadline_at' => ['2020-12-20', '2020-12-25']]), collect()], function ($passable) {
            list($request, $collection) = $passable;
            return $collection;
        });

        $this->assertTrue($collection->has('pack_deadline_at'));

        /** @var PackDeadlineAt $filter */
        $filter = $collection->get('pack_deadline_at');

        $this->assertEquals('Pack Deadline:', $filter->label());
        $this->assertEquals('2020-12-20 - 2020-12-25', $filter->value());
        $this->assertEquals('http://test.test?foo=bar', $filter->removeFilterUrl());

    }
}