<?php

namespace Tests\Unit\Services\FilterService\Filters;

use App\Services\FilterService\Filters\OrderStatus;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class OrderStatusTest extends TestCase
{
    use CreatesFakeRequest;

    #[Test]
    public function it_can_return_its_properties_for_one_status(): void
    {
        $collection = (new OrderStatus)->handle([$this->createRequest(['order_status' => 3]), collect()], function ($passable) {
            list($request, $collection) = $passable;
            return $collection;
        });

        $this->assertTrue($collection->has('order_status'));

        /** @var OrderStatus $filter */
        $filter = $collection->get('order_status');

        $this->assertEquals('Status:', $filter->label());
        $this->assertEquals('Packed', $filter->value());
        $this->assertEquals('http://test.test?foo=bar', $filter->removeFilterUrl());

    }

    #[Test]
    public function it_can_return_its_properties_for_an_array_of_statuses(): void
    {
        $collection = (new OrderStatus)->handle([$this->createRequest(['order_status' => [1,3]]), collect()], function ($passable) {
            list($request, $collection) = $passable;
            return $collection;
        });

        $this->assertTrue($collection->has('order_status'));

        /** @var OrderStatus $filter */
        $filter = $collection->get('order_status');

        $this->assertEquals('Status:', $filter->label());
        $this->assertEquals('New, Packed', $filter->value());
        $this->assertEquals('http://test.test?foo=bar', $filter->removeFilterUrl());

    }
}