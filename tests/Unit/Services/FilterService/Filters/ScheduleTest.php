<?php

namespace Tests\Unit\Services\FilterService\Filters;

use App\Models\Schedule as ScheduleModel;
use App\Services\FilterService\Filters\Schedule;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class ScheduleTest extends TenantTestCase
{
    use CreatesFakeRequest;

    #[Test]
    public function it_can_return_its_properties_for_one_schedule(): void
    {
        $schedule = ScheduleModel::factory()->create();

        $collection = (new Schedule)->handle([$this->createRequest(['schedule_id' => $schedule->id]), collect()], function ($passable) {
            list($request, $collection) = $passable;
            return $collection;
        });

        $this->assertTrue($collection->has('schedule_id'));

        /** @var Schedule $filter */
        $filter = $collection->get('schedule_id');

        $this->assertEquals('Schedule:', $filter->label());
        $this->assertEquals($schedule->title, $filter->value());
        $this->assertEquals('http://test.test?foo=bar', $filter->removeFilterUrl());

    }

    #[Test]
    public function it_can_return_its_properties_for_an_array_of_statuses(): void
    {
        $schedules = ScheduleModel::factory()->times(2)->create();

        $collection = (new Schedule)->handle([$this->createRequest(['schedule_id' => $schedules->pluck('id')->toArray()]), collect()], function ($passable) {
            list($request, $collection) = $passable;
            return $collection;
        });

        $this->assertTrue($collection->has('schedule_id'));

        /** @var Schedule $filter */
        $filter = $collection->get('schedule_id');

        $this->assertEquals('Schedule:', $filter->label());
        $this->assertEquals($schedules->pluck('title')->implode(', '), $filter->value());
        $this->assertEquals('http://test.test?foo=bar', $filter->removeFilterUrl());

    }
}