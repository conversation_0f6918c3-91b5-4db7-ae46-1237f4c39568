<?php

namespace Tests\Unit\Services\FilterService\Filters;

use App\Services\FilterService\Filters\PackingGroup as PackingGroupFilter;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Request;
use Tests\TestCase;

class PackingGroupTest extends TestCase
{
    use RefreshDatabase;

    public function test_set_value_with_single_packing_group(): void
    {
        $request = new Request(['inventory_type' => 1]);
        $filter = new PackingGroupFilter();
        
        $filter->setValue($request);
        
        $this->assertEquals('Frozen', $filter->value());

        $request = new Request(['inventory_type' => 3]);
        $filter = new PackingGroupFilter();

        $filter->setValue($request);

        $this->assertNotEquals('Frozen', $filter->value());
    }

    public function test_set_value_with_multiple_packing_groups(): void
    {
        $request = new Request(['inventory_type' => [1, 3]]);
        $filter = new PackingGroupFilter();
        
        $filter->setValue($request);
        
        $this->assertEquals('Frozen, Fresh', $filter->value());
    }

    public function test_label_is_correct(): void
    {
        $filter = new PackingGroupFilter();
        
        $this->assertEquals('Packing Group:', $filter->label());
    }

    public function test_query_param_is_correct(): void
    {
        $this->assertEquals('inventory_type', PackingGroupFilter::$query_param);
    }
}