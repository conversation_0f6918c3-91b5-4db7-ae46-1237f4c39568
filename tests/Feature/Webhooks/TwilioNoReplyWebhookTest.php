<?php

namespace Tests\Feature\Webhooks;

use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class TwilioNoReplyWebhookTest extends TenantTestCase
{
    #[Test]
    public function it_returns_a_no_reply_response(): void
    {
        $response = $this->get(route('twilio.webhook.no-reply'))
            ->assertOk()
            ->assertHeader('Content-Type', 'application/xml');

        $this->assertTrue(
            str($response->content())
                ->contains("<Response><Message>This is a no-reply number. You can follow the link in the" .
                    " previous message to visit our website for more help!</Message></Response>"
                )
        );
    }
}
