<?php

namespace Tests\Feature\Livewire\Admin\Modals;

use App\Livewire\Admin\Modals\AddPackingGroup;
use App\Models\PackingGroup;
use Livewire\Livewire;
use PHPUnit\Framework\Attributes\Test;
use Tests\LivewireTenantTestCase;

class AddPackingGroupTest extends LivewireTenantTestCase
{
    #[Test]
    public function it_can_render(): void
    {
        Livewire::test(AddPackingGroup::class)
            ->dispatch('open-modal-add-packing-group')
            ->assertStatus(200);
    }

    #[Test]
    function it_validates_the_title_upon_submission(): void
    {
        PackingGroup::create(['title' => 'notunique']);

        Livewire::test(AddPackingGroup::class)
            ->dispatch('open-modal-add-packing-group')
            ->set('title', null)
            ->call('submit')
            ->assertHasErrors(['title' => 'required'])
            ->set('title', 'notunique')
            ->call('submit')
            ->assertHasErrors(['title' => 'unique']);
    }

    #[Test]
    function it_can_add_a_packing_group(): void
    {
        Livewire::test(AddPackingGroup::class)
            ->dispatch('open-modal-add-packing-group')
            ->set('title', 'New Packing Group')
            ->call('submit')
            ->assertHasNoErrors()
            ->assertDispatched('packing-group-created');

        $this->assertDatabaseHas(PackingGroup::class, [
            'title' => 'New Packing Group'
        ]);
    }
} 