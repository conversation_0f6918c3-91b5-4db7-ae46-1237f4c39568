<?php

namespace Tests\Feature\Livewire\Admin\Forms;

use App\Billing\Gateway\PaymentMethod;
use App\Contracts\Billing;
use App\Livewire\Admin\Forms\PaymentSourceSelect;
use App\Models\Card;
use App\Models\User;
use Livewire\Livewire;
use Mo<PERSON>y\MockInterface;
use PHPUnit\Framework\Attributes\Test;
use Tests\LivewireTenantTestCase;

class PaymentSourceSelectTest extends LivewireTenantTestCase
{
    #[Test]
    public function it_renders_successfully()
    {
        $user = User::factory()->create();
        $classes = 'w-full abc-123';
        $placeholder = 'Select...';

        $card_one = Card::factory()->create([
            'user_id' => $user->id,
            'source_id' => 'pm_123',
            'default' => false,
        ]);

        $card_two = Card::factory()->create([
            'user_id' => $user->id,
            'source_id' => 'pm_456',
            'default' => true,
        ]);

        $payment_method_one = new PaymentMethod(
            id: 'pm_123',
            customer_id: 'cus_123',
            customer_name: '<PERSON>',
            exp_month: '12',
            exp_year: '2022',
            brand: 'visa',
            last_four: '4242',
        );

        $payment_method_two = new PaymentMethod(
            id: 'pm_456',
            customer_id: 'cus_123',
            customer_name: 'John Doe',
            exp_month: '12',
            exp_year: '2028',
            brand: 'mastercard',
            last_four: '4444',
        );

        $expected_payment_methods = collect([$payment_method_one, $payment_method_two]);

        $this->mock(Billing::class, function (MockInterface $mock) use ($expected_payment_methods) {
            $mock->shouldReceive('retrieveUserPaymentMethods')->andReturn($expected_payment_methods);
        });

        Livewire::test(PaymentSourceSelect::class, [
            'user_id' => $user->id,
            'class' => $classes,
            'placeholder' => $placeholder
        ])
            ->assertViewIs('livewire.forms.payment-source-select')
            ->assertViewHas('payment_methods', $expected_payment_methods)
            ->assertViewHas('default_payment_source_id', 'pm_456')
            ->assertSee($placeholder)
            ->assertSee($classes)
            ->assertSee('Mastercard ending in 4444')
            ->assertSee('(Default)')
            ->assertSee('Visa ending in 4242');
    }
}
