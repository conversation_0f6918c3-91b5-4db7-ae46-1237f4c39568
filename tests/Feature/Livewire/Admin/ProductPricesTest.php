<?php

namespace Tests\Feature\Livewire\Admin;

use App\Livewire\Admin\ProductPrices;
use App\Models\Price;
use App\Models\Product;
use Livewire\Livewire;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class ProductPricesTest extends TenantTestCase
{
    #[Test]
    public function it_creates_default_price_tier_on_mount()
    {
        $product = Product::factory()->create();

        Livewire::test(ProductPrices::class, ['product' => $product])
            ->assertSet('prices', $product->prices()->get()->toArray());

        $this->assertDatabaseHas('prices', [
            'product_id' => $product->id,
            'quantity' => 1,
        ]);
    }

    #[Test]
    public function it_updates_prices_on_price_tier_added_event()
    {
        $product = Product::factory()->create();
        $price = Price::factory()->create(['product_id' => $product->id, 'quantity' => 5]);

        Livewire::test(ProductPrices::class, ['product' => $product])
            ->dispatch('price-tier-added', $product->id, 5)
            ->assertSet('prices', $product->prices()->get()->toArray());
    }

    #[Test]
    public function it_removes_price_tier_on_remove_method_call()
    {
        $product = Product::factory()->create();
        Price::factory()->create(['product_id' => $product->id, 'quantity' => 5]);

        Livewire::test(ProductPrices::class, ['product' => $product])
            ->call('remove', 5)
            ->assertSet('prices', function (array $prices) {
                return count($prices) === 1 && $prices[0]['quantity'] === 1;
            })
            ->assertDispatched('admin-notification-sent', notification: [
                'level' => 'success',
                'title' => 'Success!',
                'message' => 'The price tier has been removed.',
                'duration' => 3000
            ]);

        $this->assertDatabaseMissing('prices', [
            'product_id' => $product->id,
            'quantity' => 5,
        ]);
    }
}
