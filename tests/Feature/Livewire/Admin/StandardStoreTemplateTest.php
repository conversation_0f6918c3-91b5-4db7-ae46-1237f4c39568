<?php

namespace Tests\Feature\Livewire\Admin;

use App\Livewire\Admin\StandardStoreTemplate;
use App\Models\Product;
use Livewire\Livewire;
use PHPUnit\Framework\Attributes\Test;
use Tests\LivewireTenantTestCase;

class StandardStoreTemplateTest extends LivewireTenantTestCase
{
    #[Test]
    public function it_renders_successfully()
    {
        $product_id = Product::factory()->create()->id;

        Livewire::test(StandardStoreTemplate::class, compact('product_id'))
            ->assertSee('Heading')
            ->assertSee('Subheading')
            ->assertSee('Featured Image')
            ->assertSee('Farming Practices')
            ->assertSee('Tenderness')
            ->assertSee('Cookability')
            ->assertSee('Cost')
            ->assertSee('Related Products')
            ->assertSee('Related Recipes');
    }

    #[Test]
    public function it_validates_data_before_saving()
    {
        $product = Product::factory()->create();

        Livewire::test(StandardStoreTemplate::class, ['product_id' => $product->id])
            ->set('heading', null)
            ->set('subheading', null)
            ->set('farming_practices', 'invalid_practice')
            ->set('related_products', null)
            ->set('related_recipes', null)
            ->set('featured_image_path', 'invalid_url')
            ->set('tenderness_score', 11)
            ->set('cookability_score', 0)
            ->set('cost_score', -1)
            ->call('save')
            ->assertHasErrors([
                'farming_practices' => 'in',
                'featured_image_path' => 'url',
                'tenderness_score' => 'between',
                'cookability_score' => 'between',
                'cost_score' => 'between',
            ]);
    }

    #[Test]
    public function it_saves_valid_data()
    {
        $product = Product::factory()->create(['settings' => [
            'store_template' => 'bundle',
            'bundle_template' => ['heading' => 'Bundle heading'],
        ]]);

        Livewire::test(StandardStoreTemplate::class, ['product_id' => $product->id])
            ->set('heading', 'New Heading')
            ->set('subheading', 'New Subheading')
            ->set('farming_practices', 'chicken')
            ->set('related_products', 'Product3, Product4')
            ->set('related_recipes', 'Recipe3, Recipe4')
            ->set('featured_image_path', 'http://example.com/new_image.jpg')
            ->set('tenderness_score', 4)
            ->set('cookability_score', 3)
            ->set('cost_score', 2)
            ->call('save');

        $product->refresh();

        $this->assertEquals('New Heading', $product->settings->standard_template->heading);
        $this->assertEquals('New Subheading', $product->settings->standard_template->subheading);
        $this->assertEquals('chicken', $product->settings->standard_template->farming_practices);
        $this->assertEquals('Product3, Product4', $product->settings->standard_template->related_products);
        $this->assertEquals('Recipe3, Recipe4', $product->settings->standard_template->related_recipes);
        $this->assertEquals('http://example.com/new_image.jpg', $product->settings->standard_template->featured_image_path);
        $this->assertEquals(4, $product->settings->standard_template->tenderness_score);
        $this->assertEquals(3, $product->settings->standard_template->cookability_score);
        $this->assertEquals(2, $product->settings->standard_template->cost_score);
        $this->assertEquals('Bundle heading', $product->settings->bundle_template->heading);
    }

    #[Test]
    public function it_mounts_with_correct_data()
    {
        $product = Product::factory()->create([
            'settings' => [
                'standard_template' => [
                    'heading' => 'Test Heading',
                    'subheading' => 'Test Subheading',
                    'farming_practices' => 'beef',
                    'related_products' => 'Product1, Product2',
                    'related_recipes' => 'Recipe1, Recipe2',
                    'featured_image_path' => 'http://example.com/image.jpg',
                    'tenderness_score' => 3,
                    'cookability_score' => 4,
                    'cost_score' => 5,
                ],
            ],
        ]);

        $component = Livewire::test(StandardStoreTemplate::class, ['product_id' => $product->id]);

        $component->assertSet('heading', 'Test Heading')
            ->assertSet('subheading', 'Test Subheading')
            ->assertSet('farming_practices', 'beef')
            ->assertSet('related_products', 'Product1, Product2')
            ->assertSet('related_recipes', 'Recipe1, Recipe2')
            ->assertSet('featured_image_path', 'http://example.com/image.jpg')
            ->assertSet('tenderness_score', 3)
            ->assertSet('cookability_score', 4)
            ->assertSet('cost_score', 5);
    }
}
