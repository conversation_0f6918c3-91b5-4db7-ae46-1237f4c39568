<?php

namespace Tests\Feature\Livewire\Admin;

use App\Livewire\Admin\Modals\BulkEditSubscriptions;
use Livewire\Livewire;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class BulkEditSubscriptionsTest extends TenantTestCase
{
    #[Test]
    public function it_renders_successfully(): void
    {
        Livewire::test(BulkEditSubscriptions::class)
            ->assertStatus(200)
            ->assertSee('Bulk Edit Subscriptions')
            ->assertSet('open', false)
            ->assertSet('action', 'date')
            ->assertSet('bulk_selection_type', null)
            ->assertSet('subscription_ids', [])
            ->assertSet('subscription_count', 0)
            ->assertSet('delivery_date', today()->addWeek()->format('Y-m-d'))
            ->assertSet('product_action', 'remove')
            ->assertSet('removed_product_id', null)
            ->assertSet('replacement_product_id', null)
            ->assertSet('expedited_date_confirmation', false);
    }

    #[Test]
    public function it_can_be_opened(): void
    {
        Livewire::test(BulkEditSubscriptions::class)
            ->assertStatus(200)
            ->dispatch(
                'open-modal-bulk-edit-subscriptions',
                bulk_selection_type: null,
                subscription_ids: [1,2],
                subscription_count: 2
            )
            ->assertSet('open', true)
            ->assertSet('bulk_selection_type', null)
            ->assertSet('subscription_ids', [1,2])
            ->assertSet('subscription_count', 2);
    }

    #[Test]
    public function it_can_be_closed(): void
    {
        Livewire::test(BulkEditSubscriptions::class)
            ->assertStatus(200)
            ->dispatch('close-modal-bulk-edit-subscriptions')
            ->assertSet('open', false);
    }

    #[Test]
    public function it_validates_the_bulk_delivery_submission(): void
    {
        Livewire::test(BulkEditSubscriptions::class)
            ->assertStatus(200)
            ->set('action', 'date')
            ->set('bulk_selection_type', 'all')
            ->set('subscription_ids', [])
            ->set('delivery_date', today()->subDays()->format('Y-m-d'))
            ->call('submit')
            ->assertHasErrors([
                'delivery_date' => ['The delivery date field must be a date after or equal to today.'],
            ])
            ->assertNotDispatched(event: 'bulk-action-selected')
            ->set('delivery_date', today()->addDays(5)->format('Y-m-d'))
            ->call('submit')
            ->assertHasErrors([
                'expedited_date_confirmation' => ['The expedited date confirmation field must be accepted.'],
            ])
            ->assertNotDispatched(event: 'bulk-action-selected');
    }

    #[Test]
    public function it_can_submit_bulk_delivery_date_events(): void
    {
        Livewire::test(BulkEditSubscriptions::class)
            ->assertStatus(200)
            ->set('action', 'date')
            ->set('bulk_selection_type', 'all')
            ->set('subscription_ids', [])
            ->set('delivery_date', today()->addDays(7)->format('Y-m-d'))
            ->set('expedited_date_confirmation', false)
            ->call('submit')
            ->assertHasNoErrors()
            ->assertDispatched(
                event: 'bulk-action-selected',
                action: 'date',
                params: [
                    'delivery_date' => today()->addDays(7)->format('Y-m-d'),
                    'bulk_selection_type' =>  'all',
                    'subscription_ids' => [],
                ]
            )
            ->assertSet('open', false);
    }

    #[Test]
    public function it_can_submit_bulk_delivery_date_events_with_expedited_dates(): void
    {
        Livewire::test(BulkEditSubscriptions::class)
            ->assertStatus(200)
            ->set('action', 'date')
            ->set('bulk_selection_type', 'all')
            ->set('subscription_ids', [])
            ->set('delivery_date', today()->addDays(3)->format('Y-m-d'))
            ->set('expedited_date_confirmation', true)
            ->call('submit')
            ->assertHasNoErrors()
            ->assertDispatched(
                event: 'bulk-action-selected',
                action: 'date',
                params: [
                    'delivery_date' => today()->addDays(3)->format('Y-m-d'),
                    'bulk_selection_type' =>  'all',
                    'subscription_ids' => [],
                ]
            )
            ->assertSet('open', false);
    }

    #[Test]
    public function it_validates_the_bulk_product_submission(): void
    {
        Livewire::test(BulkEditSubscriptions::class)
            ->assertStatus(200)
            ->set('action', 'product')
            ->set('bulk_selection_type', 'all')
            ->set('subscription_ids', [])
            ->set('product_action', 'edit')
            ->call('submit')
            ->assertHasErrors([
                'product_action' => ['The selected product action is invalid.'],
                'removed_product_id' => ['The removed product id field is required.'],
            ])
            ->assertNotDispatched(event: 'bulk-action-selected')
            ->set('product_action', 'replace')
            ->call('submit')
            ->assertHasErrors([
                'removed_product_id' => ['The removed product id field is required.'],
                'replacement_product_id' => ['The replacement product id field is required when product action is replace.'],
            ])
            ->assertNotDispatched(event: 'bulk-action-selected')
        ;
    }

    #[Test]
    public function it_can_submit_bulk_product_removal_events(): void
    {
        Livewire::test(BulkEditSubscriptions::class)
            ->assertStatus(200)
            ->set('action', 'product')
            ->set('bulk_selection_type', 'all')
            ->set('product_action', 'remove')
            ->set('subscription_ids', [])
            ->set('removed_product_id', 1)
            ->call('submit')
            ->assertHasNoErrors()
            ->assertDispatched(
                event: 'bulk-action-selected',
                action: 'product',
                params: [
                    'product_action' => 'remove',
                    'removed_product_id' => 1,
                    'replacement_product_id' => null,
                    'bulk_selection_type' => 'all',
                    'subscription_ids' => [],
                ]
            )
            ->assertSet('open', false);
    }

    #[Test]
    public function it_can_submit_bulk_product_replacement_events(): void
    {
        Livewire::test(BulkEditSubscriptions::class)
            ->assertStatus(200)
            ->set('action', 'product')
            ->set('removed_product_id', 1)
            ->set('replacement_product_id', 2)
            ->set('bulk_selection_type', 'all')
            ->set('product_action', 'replace')
            ->set('subscription_ids', [])
            ->call('submit')
            ->assertHasNoErrors()
            ->assertDispatched(
                event: 'bulk-action-selected',
                action: 'product',
                params: [
                    'product_action' => 'replace',
                    'removed_product_id' => 1,
                    'replacement_product_id' => 2,
                    'bulk_selection_type' => 'all',
                    'subscription_ids' => [],
                ]
            )
            ->assertSet('open', false);
    }
}
