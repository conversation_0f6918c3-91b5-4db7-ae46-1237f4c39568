<?php

namespace Tests\Feature\Livewire\Admin;

use App\Livewire\Admin\PackingGroupTable;
use App\Models\PackingGroup as PackingGroupModel;
use Livewire\Livewire;
use PHPUnit\Framework\Attributes\Test;
use Tests\LivewireTenantTestCase;

class PackingGroupTableTest extends LivewireTenantTestCase
{
    #[Test]
    public function it_can_render(): void
    {
        PackingGroupModel::create(['title' => 'Test Group']);

        Livewire::test(PackingGroupTable::class)
            ->assertSee('Test Group')
            ->assertStatus(200);
    }

    #[Test]
    public function it_can_start_editing(): void
    {
        $packingGroup = PackingGroupModel::create(['title' => 'Test Group']);

        Livewire::test(PackingGroupTable::class)
            ->call('startEditing', $packingGroup)
            ->assertSet('editingId', $packingGroup->id)
            ->assertSet('editingTitle', 'Test Group');
    }

    #[Test]
    public function it_can_cancel_editing(): void
    {
        $packingGroup = PackingGroupModel::create(['title' => 'Test Group']);

        Livewire::test(PackingGroupTable::class)
            ->call('startEditing', $packingGroup)
            ->call('cancelEditing')
            ->assertSet('editingId', null)
            ->assertSet('editingTitle', '');
    }

    #[Test]
    public function it_can_update_packing_group(): void
    {
        $packingGroup = PackingGroupModel::create(['title' => 'Test Group']);

        Livewire::test(PackingGroupTable::class)
            ->call('startEditing', $packingGroup)
            ->set('editingTitle', 'Updated Group')
            ->call('update')
            ->assertSet('editingId', null)
            ->assertSet('editingTitle', '')
            ->assertDispatched('packing-group-updated');

        $this->assertDatabaseHas(PackingGroupModel::class, [
            'id' => $packingGroup->id,
            'title' => 'Updated Group'
        ]);
    }

    #[Test]
    public function it_validates_title_uniqueness_on_update(): void
    {
        $packingGroup1 = PackingGroupModel::create(['title' => 'Test Group 1']);
        $packingGroup2 = PackingGroupModel::create(['title' => 'Test Group 2']);

        Livewire::test(PackingGroupTable::class)
            ->call('startEditing', $packingGroup1)
            ->set('editingTitle', 'Test Group 2')
            ->call('update')
            ->assertHasErrors(['editingTitle' => 'unique']);
    }

    #[Test]
    public function it_requires_title_on_update(): void
    {
        $packingGroup = PackingGroupModel::create(['title' => 'Test Group']);

        Livewire::test(PackingGroupTable::class)
            ->call('startEditing', $packingGroup)
            ->set('editingTitle', '')
            ->call('update')
            ->assertHasErrors(['editingTitle' => 'required']);
    }

    #[Test]
    public function it_handles_packing_group_saved_event(): void
    {
        Livewire::test(PackingGroupTable::class)
            ->call('handlePackingGroupCreated')
            ->assertDispatched('refresh');
    }

    #[Test]
    public function it_paginates_packing_groups(): void
    {
        PackingGroupModel::factory(30)->create();

        $component = Livewire::test(PackingGroupTable::class);
        
        $this->assertEquals(25, $component->viewData('packingGroups')->count());
        $this->assertTrue($component->viewData('packingGroups')->hasPages());
    }
} 