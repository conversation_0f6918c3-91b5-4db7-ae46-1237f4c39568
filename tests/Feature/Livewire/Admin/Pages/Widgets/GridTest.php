<?php

namespace Tests\Feature\Livewire\Admin\Pages\Widgets;

use App\Livewire\Admin\Pages\Widgets\Grid;
use App\Models\Page;
use Livewire\Livewire;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class GridTest extends TenantTestCase
{
    #[Test]
    public function it_renders_successfully(): void
    {
        Livewire::test(Grid::class, [
            'page_id' => 1,
            'widget' => [
                'id' => 'abc-def',
                'type' => 'grid',
                'settings' => [
                    'name' => 'Grid Widget Name',
                    'html_id' => 'test-grid',
                    'max_width' => 'none',
                    'padding' => [
                        'top' => 'sm',
                        'bottom' => 'sm'
                    ],
                    'type' => 'blog',
                    'columns' => 3,
                    'item_ids' => '1,2,3',
                ]
            ]
        ])
            ->assertStatus(200)
            ->assertSet('name', 'Grid Widget Name')
            ->assertSet('html_id', 'test-grid')
            ->assertSet('max_width', 'none')
            ->assertSet('padding_top', 'sm')
            ->assertSet('padding_bottom', 'sm')
            ->assertSet('type', 'blog')
            ->assertSet('columns', 3)
            ->assertSet('item_ids', '1,2,3');
    }

    #[Test]
    public function it_can_update_the_widget_settings(): void
    {
        $widget = [
            'id' => 'abc-def',
            'type' => 'grid',
            'settings' => [
                'name' => 'Grid Widget Name',
                'html_id' => 'test-grid',
                'max_width' => 'none',
                'padding' => [
                    'top' => 'sm',
                    'bottom' => 'sm'
                ],
                'type' => 'blog',
                'columns' => 3,
                'item_ids' => '1,2,3',
            ],
        ];

        $page = Page::factory()->create([
            'settings' => [
                'type' => 'promo',
                'content' => [$widget]
            ]
        ]);

        Livewire::test(Grid::class, [
            'page_id' => $page->id,
            'widget' => $widget,
        ])
            ->assertStatus(200)
            ->set('name', 'Grid Widget Name 2')
            ->set('html_id', 'abc')
            ->set('max_width', 'lg')
            ->set('padding_top', 'lg')
            ->set('padding_bottom', 'lg')
            ->set('type', 'recipe')
            ->set('columns', 4)
            ->set('item_ids', '4,5,6,7')
            ->call('save')
            ->assertHasNoErrors();

        $this->assertDatabaseHas(Page::class, [
            'id' => $page->id,
            'settings' => json_encode([
                'type' => 'promo',
                'content' => [
                    [
                        'id' => 'abc-def',
                        'type' => 'grid',
                        'settings' => [
                            'name' => 'Grid Widget Name 2',
                            'html_id' => 'abc',
                            'max_width' => 'lg',
                            'padding' => [
                                'top' => 'lg',
                                'bottom' => 'lg'
                            ],
                            'type' => 'recipe',
                            'columns' => 4,
                            'item_ids' => '4,5,6,7',
                        ]
                    ]
                ]
            ])
        ]);
    }

    #[Test]
    public function it_dispatches_an_event_on_successful_save(): void
    {
        $widget = [
            'id' => 'abc-def',
            'type' => 'grid',
            'settings' => [
                'name' => 'Grid Widget Name',
                'html_id' => 'test-grid',
                'max_width' => 'none',
                'padding' => [
                    'top' => 'sm',
                    'bottom' => 'sm'
                ],
                'type' => 'blog',
                'columns' => 3,
                'item_ids' => '1,2,3',
            ],
        ];

        $page = Page::factory()->create([
            'settings' => [
                'type' => 'promo',
                'content' => [$widget]
            ]
        ]);

        Livewire::test(Grid::class, [
            'page_id' => $page->id,
            'widget' => $widget,
        ])
            ->assertStatus(200)
            ->call('save')
            ->assertHasNoErrors()
            ->assertDispatched('admin-notification-sent', notification: [
                'level' => 'success',
                'title' => 'Widget updated!',
                'message' => 'The widget settings have been successfully updated.',
                'duration' => 3000
            ]);
    }
}
