<?php

namespace Tests\Feature\Livewire\Theme;

use App\Actions\Billing\SetDefaultCard;
use App\Contracts\Billing;
use App\Livewire\Theme\SubscriptionPaymentMethod;
use App\Models\Card;
use App\Models\Order;
use App\Models\Payment;
use App\Models\RecurringOrder;
use Livewire\Livewire;
use Mockery;
use Mockery\MockInterface;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class SubscriptionPaymentMethodTest extends TenantTestCase
{
    #[Test]
    public function renders_successfully_user_has_subscription()
    {
        $this->mock(Billing::class, function (MockInterface $mock) {
            $mock->shouldNotReceive('retrieveUserPaymentMethods');
        });

        Livewire::test(SubscriptionPaymentMethod::class, [
            'subscription_id' => RecurringOrder::factory()->create()->id
        ])
            ->assertStatus(200);
    }

    #[Test]
    public function renders_successfully_user_has_subscription_and_its_order_with_card_payment()
    {
        $payments = Payment::firstOrCreate(['key'=> 'card']);

        $subscription = RecurringOrder::factory()->create();

        $order = Order::factory()->create([
            'blueprint_id' => $subscription->id,
            'customer_id' => $subscription->customer_id,
            'payment_id' => $payments->id,
        ]);

        $this->mock(Billing::class, function (MockInterface $mock) {
            $mock->shouldReceive('retrieveUserPaymentMethods')->once()
                ->andReturn(collect([]));
        });

        Livewire::test(SubscriptionPaymentMethod::class, [
            'subscription_id' => $subscription->id,
            'current_order_id' => $order->id,
        ])
            ->assertStatus(200);
    }

    #[Test]
    public function it_update_subscription_payment_method()
    {
        $payments = Payment::firstOrCreate(['key'=> 'card']);

        $subscription = RecurringOrder::factory()->create();

        $old_card = Card::factory()->create(['user_id' => $subscription->customer_id]);
        $new_card = Card::factory()->create(['user_id' => $subscription->customer_id]);

        $order = Order::factory()->create([
            'blueprint_id' => $subscription->id,
            'customer_id' => $subscription->customer_id,
            'payment_id' => $payments->id,
            'payment_source_id' => $old_card->id,
        ]);

        $this->mock(Billing::class, function (MockInterface $mock) {
            $mock->shouldReceive('retrieveUserPaymentMethods')
                ->andReturn(collect([]));
        });

        $this->mock(SetDefaultCard::class, function (MockInterface $mock) use ($new_card) {
            $mock->shouldReceive('handle')->once()
                ->with(Mockery::on(fn(Card $arg) => $arg->id === $new_card->id))
                ->andReturn($new_card);
        });

        Livewire::actingAs($subscription->customer)
            ->test(SubscriptionPaymentMethod::class, [
                'subscription_id' => $subscription->id,
                'current_order_id' => $order->id,
            ])
            ->assertStatus(200)
            ->assertSet('payment_source_id', $old_card->source_id)
            ->set('payment_source_id', $new_card->source_id)
            ->assertSet('payment_source_id', $new_card->source_id)
            ->call('updatePaymentSource')
            ->assertSessionDoesntHaveErrors()
            ->assertStatus(200)
            ->assertDispatched('storefront-notification-sent', notification: [
                'level' => 'success',
                'title' => 'Success!',
                'message' => 'Your subscription payment method has been updated!',
                'duration' => 3000
            ])
        ;
    }
}

