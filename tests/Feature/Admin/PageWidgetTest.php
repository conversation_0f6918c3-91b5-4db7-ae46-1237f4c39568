<?php

namespace Tests\Feature\Admin;

use App\Models\Page;
use App\Models\Widget;
use Illuminate\Support\Carbon;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class PageWidgetTest extends TenantTestCase
{
    #[Test]
    public function a_guest_cannot_create_a_page_widget(): void
    {
        $page = Page::factory()->create();

        $this->post(route('admin.pages.widgets.store', compact('page')))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function a_non_admin_cannot_create_a_page_widget(): void
    {
        $page = Page::factory()->create();

        $this->actingAsCustomer()
            ->post(route('admin.pages.widgets.store', compact('page')))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function a_non_admin_cannot_create_a_page_widget_for_an_invalid_page(): void
    {
        $page = Page::factory()->create();

        $this->actingAsCustomer()
            ->post(route('admin.pages.widgets.store', compact('page')))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function it_validates_the_create_request(): void
    {
        $page = Page::factory()->create();

        $this->actingAsAdmin()
            ->post(route('admin.pages.widgets.store', compact('page')))
            ->assertInvalid([
                'id' => 'The id field is required.',
                'sort' => 'The sort field is required.',
            ]);
    }

    #[Test]
    public function an_admin_can_create_rich_text_widget(): void
    {
        $page = Page::factory()->create();

        $this->actingAsAdmin()
            ->post(route('admin.pages.widgets.store', compact('page')), [
                'id' => 'RichText',
                'content' => 'My content',
                'sort' => 3
            ])
            ->assertSessionHasNoErrors()
            ->assertJsonStructure(['block']);

        $this->assertDatabaseHas(Widget::class, [
            'page_id' => $page->id,
            'container_id' => null,
            'enabled' => 1,
            'description' => null,
            'visible' => 1,
            'template' => 'RichText',
            'content' => '',
            'sort' => 3
        ]);
    }

    #[Test]
    public function an_admin_can_create_HTML_widget(): void
    {
        $page = Page::factory()->create();

        $this->actingAsAdmin()
            ->post(route('admin.pages.widgets.store', compact('page')), [
                'id' => 'HTML',
                'content' => 'My content',
                'sort' => 3
            ])
            ->assertSessionHasNoErrors()
           ->assertJsonStructure(['block']);

        $this->assertDatabaseHas(Widget::class, [
            'page_id' => $page->id,
            'container_id' => null,
            'enabled' => 1,
            'description' => null,
            'visible' => 1,
            'template' => 'HTML',
            'sort' => 3
        ]);

        // content is overridden with view
        $this->assertDatabaseMissing(Widget::class, [
            'content' => 'My HTML content',
        ]);
    }

    #[Test]
    public function an_admin_can_create_Newsletter_widget(): void
    {
        $page = Page::factory()->create();

        $this->actingAsAdmin()
            ->post(route('admin.pages.widgets.store', compact('page')), [
                'id' => 'Newsletter',
                'content' => 'My content',
                'sort' => 3
            ])
            ->assertSessionHasNoErrors()
           ->assertJsonStructure(['block']);

        $this->assertDatabaseHas(Widget::class, [
            'page_id' => $page->id,
            'container_id' => null,
            'enabled' => 1,
            'description' => null,
            'visible' => 1,
            'template' => 'Newsletter',
            'sort' => 3
        ]);

        // content is overridden with view
        $this->assertDatabaseMissing(Widget::class, [
            'content' => 'My Newsletter content',
        ]);
    }

    #[Test]
    public function an_admin_can_create_Divider_widget(): void
    {
        $page = Page::factory()->create();

        $this->actingAsAdmin()
            ->post(route('admin.pages.widgets.store', compact('page')), [
                'id' => 'Divider',
                'content' => 'My content',
                'sort' => 3
            ])
            ->assertSessionHasNoErrors()
           ->assertJsonStructure(['block']);

        $this->assertDatabaseHas(Widget::class, [
            'page_id' => $page->id,
            'container_id' => null,
            'enabled' => 1,
            'description' => null,
            'visible' => 1,
            'template' => 'Divider',
            'sort' => 3
        ]);

        // content is overridden with view
        $this->assertDatabaseMissing(Widget::class, [
            'content' => 'My Divider content',
        ]);
    }

    #[Test]
    public function a_guest_cannot_update_a_page_widget(): void
    {
        $page = Page::factory()->create();

        $widget = Widget::factory()->create(['page_id' => $page->id]);

        $this->put(route('admin.pages.widgets.update', compact('page', 'widget')))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function a_non_admin_cannot_update_a_page_widget(): void
    {
        $page = Page::factory()->create();

        $widget = Widget::factory()->create(['page_id' => $page->id]);

        $this->actingAsCustomer()
            ->put(route('admin.pages.widgets.update', compact('page', 'widget')))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function an_admin_cannot_update_an_invalid_page_widget(): void
    {
        $page = Page::factory()->create();

        $this->actingAsAdmin()
            ->put(route('admin.pages.widgets.update', [$page->id, 'something']))
            ->assertRedirect();
    }

    #[Test]
    public function an_admin_cannot_update_an_invalid_page_widget_combo(): void
    {
        $page = Page::factory()->create();
        $widget = Page::factory()->create();

        $this->actingAsAdmin()
            ->put(route('admin.pages.widgets.update', [$page->id, $widget->id]))
            ->assertRedirect();
    }

    #[Test]
    public function it_validates_the_update_request(): void
    {
        $page = Page::factory()->create();

        $widget = Widget::factory()->create(['page_id' => $page->id]);

        $this->actingAsAdmin()
            ->put(route('admin.pages.widgets.update', compact('page', 'widget')))
            ->assertInvalid([
                'title' => 'The title field is required.',
            ]);

        $this->put(route('admin.pages.widgets.update', compact('page', 'widget')), ['settings' => 'seomething'])
            ->assertInvalid(['settings' => 'The settings field must be an array.']);
    }

    #[Test]
    public function an_admin_can_update_rich_text_widget(): void
    {
        $page = Page::factory()->create();

        $widget = Widget::factory()->create(['page_id' => $page->id, 'template' => 'RichText',]);

        $settings = ['foo' => 'bar'];

        $this->actingAsAdmin()
            ->put(route('admin.pages.widgets.update', compact('page', 'widget')), [
                'title' => 'The rich text',
                'content' => 'My content',
                'settings' => $settings
            ])
            ->assertOk()
            ->assertSessionHasNoErrors()
            ->assertJsonStructure(['block', 'page_id']);

        $this->assertDatabaseHas(Widget::class, [
            'page_id' => $page->id,
            'container_id' => null,
            'enabled' => (int) $widget->enabled,
            'title' => 'The rich text',
            'description' => $widget->description,
            'visible' => 1,
            'template' => 'RichText',
            'content' => 'My content',
            'settings' => json_encode(array_merge((array) $widget->settings, $settings)),
            'sort' => $widget->sort
        ]);
    }

    #[Test]
    public function an_admin_can_update_HTML_widget(): void
    {
        $page = Page::factory()->create();

        $widget = Widget::factory()->create(['page_id' => $page->id, 'template' => 'HTML']);

        $settings = ['foo' => 'bar'];

        $this->actingAsAdmin()
            ->put(route('admin.pages.widgets.update', compact('page', 'widget')), [
                'title' => 'The HTML',
                'content' => 'My HTML content',
                'settings' => $settings
            ])
            ->assertOk()
            ->assertSessionHasNoErrors()
            ->assertJsonStructure(['block', 'page_id']);

        $this->assertDatabaseHas(Widget::class, [
            'page_id' => $page->id,
            'container_id' => null,
            'enabled' => (int) $widget->enabled,
            'title' => 'The HTML',
            'description' => $widget->description,
            'visible' => 1,
            'template' => 'HTML',
            'settings' => json_encode(array_merge((array) $widget->settings, $settings)),
            'sort' => $widget->sort,
            'content' => 'My HTML content',
        ]);
    }

    #[Test]
    public function an_admin_can_update_Newsletter_widget(): void
    {
        $page = Page::factory()->create();

        $widget = Widget::factory()->create(['page_id' => $page->id, 'template' => 'Newsletter']);

        $settings = ['foo' => 'bar'];

        $this->actingAsAdmin()
            ->put(route('admin.pages.widgets.update', compact('page', 'widget')), [
                'title' => 'The Newsletter',
                'content' => 'My Newsletter content',
                'settings' => $settings
            ])
            ->assertOk()
            ->assertSessionHasNoErrors()
            ->assertJsonStructure(['block', 'page_id']);

        $this->assertDatabaseHas(Widget::class, [
            'page_id' => $page->id,
            'container_id' => null,
            'enabled' => (int) $widget->enabled,
            'title' => 'The Newsletter',
            'description' => $widget->description,
            'visible' => 1,
            'template' => 'Newsletter',
            'settings' => json_encode(array_merge((array) $widget->settings, $settings)),
            'sort' => $widget->sort,
            'content' => 'My Newsletter content',
        ]);
    }

    #[Test]
    public function an_admin_can_update_Divider_widget(): void
    {
        $page = Page::factory()->create();

        $widget = Widget::factory()->create(['page_id' => $page->id, 'template' => 'Divider']);

        $settings = ['foo' => 'bar'];

        $this->actingAsAdmin()
            ->put(route('admin.pages.widgets.update', compact('page', 'widget')), [
                'title' => 'The Divider',
                'content' => 'My Divider content',
                'settings' => $settings
            ])
            ->assertOk()
            ->assertSessionHasNoErrors()
            ->assertJsonStructure(['block', 'page_id']);

        $this->assertDatabaseHas(Widget::class, [
            'page_id' => $page->id,
            'container_id' => null,
            'enabled' => (int) $widget->enabled,
            'title' => 'The Divider',
            'description' => $widget->description,
            'visible' => 1,
            'template' => 'Divider',
            'settings' => json_encode(array_merge((array) $widget->settings, $settings)),
            'sort' => $widget->sort,
            'content' => 'My Divider content',
        ]);
    }

    #[Test]
    public function a_guest_cannot_destroy_a_page_widget(): void
    {
        $page = Page::factory()->create();

        $widget = Widget::factory()->create(['page_id' => $page->id]);

        $this->delete(route('admin.pages.widgets.destroy', compact('page', 'widget')))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function a_non_admin_cannot_destroy_a_page_widget(): void
    {
        $page = Page::factory()->create();

        $widget = Widget::factory()->create(['page_id' => $page->id]);

        $this->actingAsCustomer()
            ->delete(route('admin.pages.widgets.destroy', compact('page', 'widget')))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function an_admin_cannot_destroy_an_invalid_page_widget(): void
    {
        $page = Page::factory()->create();

        $this->actingAsAdmin()
            ->delete(route('admin.pages.widgets.destroy', [$page->id, 'something']))
            ->assertRedirect();
    }

    #[Test]
    public function an_admin_cannot_destroy_an_invalid_page_widget_combo(): void
    {
        $page = Page::factory()->create();
        $widget = Widget::factory()->create();

        $this->actingAsAdmin()
            ->delete(route('admin.pages.widgets.destroy', [$page->id, $widget->id]))
            ->assertRedirect();
    }

    #[Test]
    public function an_admin_can_destroy_a_page_widget(): void
    {
        $page = Page::factory()->create();

        Carbon::setTestNow(now());

        $widget = Widget::factory()->create(['page_id' => $page->id, 'deleted_at' => null]);

        $this->actingAsAdmin()
            ->delete(route('admin.pages.widgets.destroy', compact('page', 'widget')))
            ->assertOk()
            ->assertSessionHasNoErrors()
            ->assertExactJson(['page_id' => $page->id]);

        $this->assertDatabaseHas(Widget::class, [
            'id' => $widget->id,
            'deleted_at' => now()
        ]);

        Carbon::setTestNow();
    }
}