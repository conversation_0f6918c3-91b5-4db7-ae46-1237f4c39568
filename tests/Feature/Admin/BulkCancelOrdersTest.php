<?php

namespace Tests\Feature\Admin;

use App\Jobs\CancelOrders;
use App\Models\Order;
use Illuminate\Support\Facades\Bus;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class BulkCancelOrdersTest extends TenantTestCase
{
    #[Test]
    public function a_guest_cannot_bulk_cancel_orders(): void
    {
        $this->put(route('admin.orders.bulk-cancel'))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function a_non_admin_cannot_bulk_cancel_orders(): void
    {
        $this->actingAsCustomer()
            ->put(route('admin.orders.bulk-cancel'))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function it_validates_the_request(): void
    {
        Bus::fake([CancelOrders::class]);

        $this->actingAsAdmin()
            ->put(route('admin.orders.bulk-cancel'))
            ->assertRedirect()
            ->assertInvalid(['orders' => 'Please select at least one order.']);

        $this->put(route('admin.orders.bulk-cancel'), ['orders' => ''])
            ->assertRedirect()
            ->assertInvalid(['orders' => 'Please select at least one order.']);

        $this->put(route('admin.orders.bulk-cancel'), ['orders' => []])
            ->assertRedirect()
            ->assertInvalid(['orders' => 'Please select at least one order.']);

        $this->put(route('admin.orders.bulk-cancel'), ['orders' => '1'])
            ->assertRedirect()
            ->assertInvalid(['orders' => 'The orders field must be an array.']);

        $order_ids = Order::factory(2)->create(['canceled_at' => now()])->pluck('id');

        $this->put(route('admin.orders.bulk-cancel'), ['orders' => $order_ids->toArray()])
            ->assertRedirect()
            ->assertInvalid(['orders' => "Orders have already been canceled: {$order_ids->implode(', ')}"]);

        Bus::assertNotDispatched(CancelOrders::class);
    }

    #[Test]
    public function an_admin_can_bulk_cancel_orders(): void
    {
        Bus::fake([CancelOrders::class]);

        $orders = Order::factory(2)->create(['confirmed' => 1, 'canceled_at' => null]);

        $this->actingAsAdmin()
            ->put(route('admin.orders.bulk-cancel'), ['orders' => $orders->pluck('id')->toArray()])
            ->assertRedirect(route('admin.orders.index'))
            ->assertSessionDoesntHaveErrors()
            ->assertSessionHas('flash_notification', [
                'message' => '2 orders are now in the process of being canceled.',
                'level' => 'info'
            ]);

        Bus::assertDispatched(
            CancelOrders::class,
            fn(CancelOrders $job) => $job->order_ids === $orders->pluck('id')->toArray()
        );
    }
}
