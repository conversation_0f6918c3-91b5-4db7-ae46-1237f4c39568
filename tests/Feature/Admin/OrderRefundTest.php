<?php

namespace Tests\Feature\Admin;

use App\Billing\Gateway\Transaction;
use App\Contracts\Billing;
use App\Models\Card;
use App\Models\Order;
use App\Models\OrderPayment;
use App\Models\Refund;
use App\Models\User;
use Mo<PERSON>y\MockInterface;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class OrderRefundTest extends TenantTestCase
{
    #[Test]
    public function it_cannot_issue_a_for_an_order_that_doesnt_exist(): void
    {
        $payment = OrderPayment::factory()->create(['amount' => 5000]);

        $this->mock(Billing::class, function (MockInterface $mock) {
            $mock->shouldNotReceive('refundPayment');
        });

        $this->actingAsAdmin()
            ->post(route('admin.payments.refunds.store', [12345234678, $payment]))
            ->assertRedirect();
    }

    #[Test]
    public function it_cannot_issue_a_for_a_payment_that_doesnt_exist(): void
    {
        $order = Order::factory()->create();

        $this->mock(Billing::class, function (MockInterface $mock)  {
            $mock->shouldNotReceive('refundPayment');
        });

        $this->actingAsAdmin()
            ->post(route('admin.payments.refunds.store', [$order, 12345234678]))
            ->assertRedirect();
    }

    #[Test]
    public function it_cannot_issue_a_refund_for_an_amount_greater_than_the_payment_amount(): void
    {
        $order = Order::factory()->create();
        $payment = OrderPayment::factory()->create(['order_id' => $order->id, 'amount' => 5000]);

        $this->mock(Billing::class, function (MockInterface $mock) use ($payment) {
            $mock->shouldNotReceive('refundPayment');
        });

        $this->actingAsAdmin()
            ->post(route('admin.payments.refunds.store', [$order, $payment]), [
                'amount' => '50.01'
            ])
            ->assertRedirect()
            ->assertInvalid(['amount' => 'The refund amount can not be greater than the payment amount.']);
    }

    #[Test]
    public function it_can_issue_a_partial_refund(): void
    {
        $card = Card::factory()->create();
        $order = Order::factory()->create();
        $payment = OrderPayment::factory()->create([
            'order_id' => $order->id,
            'amount' => 5000,
            'source_id' => $card->source_id
        ]);
        $admin = User::factory()->admin()->create();

        $this->mock(Billing::class, function (MockInterface $mock) use ($payment, $admin) {
            $mock->shouldReceive('refundPayment')
                ->once()
                ->with(
                    \Mockery::on(fn(OrderPayment $arg) => $arg->id === $payment->id),
                    1234,
                    'test'
                )->andReturn(new Transaction(
                    type: Transaction::TYPE_REFUND,
                    id: 'some_id',
                    amount: 1234,
                    success: true,
                    source_id: $payment->source_id,
                    payment_type: $payment->payment_type,
                    description: 'test'
                ));
        });


        $this->actingAs($admin)
            ->post(route('admin.payments.refunds.store', [$order, $payment]), [
                'amount' => '12.34',
                'description' => 'test'
            ])
            ->assertOk()
            ->assertJsonFragment([
                'responseText' => 'A refund of &#36;' . money(1234) . ' was added.'
            ]);

        $this->assertDatabaseHas(Refund::class, [
            'payment_id' => $payment->id,
            'admin_id' => $admin->id,
            'refund_id' => 'some_id',
            'amount' => 1234,
            'status' => 'succeeded',
            'reason' => 'test'
        ]);
    }

    #[Test]
    public function it_issues_a_full_refund_when_an_amount_is_not_specified(): void
    {
        $card = Card::factory()->create();
        $order = Order::factory()->create();
        $payment = OrderPayment::factory()->create([
            'order_id' => $order->id,
            'amount' => 5000,
            'source_id' => $card->source_id
        ]);
        $admin = User::factory()->admin()->create();

        $this->mock(Billing::class, function (MockInterface $mock) use ($payment, $admin) {
            $mock->shouldReceive('refundPayment')
                ->once()
                ->with(
                    \Mockery::on(fn(OrderPayment $arg) => $arg->id === $payment->id),
                    null,
                    null
                )->andReturn(new Transaction(
                    type: Transaction::TYPE_REFUND,
                    id: 'some_id',
                    amount: 5000,
                    success: true,
                    source_id: $payment->source_id,
                    payment_type: $payment->payment_type,
                    description: null
                ));
        });


        $this->actingAs($admin)
            ->post(route('admin.payments.refunds.store', [$order, $payment]))
            ->assertOk()
            ->assertJsonFragment([
                'responseText' => 'A refund of &#36;' . money(5000) . ' was added.'
            ]);

        $this->assertDatabaseHas(Refund::class, [
            'payment_id' => $payment->id,
            'admin_id' => $admin->id,
            'refund_id' => 'some_id',
            'amount' => 5000,
            'status' => 'succeeded',
            'reason' => null
        ]);
    }

    #[Test]
    public function it_requires_an_amount_less_than_the_payment_amount_when_full_refund_is_set_to_false(): void
    {
        $order = Order::factory()->create();
        $payment = OrderPayment::factory()->create(['order_id' => $order->id, 'amount' => 5000]);
        $admin = User::factory()->admin()->create();

        $this->mock(Billing::class, function (MockInterface $mock) use ($payment, $admin) {
            $mock->shouldNotReceive('refundPayment');
        });

        $this->actingAs($admin)
            ->post(route('admin.payments.refunds.store', [
                $order,
                $payment
            ]), [
                'full_refund' => false,
                'amount' => 5001,
                ])
            ->assertRedirect()
            ->assertInvalid(['amount' => 'The refund amount can not be greater than the payment amount.']);


        $this->assertDatabaseMissing(Refund::class, [
            'payment_id' => $payment->id,
            'admin_id' => $admin->id,
            'refund_id' => 'some_id',
            'amount' => 5000,
            'status' => 'complete',
            'reason' => null
        ]);
    }

    #[Test]
    public function it__does_not_require_an_amount_less_than_the_payment_amount_when_full_refund_is_set_to_true(): void
    {
        $card = Card::factory()->create();
        $order = Order::factory()->create();
        $payment = OrderPayment::factory()->create([
            'order_id' => $order->id,
            'amount' => 5000,
            'source_id' => $card->source_id
        ]);
        $admin = User::factory()->admin()->create();

        $this->mock(Billing::class, function (MockInterface $mock) use ($payment, $admin) {
            $mock->shouldReceive('refundPayment')
                ->once()
                ->with(
                    \Mockery::on(fn(OrderPayment $arg) => $arg->id === $payment->id),
                    null,
                    null
                )->andReturn(new Transaction(
                    type: Transaction::TYPE_REFUND,
                    id: 'some_id',
                    amount: 5000,
                    success: true,
                    source_id: $payment->source_id,
                    payment_type: $payment->payment_type,
                    description: null
                ));
        });

        $this->actingAs($admin)
            ->post(route('admin.payments.refunds.store', [
                $order,
                $payment
            ]), [
                'full_refund' => true,
                'amount' => 0,
            ])
            ->assertOk()
            ->assertSessionHasNoErrors();


        $this->assertDatabaseHas(Refund::class, [
            'payment_id' => $payment->id,
            'admin_id' => $admin->id,
            'refund_id' => 'some_id',
            'amount' => 5000,
            'status' => 'succeeded',
            'reason' => null
        ]);
    }


    #[Test]
    public function it_refunds_the_amount_specified_when_full_refund_is_set_to_false(): void
    {
        $card = Card::factory()->create();
        $order = Order::factory()->create();
        $payment = OrderPayment::factory()->create([
            'order_id' => $order->id,
            'amount' => 5000,
            'source_id' => $card->source_id
        ]);
        $admin = User::factory()->admin()->create();

        $this->mock(Billing::class, function (MockInterface $mock) use ($payment, $admin) {
            $mock->shouldReceive('refundPayment')
                ->once()
                ->with(
                    \Mockery::on(fn(OrderPayment $arg) => $arg->id === $payment->id),
                    1234,
                    null
                )->andReturn(new Transaction(
                    type: Transaction::TYPE_REFUND,
                    id: 'some_id',
                    amount: 1234,
                    success: true,
                    source_id: $payment->source_id,
                    payment_type: $payment->payment_type,
                    description: null
                ));
        });

        $this->actingAs($admin)
            ->post(route('admin.payments.refunds.store', [
                $order,
                $payment
            ]), [
                'full_refund' => false,
                'amount' => '12.34',
            ])
            ->assertOk()
            ->assertSessionHasNoErrors();


        $this->assertDatabaseHas(Refund::class, [
            'payment_id' => $payment->id,
            'admin_id' => $admin->id,
            'refund_id' => 'some_id',
            'amount' => 1234,
            'status' => 'succeeded',
            'reason' => null
        ]);
    }

    #[Test]
    public function it_refunds_the_amount_specified_when_full_refund_is_not_set(): void
    {
        $card = Card::factory()->create();
        $order = Order::factory()->create();
        $payment = OrderPayment::factory()->create([
            'order_id' => $order->id,
            'amount' => 5000,
            'source_id' => $card->source_id
        ]);
        $admin = User::factory()->admin()->create();

        $this->mock(Billing::class, function (MockInterface $mock) use ($payment, $admin) {
            $mock->shouldReceive('refundPayment')
                ->once()
                ->with(
                    \Mockery::on(fn(OrderPayment $arg) => $arg->id === $payment->id),
                    1234,
                    null
                )->andReturn(new Transaction(
                    type: Transaction::TYPE_REFUND,
                    id: 'some_id',
                    amount: 1234,
                    success: true,
                    source_id: $payment->source_id,
                    payment_type: $payment->payment_type,
                    description: null
                ));
        });

        $this->actingAs($admin)
            ->post(route('admin.payments.refunds.store', [
                $order,
                $payment
            ]), [
                'amount' => '12.34',
            ])
            ->assertOk()
            ->assertSessionHasNoErrors();


        $this->assertDatabaseHas(Refund::class, [
            'payment_id' => $payment->id,
            'admin_id' => $admin->id,
            'refund_id' => 'some_id',
            'amount' => 1234,
            'status' => 'succeeded',
            'reason' => null
        ]);
    }
}
