<?php

namespace Tests\Feature\Theme\Account;

use App\Models\User;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class CustomerControllerTest extends TenantTestCase
{
    #[Test]
    public function test_email_alt_is_not_required_to_update_customer_profile(): void
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'email_alt' => null,
        ]);

        $this->actingAs($user)
            ->put(route('customer.update'), $user->toArray())
            ->assertSessionHasNoErrors();
    }
}