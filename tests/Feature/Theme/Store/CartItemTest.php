<?php

namespace Tests\Feature\Theme\Store;

use App\Cart\Item;
use App\Contracts\CartService;
use App\Events\Cart\CartCreated;
use App\Models\Cart;
use App\Models\Product;
use App\Models\User;
use Illuminate\Support\Facades\Event;
use Mo<PERSON>y\MockInterface;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class CartItemTest extends TenantTestCase
{
    #[Test]
    public function a_guest_cannot_add_an_item_to_cart(): void
    {
        $this->post(route('cart.items.store'))
            ->assertRedirect(route('login'));
    }

    #[Test]
    public function it_validates_the_request_when_adding_item_to_cart(): void
    {
        $user = User::factory()->create();

        $this->actingAs($user)
            ->get(route('store.index'))
            ->assertOk();

        $this->post(route('cart.items.store'))
            ->assertRedirect(route('store.index'))
            ->assertSessionHasErrors([
                'product_id' => 'The product id field is required.'
            ]);

        $this->post(route('cart.items.store'), [
            'product_id' => 'abc'
        ])
            ->assertRedirect(route('store.index'))
            ->assertSessionHasErrors(['product_id' => 'The selected product id is invalid.']);

        $product = Product::factory()->create();
        $this->post(route('cart.items.store'), [
            'product_id' => $product->id
        ])
            ->assertRedirect(route('store.index'))
            ->assertSessionDoesntHaveErrors(['product_id']);
    }

    #[Test]
    public function a_user_can_add_an_product_to_their_cart(): void
    {
        Event::fake([CartCreated::class]);

        $user = User::factory()->create();

        /** @var Product $product */
        $product = Product::factory()->create();

        $this->actingAs($user)
            ->get(route('store.index'))
            ->assertOk();

        $cart = new Cart;
        $cart_item = new Item('some_id', $product, $product->getUnitPrice(), 1);

        $this->mock(CartService::class, function (MockInterface $mock) use ($user, $cart, $cart_item, $product) {
            $mock->shouldReceive('find')
                ->with(User::class, (string) $user->id)
                ->andReturn($cart);
        });

        $this->post(route('cart.items.store'), [
            'product_id' => $product->id,
            'quantity' => 1
        ])
            ->assertRedirect(route('store.index'))
            ->assertSessionDoesntHaveErrors();
    }
}