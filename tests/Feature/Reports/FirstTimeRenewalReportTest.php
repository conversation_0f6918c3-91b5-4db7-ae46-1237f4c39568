<?php

namespace Tests\Feature\Reports;

use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class FirstTimeRenewalReportTest extends TenantTestCase
{
    #[Test]
    public function an_guest_cannot_view_the_first_time_renewal_report(): void
    {
        $this->get(route('admin.reports.first-time-renewal'))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function a_non_admin_cannot_view_the_first_time_renewal_report(): void
    {
        $this->actingAsCustomer()
            ->get(route('admin.reports.first-time-renewal'))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function an_admin_can_view_the_first_time_renewal_report(): void
    {
        $this->actingAsAdmin()
            ->get(route('admin.reports.first-time-renewal'))
            ->assertOk()
            ->assertViewIs('reports.first-time-renewal.index');
    }
}
