<?php

namespace Tests\Feature\API;

use App\Contracts\Billing;
use App\Models\Card;
use App\Models\User;
use Mo<PERSON>y\MockInterface;
use PHPUnit\Framework\Attributes\Test;
use Stripe\PaymentMethod;
use Stripe\SetupIntent;
use Tests\TenantTestCase;

class UserPaymentMethodsTest extends TenantTestCase
{
    #[Test]
    public function it_requires_an_authenticated_user_to_store_a_user_payment_method(): void
    {
        $user = User::factory()->create();

        $this->postJson(route('api.users.payment-methods.store', compact('user')))
            ->assertUnauthorized();
    }

    #[Test]
    public function it_requires_valid_user_to_store_a_user_payment_method(): void
    {
        $this->actingAsApiCustomer()
            ->postJson(route('api.users.payment-methods.store', ['user' => '123']))
            ->assertNotFound();
    }

    #[Test]
    public function it_validates_the_store_user_payment_method_for_stripe(): void
    {
        $user = User::factory()->create();

        $this->actingAsApiCustomer()
            ->postJson(route('api.users.payment-methods.store', compact('user')))
            ->assertUnprocessable()
            ->assertJsonFragment(['setup_intent_id' => ['The setup intent id field is required.']]);
    }

    #[Test]
    public function it_can_store_a_user_stripe_payment_method(): void
    {
        $user = User::factory()->create();

        $expected_setup_intent = SetupIntent::constructFrom(['id' => 'seti_123', 'payment_method' => 'pm_123', 'customer' => 'cus_123']);
        $expected_payment_method = PaymentMethod::constructFrom([
            'id' => 'pm_123',
            'card' => [
                'brand' => 'visa',
                'exp_month' => '06',
                'exp_year' => '12',
                'last4' => '9999',
            ]
        ]);

        $this->mock(Billing::class, function (MockInterface $mock) use ($expected_setup_intent, $expected_payment_method) {
            $mock->shouldReceive('fetchSetupIntent')->once()->with('seti_123')->andReturn($expected_setup_intent);
            $mock->shouldReceive('fetchPaymentMethod')->once()->with('pm_123')->andReturn($expected_payment_method);
        });

        $this->actingAsApiCustomer()
            ->postJson(route('api.users.payment-methods.store', compact('user')), [
                'setup_intent_id' => 'seti_123'
            ])
            ->assertCreated()
            ->assertJsonFragment([
                'brand' => 'visa',
                'last_four' => '9999',
            ]);

        $this->assertDatabaseHas(Card::class, [
            'user_id' => $user->id,
            'default' => false,
            'source_id' => 'pm_123',
        ]);
    }
}
