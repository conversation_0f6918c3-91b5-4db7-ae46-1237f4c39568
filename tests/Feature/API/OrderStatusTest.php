<?php

namespace Tests\Feature\API;

use App\Support\Enums\OrderStatus;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class OrderStatusTest extends TenantTestCase
{
    #[Test]
    public function unauthenticated_users_cannot_fetch_order_statuses(): void
    {
        $this->getJson(route('api.order-statuses.index'))
            ->assertUnauthorized();
    }

    #[Test]
    public function non_admin_users_cannot_fetch_order_statuses(): void
    {
        $this->actingAsApiCustomer()
            ->get<PERSON>son(route('api.order-statuses.index'))
            ->assertForbidden();
    }

    #[Test]
    public function admins_can_fetch_order_statuses(): void
    {
        $this->actingAsApiAdmin()
            ->getJson(route('api.order-statuses.index'))
            ->assertOk()
            ->assertJsonFragment(OrderStatus::all()->toArray());
    }

    #[Test]
    public function admins_can_fetch_filtered_order_statuses(): void
    {
        $excluded = [2, 3];

        $this->actingAsApiAdmin()
            ->getJson(route('api.order-statuses.index', compact('excluded')))
            ->assertOk()
            ->assertJsonFragment(
                OrderStatus::all()
                    ->reject(fn ($status, $key) => in_array($key, $excluded))
                    ->toArray()
            );
    }
}
