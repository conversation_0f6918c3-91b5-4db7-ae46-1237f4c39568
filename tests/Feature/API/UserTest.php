<?php

namespace Tests\Feature\API;

use App\Events\User\UserSubscribedToNewsletter;
use App\Events\User\UserWasRegistered;
use App\Models\User;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Event;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class UserTest extends TenantTestCase
{
    use WithFaker;

    #[Test]
    public function it_can_search_customers_by_email(): void
    {
        $user = User::factory()->create(['email' => '<EMAIL>']);
        $user_2 = User::factory()->create(['email' => '<EMAIL>']);

        $this->actingAsApiAdmin()
            ->getJson(route('api.users.index', ['users' => 'bc@te']))
            ->assertOk()
            ->assertJsonCount(1)
            ->assertJsonStructure([
                '*' => ['id', 'first_name', 'last_name', 'email', 'role_id', 'phone', 'street','street_2', 'city','state', 'zip']
            ])
            ->json();
    }

    #[Test]
    public function a_guest_cannot_create_new_users(): void
    {
        $this->postJson(route('api.users.store'))
            ->assertUnauthorized();
    }

    #[Test]
    public function non_admins_cannot_create_a_new_user(): void
    {
        $this->actingAsApiCustomer()
            ->postJson(route('api.users.store'))
            ->assertForbidden();
    }

    #[Test]
    public function titles_must_pass_validation_when_creating_delivery_location(): void
    {
        $this->actingAsAdmin()
            ->postJson(route('api.users.store'))
            ->assertInvalid([
                'first_name' => 'The first name field is required.',
                'last_name' => 'The last name field is required.',
                'email' => 'The email field is required.',
            ]);

        $this->actingAsAdmin()
            ->post(route('admin.users.store'), [
                'email' => 'abc@tes',
            ])
            ->assertInvalid([
                'email' => 'The email field must be a valid email address.',
            ]);

        User::factory()->create(['email' => '<EMAIL>']);

        $this->actingAsAdmin()
            ->post(route('admin.users.store'), [
                'email' => '<EMAIL>',
            ])
            ->assertInvalid([
                'email' => 'The email has already been taken.',
            ]);
    }

    #[Test]
    public function an_admin_can_create_a_new_user_without_opting_into_newsletter(): void
    {
        Event::fake([UserWasRegistered::class, UserSubscribedToNewsletter::class]);

        $attributes = [
            'first_name' =>  $this->faker->firstName(),
            'last_name' =>  $this->faker->lastName(),
            'email' =>  $this->faker->unique()->safeEmail(),
            'phone' =>  $this->faker->phoneNumber(),
        ];

        $user = $this->actingAsApiAdmin()
            ->postJson(route('api.users.store', $attributes))
            ->assertCreated()
            ->assertJsonStructure(
                ['id', 'first_name', 'last_name', 'email', 'role_id', 'phone', 'street','street_2', 'city','state', 'zip']
            )
            ->json();

        $this->assertDatabaseHas(User::class, array_merge(['id' => $user['id']], $attributes));

        Event::assertDispatched(UserWasRegistered::class, fn(UserWasRegistered $event) => $event->user->id === $user['id']);
        Event::assertNotDispatched(UserSubscribedToNewsletter::class);
    }

    #[Test]
    public function an_admin_can_create_a_new_user_with_opting_into_newsletter(): void
    {
        Event::fake([UserWasRegistered::class, UserSubscribedToNewsletter::class]);

        $attributes = [
            'first_name' =>  $this->faker->firstName(),
            'last_name' =>  $this->faker->lastName(),
            'email' =>  $this->faker->unique()->safeEmail(),
            'phone' =>  $this->faker->phoneNumber(),
            'newsletter' => true
        ];

        $user = $this->actingAsApiAdmin()
            ->postJson(route('api.users.store', $attributes))
            ->assertCreated()
            ->assertJsonStructure(
                ['id', 'first_name', 'last_name', 'email', 'role_id', 'phone', 'street','street_2', 'city','state', 'zip']
            )
            ->json();

        $this->assertDatabaseHas(User::class, array_merge(['id' => $user['id']], $attributes));

        Event::assertDispatched(UserWasRegistered::class, fn(UserWasRegistered $event) => $event->user->id === $user['id']);
        Event::assertDispatched(UserSubscribedToNewsletter::class, fn(UserSubscribedToNewsletter $event) => $event->user->id === $user['id']);
    }

}