<?php

namespace Tests\Feature\API;

use App\Models\ApiKey;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class HeartbeatTest extends TenantTestCase
{
    #[Test]
    public function it_requires_bearer_token_to_make_api_request(): void
    {
        $this->getJson(route('api.v1.heartbeat'))
            ->assertUnauthorized();
    }

    #[Test]
    public function it_returns_a_heartbeat_response(): void
    {
        $this->getJson(route('api.v1.heartbeat'), [
            'Authorization' => "Bearer {$this->generateApiKey()}"
        ])
            ->assertOk()
            ->assertExactJson([
                'success' => true,
                'status' => 200
            ]);
    }

    protected function generateApiKey()
    {
        return ApiKey::factory()->create(['name' => 'Test API Key'])
            ->generateKey();
    }
}