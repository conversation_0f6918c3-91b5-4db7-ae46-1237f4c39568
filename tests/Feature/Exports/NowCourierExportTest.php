<?php

namespace Tests\Feature\Exports;

use Illuminate\Support\Carbon;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class NowCourierExportTest extends TenantTestCase
{
    #[Test]
    public function an_unauthenticated_user_cannot_download_csv(): void
    {
        $this->put(route('admin.orders.bulk-update.export', ['export' => 'now_courier']), [
                'orders' => [1,2,3]
            ])
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function an_non_admin_user_cannot_download_csv(): void
    {
        $this->actingAsCustomer()
            ->put(route('admin.orders.bulk-update.export', ['export' => 'now_courier']), [
                'orders' => [1,2,3]
            ])
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function it_validates_the_request(): void
    {
        $this->actingAsAdmin()
            ->put(route('admin.orders.bulk-update.export', ['export' => 'now_courier']), [
                'type' => 'maybe',
                'orders' => [1,2,3]
            ])
            ->assertSessionHasErrors(['type' => 'The selected type is invalid.']);

        $this->actingAsAdmin()
            ->put(route('admin.orders.bulk-update.export', ['export' => 'now_courier']), [
                'type' => 'page',
                'orders' => []
            ])
            ->assertSessionHasErrors(['orders' => 'At least one order must be selected.']);
    }

    #[Test]
    public function it_can_generate_a_downloadable_csv_for_single_page(): void
    {
        Carbon::setTestNow(now());

        $response = $this->actingAsAdmin()
            ->put(route('admin.orders.bulk-update.export', ['export' => 'now_courier']), [
                'type' => 'page',
                'orders' => [1,2,3]
            ])
            ->assertOk();

        $response->assertHeader('Content-Disposition', "attachment; filename=\"now_courier_export.csv\"");

        Carbon::setTestNow();
    }

    #[Test]
    public function it_can_generate_a_downloadable_csv_when_type_is_not_selected(): void
    {
        Carbon::setTestNow(now());

        $response = $this->actingAsAdmin()
            ->put(route('admin.orders.bulk-update.export', ['export' => 'now_courier']), [
                'type' => null,
                'orders' => [1,2,3]
            ])
            ->assertOk();

        $response->assertHeader('Content-Disposition', "attachment; filename=\"now_courier_export.csv\"");

        Carbon::setTestNow();
    }

    #[Test]
    public function it_can_generate_a_downloadable_csv_for_all_pages(): void
    {
        Carbon::setTestNow(now());
        session(['orders-filtered' => [
            'subscription_status' => 'all',
            'confirmed' => '1',
            'order_status' => ['1','8'],
            'orderBy' => 'orders.confirmed_date',
            'sort' => 'desc',
        ]]);

        $response = $this->actingAsAdmin()
            ->put(route('admin.orders.bulk-update.export', ['export' => 'now_courier']), [
                'type' => 'all',
                'orders' => [1,2,3]
            ])
            ->assertOk();

        $response->assertHeader('Content-Disposition', "attachment; filename=\"now_courier_export.csv\"");

        Carbon::setTestNow();
    }
}