<?php

namespace Tests\Feature;

use App\Mail\OrderConfirmation;
use App\Models\Order;
use App\Models\Setting;
use App\Models\Template;
use Illuminate\Support\Facades\Mail;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class OrderConfirmationNotificationTest extends TenantTestCase
{
    #[Test]
    public function an_guest_cannot_send_an_order_confirmation_notification(): void
    {
        Mail::fake();

        $order = Order::factory()->create();

        $this->postJson(route('admin.orders.notifications.confirmed', compact('order')))
            ->assertUnauthorized();

        Mail::assertNotQueued(OrderConfirmation::class);
    }

    #[Test]
    public function an_non_admin_cannot_send_an_order_confirmation_notification(): void
    {
        Mail::fake();

        $order = Order::factory()->create();

        $this->actingAsCustomer()
            ->postJson(route('admin.orders.notifications.confirmed', compact('order')))
            ->assertForbidden();

        Mail::assertNotQueued(OrderConfirmation::class);
    }

    #[Test]
    public function an_admin_cannot_send_an_order_confirmation_notification_for_an_order_that_does_not_exist(): void
    {
        Mail::fake();

        $this->actingAsAdmin()
            ->postJson(route('admin.orders.notifications.confirmed', ['order' => 'abc']))
            ->assertNotFound();

        Mail::assertNotQueued(OrderConfirmation::class);
    }

    #[Test]
    public function an_admin_can_send_an_order_confirmation_notification(): void
    {
        Mail::fake();

        $template = Template::factory()->create();
        Setting::updateOrCreate(
            ['key' => 'email_order_confirmation_template'],
            ['value' => $template->id]
        );

        $order = Order::factory()->create();

        $this->actingAsAdmin()
            ->postJson(route('admin.orders.notifications.confirmed', compact('order')))
            ->assertOk()
            ->assertJsonFragment(["The order confirmation notification has been sent to {$order->getCustomerName()}."]);

        Mail::assertQueued(
            OrderConfirmation::class,
            function (OrderConfirmation $mail) use ($order) {
                return $mail->order_id === $order->id;
            });
    }

    #[Test]
    public function an_admin_is_rate_limited_when_sending_an_order_confirmation_notification(): void
    {
        Mail::fake();

        $template = Template::factory()->create();
        Setting::updateOrCreate(
            ['key' => 'email_order_confirmation_template'],
            ['value' => $template->id]
        );

        $order = Order::factory()->create();

        $this->actingAsAdmin();

        foreach (range(1,2) as $attempt) {
            $this->postJson(route('admin.orders.notifications.confirmed', compact('order')))
                ->assertOk()
                ->assertJsonFragment(["The order confirmation notification has been sent to {$order->getCustomerName()}."]);

            Mail::assertQueued(
                OrderConfirmation::class,
                function (OrderConfirmation $mail) use ($order) {
                    return $mail->order_id === $order->id;
                });
        }

        Mail::fake();

        $this->postJson(route('admin.orders.notifications.confirmed', compact('order')))
            ->assertStatus(429)
            ->assertJsonFragment(["Too many emails have been sent to {$order->getCustomerName()}."]);

        Mail::assertNotQueued(OrderConfirmation::class);
    }
}