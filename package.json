{"private": true, "scripts": {"rimraf": "./node_modules/rimraf/bin.js", "cleanup": "rimraf public/js/chunks", "link": "rm -f public/themes && ln -s $(pwd)/resources/themes $(pwd)/public/themes", "dev-back": "npm run development", "development": "npm run link && mix", "watch": "npm run link && mix watch", "watch-poll": "mix watch -- --watch-options-poll=1000", "hot": "mix watch --hot", "prod": "npm run production", "production": "npm run cleanup && npm run link && mix --production", "theme-watch": "cd resources/theme && mix watch", "theme-dev": "cd resources/theme && mix", "theme-prod": "cd resources/theme && mix --production", "release": "npm run prod && npm run theme-prod", "theme": "npm run theme-dev", "e2e:local-setup": "npx playwright test --project=local-setup", "e2e:test": "npx playwright test --project chromium firefox webkit", "e2e:test-ui": "npx playwright test --ui --project=chromium", "dev": "vite", "build": "vite build"}, "devDependencies": {"@alpinejs/collapse": "^3.13.10", "@alpinejs/focus": "^3.13.10", "@alpinejs/intersect": "^3.14.3", "@alpinejs/resize": "^3.14.9", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/preset-env": "^7.23.6", "@bugsnag/js": "^7.23.0", "@bugsnag/plugin-vue": "^7.22.7", "@easepick/bundle": "^1.2.1", "@googlemaps/js-api-loader": "^1.16.6", "@headlessui/vue": "^1.4.3", "@heroicons/vue": "^1.0.5", "@inertiajs/inertia": "^0.11.0", "@inertiajs/inertia-vue3": "^0.6.0", "@inertiajs/progress": "^0.1.2", "@melloware/coloris": "^0.24.0", "@playwright/test": "^1.41.1", "@popperjs/core": "^2.11.8", "@stripe/terminal-js": "^0.24.0", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/line-clamp": "^0.4.4", "@tailwindcss/nesting": "^0.0.0-insiders.565cd3e", "@tailwindcss/typography": "^0.5.9", "@types/node": "^20.11.14", "@vitejs/plugin-vue": "^5.0.5", "@vue-leaflet/vue-leaflet": "^0.10.1", "@vueup/vue-quill": "^1.0.0-beta.8", "alpinejs": "^3.12.0", "autoprefixer": "^10.4.19", "axios": "^0.25", "balloon-css": "^1.0.4", "browser-sync": "^3.0.4", "browser-sync-webpack-plugin": "^2.2.2", "chart.js": "^4.4.2", "cross-env": "^5.2.1", "dayjs": "^1.11.1", "dotenv": "^16.4.5", "dropzone": "^4.3.0", "eslint-plugin-vue": "^8.5.0", "hex-rgb": "^5.0.0", "jquery": "^3.1.1", "js-confetti": "^0.12.0", "laravel-mix": "^6.0.0", "laravel-vite-plugin": "^1.0.4", "leaflet": "^1.9.4", "leaflet.markercluster": "^1.1.0", "less": "^3.11.1", "less-loader": "^10.0.0", "livewire-sortable": "^1.0.0", "lodash": "^4.17.21", "mitt": "^3.0.0", "moment": "^2.30.1", "pikaday": "^1.8.2", "pinia": "^2.0.13", "postcss": "^8.4.21", "postcss-import": "^16.1.0", "postcss-nested": "^6.0.1", "postcss-prefix-selector": "^1.16.0", "quill": "^1.3.6", "rimraf": "^3.0.2", "rough-notation": "^0.5.1", "sortable": "^2.0.0", "sortablejs": "^1.15.0", "swiper": "^11.1.14", "tailwindcss": "^3.4.4", "uuid": "^8.3.2", "vite": "^7.0.0", "vue": "^3.2.29", "vue-currency-input": "^2.4.0", "vue-loader": "^16.8.3", "vue-router": "^4.0.13", "vue-select": "^4.0.0-beta.3", "vuedraggable": "^4.1.0", "webpack": "^5.76.0", "webpack-bugsnag-plugins": "^1.8.0", "webpack-cli": "^4.8.0"}, "dependencies": {"@babel/core": "^7.18.10", "vue-google-autocomplete": "^1.1.4"}, "type": "module"}