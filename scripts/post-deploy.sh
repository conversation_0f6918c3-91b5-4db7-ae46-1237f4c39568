#!/bin/bash

DIR=/var/www/vhosts/sev0619-02-19.reusser.design

cd $DIR

# Laravel Folders
rm -rf bootstrap/cache/
rm -rf storage/logs

# Laravel
sudo mkdir -p storage/logs
sudo mkdir -p bootstrap/cache

# All permissions
sudo chown -R ubuntu:www-data {storage,public,bootstrap}/
sudo find {storage,public,bootstrap}/ -type d -exec chmod 775 {} \;
sudo find {storage,public,bootstrap}/ -type f -exec chmod 664 {} \;

# Symlinks
rm -f public/themes
ln -s $(pwd)/resources/themes $(pwd)/public/themes
rm -f resources/assets/less/graze-css
ln -s $(pwd)/../graze-css/src/graze-css $(pwd)/resources/assets/less/graze-css

# Clear config and restart supervisor
php artisan cache:clear
php artisan config:clear
php artisan view:clear

php artisan package:discover
# php artisan route:cache
php artisan config:cache

php artisan queue:restart
php artisan grazecart:migrate

echo "Post Deploy Script Ran Successfully"
